import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../validators/password_validator.dart';
import '../constants/app_colors.dart';

/// ويدجت لعرض قوة كلمة المرور مع مؤشر بصري ونصائح
class PasswordStrengthIndicator extends StatelessWidget {
  final String password;
  final bool showDetails;
  final bool showSuggestions;
  final EdgeInsets? padding;

  const PasswordStrengthIndicator({
    super.key,
    required this.password,
    this.showDetails = true,
    this.showSuggestions = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (password.isEmpty) {
      return const SizedBox.shrink();
    }

    final result = PasswordValidator.validatePassword(password);
    final strength = PasswordValidator.getPasswordStrength(password);
    final percentage = PasswordValidator.getPasswordStrengthPercentage(password);

    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // مؤشر القوة
          _buildStrengthIndicator(strength, percentage),
          
          const SizedBox(height: 8),
          
          // نص القوة
          _buildStrengthText(strength, result.message),
          
          if (showDetails && result.issues.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildIssuesList(result.issues),
          ],
          
          if (showSuggestions && result.suggestions.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildSuggestionsList(result.suggestions),
          ],
        ],
      ),
    );
  }

  /// بناء مؤشر القوة البصري
  Widget _buildStrengthIndicator(PasswordStrength strength, int percentage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'قوة كلمة المرور:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '$percentage%',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(strength.colorValue),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // شريط التقدم
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: AppColors.backgroundGray,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                Color(strength.colorValue),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 4),
        
        // مؤشرات القوة
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildStrengthDot(PasswordStrength.veryWeak, strength),
            _buildStrengthDot(PasswordStrength.weak, strength),
            _buildStrengthDot(PasswordStrength.fair, strength),
            _buildStrengthDot(PasswordStrength.good, strength),
            _buildStrengthDot(PasswordStrength.strong, strength),
          ],
        ),
      ],
    );
  }

  /// بناء نقطة مؤشر القوة
  Widget _buildStrengthDot(PasswordStrength level, PasswordStrength current) {
    final isActive = current.index >= level.index;
    
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isActive 
            ? Color(level.colorValue)
            : AppColors.backgroundGray,
        border: Border.all(
          color: isActive 
              ? Color(level.colorValue)
              : AppColors.textGray.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
    );
  }

  /// بناء نص القوة
  Widget _buildStrengthText(PasswordStrength strength, String message) {
    return Row(
      children: [
        Icon(
          _getStrengthIcon(strength),
          size: 16,
          color: Color(strength.colorValue),
        ),
        const SizedBox(width: 6),
        Text(
          strength.label,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(strength.colorValue),
          ),
        ),
        if (message.isNotEmpty) ...[
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '- $message',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: AppColors.textGray,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// الحصول على أيقونة القوة
  IconData _getStrengthIcon(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.veryWeak:
        return Icons.error;
      case PasswordStrength.weak:
        return Icons.warning;
      case PasswordStrength.fair:
        return Icons.info;
      case PasswordStrength.good:
        return Icons.check_circle_outline;
      case PasswordStrength.strong:
        return Icons.verified;
    }
  }

  /// بناء قائمة المشاكل
  Widget _buildIssuesList(List<String> issues) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                size: 16,
                color: Colors.red,
              ),
              const SizedBox(width: 6),
              Text(
                'مشاكل في كلمة المرور:',
                style: GoogleFonts.cairo(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...issues.map((issue) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '• ',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.red,
                  ),
                ),
                Expanded(
                  child: Text(
                    issue,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.red.withValues(alpha: 0.8),
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// بناء قائمة الاقتراحات
  Widget _buildSuggestionsList(List<String> suggestions) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: Colors.blue,
              ),
              const SizedBox(width: 6),
              Text(
                'اقتراحات للتحسين:',
                style: GoogleFonts.cairo(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...suggestions.map((suggestion) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '• ',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.blue,
                  ),
                ),
                Expanded(
                  child: Text(
                    suggestion,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.blue.withValues(alpha: 0.8),
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }
}

/// ويدجت مبسط لعرض قوة كلمة المرور فقط
class SimplePasswordStrengthIndicator extends StatelessWidget {
  final String password;
  final double height;

  const SimplePasswordStrengthIndicator({
    super.key,
    required this.password,
    this.height = 4,
  });

  @override
  Widget build(BuildContext context) {
    if (password.isEmpty) {
      return Container(
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(height / 2),
          color: AppColors.backgroundGray,
        ),
      );
    }

    final strength = PasswordValidator.getPasswordStrength(password);
    final percentage = PasswordValidator.getPasswordStrengthPercentage(password);

    return Container(
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(height / 2),
        color: AppColors.backgroundGray,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(height / 2),
        child: LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: Colors.transparent,
          valueColor: AlwaysStoppedAnimation<Color>(
            Color(strength.colorValue),
          ),
        ),
      ),
    );
  }
}
