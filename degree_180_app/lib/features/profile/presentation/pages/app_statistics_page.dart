import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/services/cache_service.dart';
import '../../../../core/services/connectivity_service.dart';
import '../../../../core/services/error_service.dart';

class AppStatisticsPage extends StatefulWidget {
  const AppStatisticsPage({super.key});

  @override
  State<AppStatisticsPage> createState() => _AppStatisticsPageState();
}

class _AppStatisticsPageState extends State<AppStatisticsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  final AnalyticsService _analytics = AnalyticsService();
  final CacheService _cache = CacheService();
  final ConnectivityService _connectivity = ConnectivityService();
  final ErrorService _errorService = ErrorService();

  // Statistics data
  Map<String, dynamic> _usageStats = {};
  Map<String, dynamic> _performanceStats = {};
  Map<String, dynamic> _errorStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadStatistics();
    _analytics.logScreenView('App Statistics', 'AppStatisticsPage');
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    setState(() => _isLoading = true);
    
    try {
      // Load usage statistics
      _usageStats = {
        'totalBookings': 45,
        'completedBookings': 38,
        'cancelledBookings': 7,
        'totalSpent': 425.5,
        'averageBookingValue': 11.2,
        'favoriteProviders': 12,
        'searchesPerformed': 156,
        'reviewsWritten': 23,
        'photosUploaded': 67,
        'appOpens': 234,
        'sessionDuration': 18.5, // minutes
        'lastActiveDate': DateTime.now().subtract(const Duration(hours: 2)),
      };

      // Load performance statistics
      final cacheSize = await _cache.getCacheSize();
      _performanceStats = {
        'cacheSize': cacheSize,
        'averageLoadTime': 1.2, // seconds
        'networkRequests': 1456,
        'successfulRequests': 1398,
        'failedRequests': 58,
        'averageResponseTime': 850, // milliseconds
        'dataUsage': 45.6, // MB
        'batteryUsage': 3.2, // percentage
        'memoryUsage': 128.5, // MB
        'crashCount': 2,
      };

      // Load error statistics
      final errorHistory = _errorService.errorHistory;
      final errorStats = _errorService.getErrorStatistics(
        period: const Duration(days: 30),
      );
      
      _errorStats = {
        'totalErrors': errorHistory.length,
        'errorsByType': errorStats,
        'criticalErrors': _errorService.getCriticalErrors(
          period: const Duration(days: 30),
        ).length,
        'lastError': errorHistory.isNotEmpty ? errorHistory.last : null,
        'errorTrend': _calculateErrorTrend(),
      };

    } catch (e) {
      _errorService.reportError(e, context: {'page': 'app_statistics'});
    } finally {
      setState(() => _isLoading = false);
    }
  }

  List<double> _calculateErrorTrend() {
    // Mock error trend data for the last 7 days
    return [2, 1, 3, 0, 1, 2, 1];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Statistics'),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.textWhite,
          unselectedLabelColor: AppColors.textWhite.withValues(alpha: 0.7),
          indicatorColor: AppColors.textWhite,
          tabs: const [
            Tab(text: 'Usage'),
            Tab(text: 'Performance'),
            Tab(text: 'Errors'),
            Tab(text: 'Network'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildUsageTab(),
                _buildPerformanceTab(),
                _buildErrorsTab(),
                _buildNetworkTab(),
              ],
            ),
    );
  }

  Widget _buildUsageTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Overview Cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Bookings',
                  _usageStats['totalBookings'].toString(),
                  Icons.event,
                  AppColors.primaryPurple,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Total Spent',
                  '${_usageStats['totalSpent']} KWD',
                  Icons.attach_money,
                  AppColors.success,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Reviews Written',
                  _usageStats['reviewsWritten'].toString(),
                  Icons.star,
                  AppColors.starYellow,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'App Opens',
                  _usageStats['appOpens'].toString(),
                  Icons.open_in_new,
                  AppColors.info,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Booking Status Chart
          _buildBookingStatusChart(),

          const SizedBox(height: 24),

          // Activity Timeline
          _buildActivityTimeline(),

          const SizedBox(height: 24),

          // Detailed Statistics
          _buildDetailedUsageStats(),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Performance Overview
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Cache Size',
                  _formatBytes(_performanceStats['cacheSize']),
                  Icons.storage,
                  AppColors.warning,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Memory Usage',
                  '${_performanceStats['memoryUsage']} MB',
                  Icons.memory,
                  AppColors.error,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Avg Load Time',
                  '${_performanceStats['averageLoadTime']}s',
                  Icons.speed,
                  AppColors.success,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Data Usage',
                  '${_performanceStats['dataUsage']} MB',
                  Icons.data_usage,
                  AppColors.info,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Performance Chart
          _buildPerformanceChart(),

          const SizedBox(height: 24),

          // Network Statistics
          _buildNetworkStats(),
        ],
      ),
    );
  }

  Widget _buildErrorsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Error Overview
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Errors',
                  _errorStats['totalErrors'].toString(),
                  Icons.error,
                  AppColors.error,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Critical Errors',
                  _errorStats['criticalErrors'].toString(),
                  Icons.warning,
                  AppColors.warning,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Error Types Chart
          _buildErrorTypesChart(),

          const SizedBox(height: 24),

          // Error Trend
          _buildErrorTrendChart(),

          const SizedBox(height: 24),

          // Recent Errors
          _buildRecentErrors(),
        ],
      ),
    );
  }

  Widget _buildNetworkTab() {
    final connectionStatus = _connectivity.currentStatus;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Connection Status
          _buildConnectionStatusCard(connectionStatus),

          const SizedBox(height: 24),

          // Network Statistics
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Successful Requests',
                  _performanceStats['successfulRequests'].toString(),
                  Icons.check_circle,
                  AppColors.success,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Failed Requests',
                  _performanceStats['failedRequests'].toString(),
                  Icons.error,
                  AppColors.error,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Avg Response Time',
                  '${_performanceStats['averageResponseTime']}ms',
                  Icons.timer,
                  AppColors.info,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Total Requests',
                  _performanceStats['networkRequests'].toString(),
                  Icons.sync,
                  AppColors.primaryPurple,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Request Success Rate Chart
          _buildRequestSuccessChart(),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingStatusChart() {
    final completed = _usageStats['completedBookings'];
    final cancelled = _usageStats['cancelledBookings'];
    final total = completed + cancelled;

    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Status Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: completed.toDouble(),
                      title: '${(completed / total * 100).round()}%',
                      color: AppColors.success,
                      radius: 60,
                    ),
                    PieChartSectionData(
                      value: cancelled.toDouble(),
                      title: '${(cancelled / total * 100).round()}%',
                      color: AppColors.error,
                      radius: 60,
                    ),
                  ],
                  centerSpaceRadius: 40,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildLegendItem('Completed', AppColors.success),
                _buildLegendItem('Cancelled', AppColors.error),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(label),
      ],
    );
  }

  Widget _buildActivityTimeline() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildTimelineItem('Booking completed', '2 hours ago', Icons.check_circle, AppColors.success),
            _buildTimelineItem('Review written', '1 day ago', Icons.star, AppColors.starYellow),
            _buildTimelineItem('Search performed', '2 days ago', Icons.search, AppColors.info),
            _buildTimelineItem('Profile updated', '1 week ago', Icons.person, AppColors.primaryPurple),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
                Text(time, style: TextStyle(color: AppColors.textLight, fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedUsageStats() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Detailed Statistics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('Average Booking Value', '${_usageStats['averageBookingValue']} KWD'),
            _buildStatRow('Favorite Providers', _usageStats['favoriteProviders'].toString()),
            _buildStatRow('Searches Performed', _usageStats['searchesPerformed'].toString()),
            _buildStatRow('Photos Uploaded', _usageStats['photosUploaded'].toString()),
            _buildStatRow('Average Session Duration', '${_usageStats['sessionDuration']} minutes'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  Widget _buildPerformanceChart() {
    // Mock performance data
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Trend (Last 7 Days)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: const FlTitlesData(show: false),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: [
                        const FlSpot(0, 1.5),
                        const FlSpot(1, 1.2),
                        const FlSpot(2, 1.8),
                        const FlSpot(3, 1.1),
                        const FlSpot(4, 1.4),
                        const FlSpot(5, 1.0),
                        const FlSpot(6, 1.2),
                      ],
                      isCurved: true,
                      color: AppColors.primaryPurple,
                      barWidth: 3,
                      dotData: const FlDotData(show: false),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkStats() {
    final successRate = (_performanceStats['successfulRequests'] / 
                        _performanceStats['networkRequests'] * 100).round();
    
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Network Performance',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('Success Rate', '$successRate%'),
            _buildStatRow('Total Requests', _performanceStats['networkRequests'].toString()),
            _buildStatRow('Failed Requests', _performanceStats['failedRequests'].toString()),
            _buildStatRow('Average Response Time', '${_performanceStats['averageResponseTime']}ms'),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorTypesChart() {
    final errorsByType = _errorStats['errorsByType'] as Map<dynamic, int>;
    
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Error Types Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ...errorsByType.entries.map((entry) => 
              _buildStatRow(entry.key.toString(), entry.value.toString())),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorTrendChart() {
    final trendData = _errorStats['errorTrend'] as List<double>;
    
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Error Trend (Last 7 Days)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 150,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: const FlTitlesData(show: false),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: trendData.asMap().entries.map((entry) => 
                        FlSpot(entry.key.toDouble(), entry.value)).toList(),
                      isCurved: true,
                      color: AppColors.error,
                      barWidth: 3,
                      dotData: const FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentErrors() {
    final recentErrors = _errorService.errorHistory.take(5).toList();
    
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Errors',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (recentErrors.isEmpty)
              const Text('No recent errors')
            else
              ...recentErrors.map((error) => 
                _buildErrorItem(error.message, error.timestamp)),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorItem(String message, DateTime timestamp) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppColors.error, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(message, style: const TextStyle(fontSize: 12)),
                Text(
                  _formatTimestamp(timestamp),
                  style: TextStyle(color: AppColors.textLight, fontSize: 10),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConnectionStatusCard(dynamic connectionStatus) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Connection',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('Type', connectionStatus.type.toString()),
            _buildStatRow('Status', connectionStatus.isConnected ? 'Connected' : 'Disconnected'),
            _buildStatRow('Quality', connectionStatus.quality.toString()),
            if (connectionStatus.speed != null)
              _buildStatRow('Speed', '${connectionStatus.speed} Mbps'),
            _buildStatRow('Latency', '${connectionStatus.latency} ms'),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestSuccessChart() {
    final successRate = _performanceStats['successfulRequests'] / 
                       _performanceStats['networkRequests'];
    
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Request Success Rate',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Center(
              child: SizedBox(
                width: 150,
                height: 150,
                child: CircularProgressIndicator(
                  value: successRate,
                  strokeWidth: 10,
                  backgroundColor: AppColors.borderGray,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.success),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: Text(
                '${(successRate * 100).round()}%',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) return 'Just now';
    if (difference.inMinutes < 60) return '${difference.inMinutes}m ago';
    if (difference.inHours < 24) return '${difference.inHours}h ago';
    return '${difference.inDays}d ago';
  }
}
