import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/theme_manager.dart';

/// مؤشر تقدم خطوات المصادقة
class AuthStepIndicator extends StatefulWidget {
  final int currentStep;
  final int totalSteps;
  final int completedSteps;

  const AuthStepIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.completedSteps,
  });

  @override
  State<AuthStepIndicator> createState() => _AuthStepIndicatorState();
}

class _AuthStepIndicatorState extends State<AuthStepIndicator>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _progressController.forward();
  }

  @override
  void didUpdateWidget(AuthStepIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentStep != widget.currentStep) {
      _progressController.reset();
      _progressController.forward();
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط التقدم
        _buildProgressBar(),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        // نص التقدم
        _buildProgressText(),
      ],
    );
  }

  Widget _buildProgressBar() {
    return Container(
      height: 6,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: AppColors.textWhite.withOpacity(0.3),
      ),
      child: AnimatedBuilder(
        animation: _progressAnimation,
        builder: (context, child) {
          final progress = (widget.currentStep / (widget.totalSteps - 1)) * _progressAnimation.value;
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(3),
              gradient: LinearGradient(
                colors: [
                  AppColors.textWhite,
                  AppColors.textWhite.withOpacity(0.8),
                ],
              ),
            ),
            width: MediaQuery.of(context).size.width * 0.8 * progress,
          );
        },
      ),
    );
  }

  Widget _buildProgressText() {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _progressAnimation.value,
          child: Text(
            'الخطوة ${widget.currentStep + 1} من ${widget.totalSteps}',
            style: GoogleFonts.cairo(
              fontSize: AppConstants.fontSizeSmall,
              color: AppColors.textWhite.withOpacity(0.9),
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      },
    );
  }
}

/// مؤشر خطوات دائري متقدم
class CircularStepIndicator extends StatefulWidget {
  final int currentStep;
  final int totalSteps;
  final List<String> stepTitles;

  const CircularStepIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepTitles,
  });

  @override
  State<CircularStepIndicator> createState() => _CircularStepIndicatorState();
}

class _CircularStepIndicatorState extends State<CircularStepIndicator>
    with TickerProviderStateMixin {
  late List<AnimationController> _stepControllers;
  late List<Animation<double>> _stepAnimations;

  @override
  void initState() {
    super.initState();
    _initializeStepAnimations();
  }

  void _initializeStepAnimations() {
    _stepControllers = List.generate(
      widget.totalSteps,
      (index) => AnimationController(
        duration: Duration(milliseconds: 300 + (index * 100)),
        vsync: this,
      ),
    );

    _stepAnimations = _stepControllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.elasticOut,
      ));
    }).toList();

    // تشغيل الانيميشن للخطوات المكتملة
    for (int i = 0; i <= widget.currentStep; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _stepControllers[i].forward();
        }
      });
    }
  }

  @override
  void didUpdateWidget(CircularStepIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentStep != widget.currentStep) {
      _updateStepAnimations();
    }
  }

  void _updateStepAnimations() {
    for (int i = 0; i < widget.totalSteps; i++) {
      if (i <= widget.currentStep) {
        _stepControllers[i].forward();
      } else {
        _stepControllers[i].reverse();
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _stepControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(widget.totalSteps, (index) {
        return _buildStepCircle(index);
      }),
    );
  }

  Widget _buildStepCircle(int index) {
    final isCompleted = index < widget.currentStep;
    final isCurrent = index == widget.currentStep;
    final isUpcoming = index > widget.currentStep;

    return AnimatedBuilder(
      animation: _stepAnimations[index],
      builder: (context, child) {
        return Column(
          children: [
            // الدائرة
            Container(
              width: 40 * _stepAnimations[index].value,
              height: 40 * _stepAnimations[index].value,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isCompleted
                    ? ThemeManager.currentColors.primary
                    : isCurrent
                        ? ThemeManager.currentColors.secondary
                        : AppColors.textWhite.withOpacity(0.3),
                border: Border.all(
                  color: AppColors.textWhite,
                  width: 2,
                ),
                boxShadow: isCurrent
                    ? [
                        BoxShadow(
                          color: ThemeManager.currentColors.secondary.withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ]
                    : null,
              ),
              child: Center(
                child: isCompleted
                    ? Icon(
                        Icons.check,
                        color: AppColors.textWhite,
                        size: 20 * _stepAnimations[index].value,
                      )
                    : Text(
                        '${index + 1}',
                        style: GoogleFonts.cairo(
                          color: AppColors.textWhite,
                          fontWeight: FontWeight.bold,
                          fontSize: 16 * _stepAnimations[index].value,
                        ),
                      ),
              ),
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            // عنوان الخطوة
            if (index < widget.stepTitles.length)
              Opacity(
                opacity: _stepAnimations[index].value,
                child: Text(
                  widget.stepTitles[index],
                  style: GoogleFonts.cairo(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppColors.textWhite.withOpacity(0.9),
                    fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        );
      },
    );
  }
}
