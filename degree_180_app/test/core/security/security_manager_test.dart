import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:degree_180_app/core/security/security_manager.dart';

void main() {
  group('SecurityManager', () {
    late SecurityManager securityManager;

    setUp(() {
      SharedPreferences.setMockInitialValues({});
      securityManager = SecurityManager.instance;
    });

    group('Data Encryption/Decryption', () {
      test('should encrypt and decrypt data correctly', () async {
        // Arrange
        const testData = 'sensitive information';

        // Act
        final encryptedData = await securityManager.encryptData(testData);
        final decryptedData = await securityManager.decryptData(encryptedData);

        // Assert
        expect(encryptedData, isNot(equals(testData)));
        expect(decryptedData, equals(testData));
      });

      test('should handle empty string encryption/decryption', () async {
        // Arrange
        const testData = '';

        // Act
        final encryptedData = await securityManager.encryptData(testData);
        final decryptedData = await securityManager.decryptData(encryptedData);

        // Assert
        expect(decryptedData, equals(testData));
      });

      test('should handle special characters in encryption/decryption', () async {
        // Arrange
        const testData = 'Special chars: !@#\$%^&*()_+{}|:"<>?[]\\;\',./ 🔒🔑';

        // Act
        final encryptedData = await securityManager.encryptData(testData);
        final decryptedData = await securityManager.decryptData(encryptedData);

        // Assert
        expect(decryptedData, equals(testData));
      });
    });

    group('Secure Data Storage', () {
      test('should store and retrieve secure data correctly', () async {
        // Arrange
        const key = 'test_key';
        const data = 'secure data';

        // Act
        final stored = await securityManager.storeSecureData(key, data);
        final retrieved = await securityManager.getSecureData(key);

        // Assert
        expect(stored, true);
        expect(retrieved, equals(data));
      });

      test('should return null for non-existent key', () async {
        // Arrange
        const key = 'non_existent_key';

        // Act
        final retrieved = await securityManager.getSecureData(key);

        // Assert
        expect(retrieved, isNull);
      });

      test('should remove secure data correctly', () async {
        // Arrange
        const key = 'test_key';
        const data = 'secure data';

        // Act
        await securityManager.storeSecureData(key, data);
        final removed = await securityManager.removeSecureData(key);
        final retrieved = await securityManager.getSecureData(key);

        // Assert
        expect(removed, true);
        expect(retrieved, isNull);
      });

      test('should clear all secure data', () async {
        // Arrange
        const key1 = 'test_key_1';
        const key2 = 'test_key_2';
        const data1 = 'secure data 1';
        const data2 = 'secure data 2';

        // Act
        await securityManager.storeSecureData(key1, data1);
        await securityManager.storeSecureData(key2, data2);
        await securityManager.clearAllSecureData();
        final retrieved1 = await securityManager.getSecureData(key1);
        final retrieved2 = await securityManager.getSecureData(key2);

        // Assert
        expect(retrieved1, isNull);
        expect(retrieved2, isNull);
      });
    });

    group('Password Hashing', () {
      test('should hash password consistently', () async {
        // Arrange
        const password = 'testPassword123';

        // Act
        final hash1 = await securityManager.hashPassword(password);
        final hash2 = await securityManager.hashPassword(password);

        // Assert
        expect(hash1, equals(hash2));
        expect(hash1, isNot(equals(password)));
      });

      test('should verify password correctly', () async {
        // Arrange
        const password = 'testPassword123';
        const wrongPassword = 'wrongPassword';

        // Act
        final hashedPassword = await securityManager.hashPassword(password);
        final isCorrect = await securityManager.verifyPassword(password, hashedPassword);
        final isWrong = await securityManager.verifyPassword(wrongPassword, hashedPassword);

        // Assert
        expect(isCorrect, true);
        expect(isWrong, false);
      });

      test('should handle empty password', () async {
        // Arrange
        const password = '';

        // Act
        final hashedPassword = await securityManager.hashPassword(password);
        final isCorrect = await securityManager.verifyPassword(password, hashedPassword);

        // Assert
        expect(isCorrect, true);
        expect(hashedPassword, isNot(equals(password)));
      });
    });

    group('Login Attempt Tracking', () {
      test('should allow login attempts initially', () async {
        // Arrange
        const identifier = '<EMAIL>';

        // Act
        final canAttempt = await securityManager.canAttemptLogin(identifier);

        // Assert
        expect(canAttempt, true);
      });

      test('should track failed login attempts', () async {
        // Arrange
        const identifier = '<EMAIL>';

        // Act
        await securityManager.recordFailedLogin(identifier);
        final canAttempt = await securityManager.canAttemptLogin(identifier);

        // Assert
        expect(canAttempt, true); // Should still allow attempts until max is reached
      });

      test('should lock out after max failed attempts', () async {
        // Arrange
        const identifier = '<EMAIL>';

        // Act
        // Record 5 failed attempts (max limit)
        for (int i = 0; i < 5; i++) {
          await securityManager.recordFailedLogin(identifier);
        }
        final canAttempt = await securityManager.canAttemptLogin(identifier);

        // Assert
        expect(canAttempt, false);
      });

      test('should reset attempts after successful login', () async {
        // Arrange
        const identifier = '<EMAIL>';

        // Act
        // Record some failed attempts
        for (int i = 0; i < 3; i++) {
          await securityManager.recordFailedLogin(identifier);
        }
        await securityManager.recordSuccessfulLogin(identifier);
        final canAttempt = await securityManager.canAttemptLogin(identifier);

        // Assert
        expect(canAttempt, true);
      });

      test('should return remaining lockout time when locked out', () async {
        // Arrange
        const identifier = '<EMAIL>';

        // Act
        // Lock out the user
        for (int i = 0; i < 5; i++) {
          await securityManager.recordFailedLogin(identifier);
        }
        final remainingTime = await securityManager.getRemainingLockoutTime(identifier);

        // Assert
        expect(remainingTime, isNotNull);
        expect(remainingTime!.inMinutes, greaterThan(0));
      });

      test('should return null lockout time when not locked out', () async {
        // Arrange
        const identifier = '<EMAIL>';

        // Act
        final remainingTime = await securityManager.getRemainingLockoutTime(identifier);

        // Assert
        expect(remainingTime, isNull);
      });
    });

    group('Input Validation', () {
      test('should validate normal input as valid', () {
        // Arrange
        const input = 'normal text input';

        // Act
        final isValid = securityManager.isValidInput(input);

        // Assert
        expect(isValid, true);
      });

      test('should reject SQL injection patterns', () {
        // Arrange
        const sqlInjectionInputs = [
          "'; DROP TABLE users; --",
          "1' OR '1'='1",
          "admin'--",
          "1; DELETE FROM users",
          "UNION SELECT * FROM passwords",
        ];

        // Act & Assert
        for (final input in sqlInjectionInputs) {
          final isValid = securityManager.isValidInput(input);
          expect(isValid, false, reason: 'Should reject SQL injection: $input');
        }
      });

      test('should reject XSS patterns', () {
        // Arrange
        const xssInputs = [
          '<script>alert("xss")</script>',
          'javascript:alert("xss")',
          '<img src="x" onerror="alert(1)">',
          'onclick="alert(1)"',
          '<SCRIPT>alert("XSS")</SCRIPT>',
        ];

        // Act & Assert
        for (final input in xssInputs) {
          final isValid = securityManager.isValidInput(input);
          expect(isValid, false, reason: 'Should reject XSS: $input');
        }
      });

      test('should reject input exceeding max length', () {
        // Arrange
        final longInput = 'a' * 1001; // Default max length is 1000

        // Act
        final isValid = securityManager.isValidInput(longInput);

        // Assert
        expect(isValid, false);
      });

      test('should reject empty input', () {
        // Arrange
        const input = '';

        // Act
        final isValid = securityManager.isValidInput(input);

        // Assert
        expect(isValid, false);
      });

      test('should respect custom max length', () {
        // Arrange
        const input = 'test';

        // Act
        final isValidShort = securityManager.isValidInput(input, maxLength: 3);
        final isValidLong = securityManager.isValidInput(input, maxLength: 5);

        // Assert
        expect(isValidShort, false);
        expect(isValidLong, true);
      });
    });

    group('Input Sanitization', () {
      test('should remove dangerous characters', () {
        // Arrange
        const input = 'Hello <script>alert("xss")</script> World';

        // Act
        final sanitized = securityManager.sanitizeInput(input);

        // Assert
        expect(sanitized, 'Hello alert("xss") World');
        expect(sanitized, isNot(contains('<')));
        expect(sanitized, isNot(contains('>')));
        expect(sanitized, isNot(contains('script')));
      });

      test('should remove javascript patterns', () {
        // Arrange
        const input = 'javascript:alert("test")';

        // Act
        final sanitized = securityManager.sanitizeInput(input);

        // Assert
        expect(sanitized, 'alert("test")');
        expect(sanitized, isNot(contains('javascript:')));
      });

      test('should remove event handlers', () {
        // Arrange
        const input = 'onclick="malicious()" onload="bad()"';

        // Act
        final sanitized = securityManager.sanitizeInput(input);

        // Assert
        expect(sanitized, '"malicious()" "bad()"');
        expect(sanitized, isNot(contains('onclick=')));
        expect(sanitized, isNot(contains('onload=')));
      });

      test('should trim whitespace', () {
        // Arrange
        const input = '  test input  ';

        // Act
        final sanitized = securityManager.sanitizeInput(input);

        // Assert
        expect(sanitized, 'test input');
      });
    });

    group('Token Generation', () {
      test('should generate secure token with default length', () {
        // Act
        final token = securityManager.generateSecureToken();

        // Assert
        expect(token.length, 32);
        expect(token, matches(RegExp(r'^[a-zA-Z0-9]+$')));
      });

      test('should generate secure token with custom length', () {
        // Arrange
        const customLength = 16;

        // Act
        final token = securityManager.generateSecureToken(length: customLength);

        // Assert
        expect(token.length, customLength);
        expect(token, matches(RegExp(r'^[a-zA-Z0-9]+$')));
      });

      test('should generate different tokens each time', () {
        // Act
        final token1 = securityManager.generateSecureToken();
        final token2 = securityManager.generateSecureToken();

        // Assert
        expect(token1, isNot(equals(token2)));
      });
    });

    group('Environment Detection', () {
      test('should detect debug mode', () {
        // Act
        final isDebug = securityManager.isDebugMode;

        // Assert
        expect(isDebug, isA<bool>());
      });

      test('should detect potential emulator', () {
        // Act
        final isPotentiallyEmulator = securityManager.isPotentiallyEmulator;

        // Assert
        expect(isPotentiallyEmulator, isA<bool>());
      });
    });
  });
}
