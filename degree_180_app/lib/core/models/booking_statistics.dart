import 'package:equatable/equatable.dart';

/// نموذج إحصائيات الحجز
class BookingStatistics extends Equatable {
  final int totalBookings;
  final int confirmedBookings;
  final int pendingBookings;
  final int cancelledBookings;
  final int completedBookings;
  final double totalRevenue;
  final double averageBookingValue;
  final Map<String, int> bookingsByService;
  final Map<String, int> bookingsByMonth;
  final Map<String, double> revenueByService;
  final Map<String, double> revenueByMonth;
  final double cancellationRate;
  final double completionRate;
  final int totalCustomers;
  final int returningCustomers;
  final double customerRetentionRate;

  const BookingStatistics({
    required this.totalBookings,
    required this.confirmedBookings,
    required this.pendingBookings,
    required this.cancelledBookings,
    required this.completedBookings,
    required this.totalRevenue,
    required this.averageBookingValue,
    required this.bookingsByService,
    required this.bookingsByMonth,
    required this.revenueByService,
    required this.revenueByMonth,
    required this.cancellationRate,
    required this.completionRate,
    required this.totalCustomers,
    required this.returningCustomers,
    required this.customerRetentionRate,
  });

  /// إنشاء من Map
  factory BookingStatistics.fromMap(Map<String, dynamic> map) {
    return BookingStatistics(
      totalBookings: map['totalBookings']?.toInt() ?? 0,
      confirmedBookings: map['confirmedBookings']?.toInt() ?? 0,
      pendingBookings: map['pendingBookings']?.toInt() ?? 0,
      cancelledBookings: map['cancelledBookings']?.toInt() ?? 0,
      completedBookings: map['completedBookings']?.toInt() ?? 0,
      totalRevenue: map['totalRevenue']?.toDouble() ?? 0.0,
      averageBookingValue: map['averageBookingValue']?.toDouble() ?? 0.0,
      bookingsByService: Map<String, int>.from(map['bookingsByService'] ?? {}),
      bookingsByMonth: Map<String, int>.from(map['bookingsByMonth'] ?? {}),
      revenueByService: Map<String, double>.from(map['revenueByService'] ?? {}),
      revenueByMonth: Map<String, double>.from(map['revenueByMonth'] ?? {}),
      cancellationRate: map['cancellationRate']?.toDouble() ?? 0.0,
      completionRate: map['completionRate']?.toDouble() ?? 0.0,
      totalCustomers: map['totalCustomers']?.toInt() ?? 0,
      returningCustomers: map['returningCustomers']?.toInt() ?? 0,
      customerRetentionRate: map['customerRetentionRate']?.toDouble() ?? 0.0,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'totalBookings': totalBookings,
      'confirmedBookings': confirmedBookings,
      'pendingBookings': pendingBookings,
      'cancelledBookings': cancelledBookings,
      'completedBookings': completedBookings,
      'totalRevenue': totalRevenue,
      'averageBookingValue': averageBookingValue,
      'bookingsByService': bookingsByService,
      'bookingsByMonth': bookingsByMonth,
      'revenueByService': revenueByService,
      'revenueByMonth': revenueByMonth,
      'cancellationRate': cancellationRate,
      'completionRate': completionRate,
      'totalCustomers': totalCustomers,
      'returningCustomers': returningCustomers,
      'customerRetentionRate': customerRetentionRate,
    };
  }

  /// نسخة محدثة
  BookingStatistics copyWith({
    int? totalBookings,
    int? confirmedBookings,
    int? pendingBookings,
    int? cancelledBookings,
    int? completedBookings,
    double? totalRevenue,
    double? averageBookingValue,
    Map<String, int>? bookingsByService,
    Map<String, int>? bookingsByMonth,
    Map<String, double>? revenueByService,
    Map<String, double>? revenueByMonth,
    double? cancellationRate,
    double? completionRate,
    int? totalCustomers,
    int? returningCustomers,
    double? customerRetentionRate,
  }) {
    return BookingStatistics(
      totalBookings: totalBookings ?? this.totalBookings,
      confirmedBookings: confirmedBookings ?? this.confirmedBookings,
      pendingBookings: pendingBookings ?? this.pendingBookings,
      cancelledBookings: cancelledBookings ?? this.cancelledBookings,
      completedBookings: completedBookings ?? this.completedBookings,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      averageBookingValue: averageBookingValue ?? this.averageBookingValue,
      bookingsByService: bookingsByService ?? this.bookingsByService,
      bookingsByMonth: bookingsByMonth ?? this.bookingsByMonth,
      revenueByService: revenueByService ?? this.revenueByService,
      revenueByMonth: revenueByMonth ?? this.revenueByMonth,
      cancellationRate: cancellationRate ?? this.cancellationRate,
      completionRate: completionRate ?? this.completionRate,
      totalCustomers: totalCustomers ?? this.totalCustomers,
      returningCustomers: returningCustomers ?? this.returningCustomers,
      customerRetentionRate: customerRetentionRate ?? this.customerRetentionRate,
    );
  }

  /// إحصائيات فارغة
  static const BookingStatistics empty = BookingStatistics(
    totalBookings: 0,
    confirmedBookings: 0,
    pendingBookings: 0,
    cancelledBookings: 0,
    completedBookings: 0,
    totalRevenue: 0.0,
    averageBookingValue: 0.0,
    bookingsByService: {},
    bookingsByMonth: {},
    revenueByService: {},
    revenueByMonth: {},
    cancellationRate: 0.0,
    completionRate: 0.0,
    totalCustomers: 0,
    returningCustomers: 0,
    customerRetentionRate: 0.0,
  );

  @override
  List<Object?> get props => [
        totalBookings,
        confirmedBookings,
        pendingBookings,
        cancelledBookings,
        completedBookings,
        totalRevenue,
        averageBookingValue,
        bookingsByService,
        bookingsByMonth,
        revenueByService,
        revenueByMonth,
        cancellationRate,
        completionRate,
        totalCustomers,
        returningCustomers,
        customerRetentionRate,
      ];

  @override
  String toString() {
    return 'BookingStatistics(totalBookings: $totalBookings, totalRevenue: $totalRevenue, cancellationRate: $cancellationRate)';
  }
}
