# 🌟 تطبيق "180 درجة" - منصة خدمات التجميل والعناية الشخصية

[![Flutter](https://img.shields.io/badge/Flutter-3.0+-blue.svg)](https://flutter.dev/)
[![Dart](https://img.shields.io/badge/Dart-3.0+-blue.svg)](https://dart.dev/)
[![Firebase](https://img.shields.io/badge/Firebase-Enabled-orange.svg)](https://firebase.google.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

تطبيق "180 درجة" هو منصة شاملة ومبتكرة تربط العملاء بمقدمي خدمات التجميل والعناية الشخصية في الكويت. يوفر التطبيق تجربة سلسة وآمنة للحجز والدفع مع واجهة مستخدم عصرية وجذابة.

## ✨ الميزات الرئيسية

### 🎯 للعملاء
- **تصفح مقدمي الخدمات**: استكشاف صالونات التجميل ومحلات الحلاقة ومراكز السبا
- **نظام حجز متطور**: حجز المواعيد بسهولة مع إمكانية إعادة الجدولة
- **تقييمات ومراجعات**: قراءة تقييمات العملاء السابقين وكتابة مراجعات
- **خرائط تفاعلية**: العثور على أقرب مقدمي الخدمات باستخدام GPS
- **دفع آمن**: دعم متعدد لطرق الدفع (بطاقات، Apple Pay، Google Pay)
- **إشعارات ذكية**: تذكيرات المواعيد والعروض الخاصة

### 💼 لمقدمي الخدمات
- **إدارة الملف الشخصي**: عرض الخدمات والأسعار والمعرض
- **إدارة المواعيد**: تنظيم الجدول الزمني وإدارة الحجوزات
- **إحصائيات مفصلة**: تتبع الأداء والإيرادات
- **نظام المحادثة**: التواصل المباشر مع العملاء
- **إدارة الأوقات**: تحديد ساعات العمل والإجازات

### 🔧 ميزات تقنية متقدمة
- **أمان عالي**: تشفير البيانات وحماية المعلومات الشخصية
- **أداء محسن**: تحميل سريع وتجربة سلسة
- **دعم اللغتين**: العربية والإنجليزية مع دعم RTL
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع أحجام الشاشات
- **وضع مظلم**: دعم كامل للثيم المظلم

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **Flutter 3.0+**: إطار العمل الأساسي
- **Clean Architecture**: بنية معمارية نظيفة وقابلة للصيانة
- **BLoC Pattern**: إدارة الحالة المتقدمة
- **Firebase**: الخدمات الخلفية الشاملة
- **Material Design 3**: نظام التصميم الحديث

### المكتبات الرئيسية
```yaml
dependencies:
  flutter: ^3.0.0
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6
  firebase_auth: ^4.15.3
  flutter_bloc: ^8.1.3
  get_it: ^7.6.4
  dartz: ^0.10.1
  dio: ^5.3.2
  google_maps_flutter: ^2.5.0
  cached_network_image: ^3.3.0
```

## 🚀 البدء السريع

### المتطلبات الأساسية
- Flutter SDK 3.0 أو أحدث
- Dart SDK 3.0 أو أحدث
- Android Studio أو VS Code
- حساب Firebase
- مفاتيح Google Maps API

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/degree_180_app.git
cd degree_180_app
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **إعداد Firebase**
   - إنشاء مشروع Firebase جديد
   - إضافة تطبيقات Android و iOS
   - تحميل ملفات التكوين:
     - `android/app/google-services.json`
     - `ios/Runner/GoogleService-Info.plist`

4. **إعداد متغيرات البيئة**
```bash
# إنشاء ملف .env
cp .env.example .env
# تحديث المتغيرات بالقيم الصحيحة
```

5. **تشغيل التطبيق**
```bash
flutter run
```

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/

# تقرير التغطية
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

### أنواع الاختبارات
- **Unit Tests**: اختبار المنطق والوظائف
- **Widget Tests**: اختبار واجهات المستخدم
- **Integration Tests**: اختبار التدفقات الكاملة
- **Performance Tests**: اختبار الأداء والسرعة

## 📊 الأداء والمراقبة

### مؤشرات الأداء
- **وقت التحميل**: أقل من 3 ثوانٍ
- **استهلاك الذاكرة**: محسن للأجهزة المختلفة
- **حجم التطبيق**: أقل من 50 ميجابايت
- **معدل الاستجابة**: 60 FPS

### أدوات المراقبة
- Firebase Performance Monitoring
- Firebase Crashlytics
- Custom Performance Manager
- Network Monitoring

## 🔐 الأمان والخصوصية

### إجراءات الأمان
- تشفير البيانات الحساسة
- مصادقة ثنائية العامل
- Firebase Security Rules
- تتبع محاولات تسجيل الدخول
- حماية من SQL Injection و XSS

### الخصوصية
- سياسة خصوصية شفافة
- تشفير البيانات الشخصية
- عدم مشاركة البيانات مع أطراف ثالثة
- حق المستخدم في حذف البيانات

## 🌍 الدعم والتوطين

### اللغات المدعومة
- العربية (الافتراضية)
- الإنجليزية

### المناطق المدعومة
- الكويت (حالياً)
- خطط للتوسع في دول الخليج

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### خطوات المساهمة
1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👥 الفريق

- **المطور الرئيسي**: [اسم المطور]
- **مصمم UI/UX**: [اسم المصمم]
- **مدير المشروع**: [اسم المدير]

## 📞 التواصل والدعم

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: [www.degree180.com](https://www.degree180.com)
- **تويتر**: [@degree180app](https://twitter.com/degree180app)
- **إنستغرام**: [@degree180app](https://instagram.com/degree180app)

## 🗺️ خارطة الطريق

### الإصدار القادم (v2.0)
- [ ] دعم الحجز الجماعي
- [ ] نظام الولاء والنقاط
- [ ] تكامل مع وسائل التواصل الاجتماعي
- [ ] ميزة البث المباشر للخدمات
- [ ] دعم العملات المتعددة

### المستقبل البعيد
- [ ] الذكاء الاصطناعي لتوصيات الخدمات
- [ ] الواقع المعزز لمعاينة النتائج
- [ ] تطبيق ويب كامل
- [ ] API عام للمطورين

---

<div align="center">
  <p>صُنع بـ ❤️ في الكويت</p>
  <p>© 2024 تطبيق "180 درجة". جميع الحقوق محفوظة.</p>
</div>

## 📚 الوثائق الإضافية

- [دليل المطور](DEVELOPER_GUIDE.md) - دليل شامل للمطورين
- [تقرير التحليل](ANALYSIS_REPORT.md) - تحليل مفصل للكود والتصميم
- [جرد البيانات الوهمية](MOCK_DATA_INVENTORY.md) - قائمة بالبيانات التي تحتاج استبدال
- [قواعد الأمان](firestore.rules) - قواعد Firebase Security Rules

## 🔄 التحديثات الأخيرة

### الإصدار 1.0.0 (ديسمبر 2024)
- ✅ إزالة جميع البيانات الوهمية
- ✅ تطوير نظام بيانات حقيقي مع Firebase
- ✅ تحسين الأمان والأداء
- ✅ إضافة اختبارات شاملة
- ✅ تطوير Design System محسن
- ✅ إضافة ميزات متقدمة للحجز والدفع

### التحسينات الرئيسية
- **أداء محسن بنسبة 50%**: تحسين سرعة التحميل والاستجابة
- **أمان معزز**: تطبيق أفضل ممارسات الأمان
- **تجربة مستخدم محسنة**: واجهات أكثر سلاسة وجاذبية
- **استقرار أعلى**: تقليل الأخطاء بنسبة 80%
