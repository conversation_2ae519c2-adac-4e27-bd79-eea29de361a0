class AppConfig {
  static const String appName = '180 درجة';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  
  // Environment
  static const bool isProduction = false;
  static const bool enableLogging = true;
  
  // API Configuration
  static const String baseUrl = 'https://api.degree180.com';
  static const int connectTimeout = 30000;
  static const int receiveTimeout = 30000;
  
  // Firebase Configuration
  static const String firebaseProjectId = 'demo-project';
  
  // Features Flags
  static const bool enableGoogleSignIn = true;
  static const bool enableAppleSignIn = true;
  static const bool enableBiometricAuth = true;
  static const bool enablePushNotifications = true;
  
  // UI Configuration
  static const bool enableAnimations = true;
  static const bool enableHapticFeedback = true;
  
  // Cache Configuration
  static const int cacheMaxAge = 3600; // 1 hour in seconds
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png'];
  
  // Location
  static const double defaultLatitude = 29.3759; // Kuwait City
  static const double defaultLongitude = 47.9774; // Kuwait City
  static const double searchRadius = 50.0; // 50km
  
  // Rating
  static const int maxRating = 5;
  static const double minRatingToShow = 1.0;
  
  // Chat
  static const int maxMessageLength = 500;
  static const int chatHistoryLimit = 100;
  
  // Booking
  static const int maxAdvanceBookingDays = 30;
  static const int minBookingNoticeHours = 2;
  
  // Worker Schedule
  static const int maxWorkingHoursPerDay = 12;
  static const int minBreakMinutes = 30;
  
  // Customer
  static const int maxFavoriteProviders = 50;
  static const int maxRecentSearches = 10;
  
  // Notifications
  static const bool defaultNotificationsEnabled = true;
  static const bool defaultEmailNotifications = false;
  static const bool defaultSmsNotifications = false;
  
  // Security
  static const int maxLoginAttempts = 5;
  static const int lockoutDurationMinutes = 15;
  static const int sessionTimeoutMinutes = 60;
  
  // Performance
  static const int imageCompressionQuality = 80;
  static const int thumbnailSize = 200;
  static const int preloadDistance = 5; // Number of items to preload
  
  // Analytics
  static const bool enableAnalytics = !isProduction;
  static const bool enableCrashReporting = true;
  
  // Development
  static const bool showDebugInfo = !isProduction;
  static const bool enableTestMode = !isProduction;
}
