import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/models/location_model.dart';
import '../../../../core/enums/service_type.dart';
import '../../domain/usecases/get_service_providers_usecase.dart';
import '../../domain/usecases/get_service_provider_details_usecase.dart';



class ServiceProviderBloc extends Bloc<ServiceProviderEvent, ServiceProviderState> {
  final GetServiceProvidersUseCase getServiceProvidersUseCase;
  final GetServiceProviderDetailsUseCase getServiceProviderDetailsUseCase;

  ServiceProviderBloc({
    required this.getServiceProvidersUseCase,
    required this.getServiceProviderDetailsUseCase,
  }) : super(ServiceProviderInitial()) {
    on<LoadServiceProvidersEvent>(_onLoadServiceProviders);
    on<LoadServiceProviderDetailsEvent>(_onLoadServiceProviderDetails);
    on<SearchServiceProvidersEvent>(_onSearchServiceProviders);
    on<LoadNearbyProvidersEvent>(_onLoadNearbyProviders);
    on<LoadFeaturedProvidersEvent>(_onLoadFeaturedProviders);
    on<RefreshServiceProvidersEvent>(_onRefreshServiceProviders);
  }

  Future<void> _onLoadServiceProviders(
    LoadServiceProvidersEvent event,
    Emitter<ServiceProviderState> emit,
  ) async {
    emit(ServiceProviderLoading());

    final result = await getServiceProvidersUseCase(
      GetServiceProvidersParams(
        serviceType: event.serviceType,
        userLocation: event.userLocation,
        radiusKm: event.radiusKm,
        limit: event.limit,
        lastDocumentId: event.lastDocumentId,
        isVerified: event.isVerified,
        isOnline: event.isOnline,
        minRating: event.minRating,
      ),
    );

    result.fold(
      (failure) => emit(ServiceProviderError(message: failure.message)),
      (providers) {
        if (providers.isEmpty) {
          emit(const ServiceProviderEmpty(message: 'No service providers found'));
        } else {
          emit(ServiceProviderLoaded(providers: providers));
        }
      },
    );
  }

  Future<void> _onLoadServiceProviderDetails(
    LoadServiceProviderDetailsEvent event,
    Emitter<ServiceProviderState> emit,
  ) async {
    emit(ServiceProviderDetailsLoading());

    final result = await getServiceProviderDetailsUseCase(event.providerId);

    result.fold(
      (failure) => emit(ServiceProviderError(message: failure.message)),
      (provider) => emit(ServiceProviderDetailsLoaded(provider: provider)),
    );
  }

  Future<void> _onSearchServiceProviders(
    SearchServiceProvidersEvent event,
    Emitter<ServiceProviderState> emit,
  ) async {
    emit(ServiceProviderSearchLoading());

    final result = await getServiceProvidersUseCase(
      GetServiceProvidersParams(
        serviceType: event.serviceType,
        userLocation: event.userLocation,
        radiusKm: event.radiusKm,
        limit: event.limit,
      ),
    );

    result.fold(
      (failure) => emit(ServiceProviderError(message: failure.message)),
      (providers) {
        // Filter by search query
        final filteredProviders = providers.where((provider) {
          final query = event.query.toLowerCase();
          return provider.name.toLowerCase().contains(query) ||
                 provider.description?.toLowerCase().contains(query) == true ||
                 provider.services.any((service) => 
                   service.name.toLowerCase().contains(query) ||
                   service.description.toLowerCase().contains(query));
        }).toList();

        if (filteredProviders.isEmpty) {
          emit(const ServiceProviderSearchEmpty(message: 'No providers found for your search'));
        } else {
          emit(ServiceProviderSearchLoaded(providers: filteredProviders, query: event.query));
        }
      },
    );
  }

  Future<void> _onLoadNearbyProviders(
    LoadNearbyProvidersEvent event,
    Emitter<ServiceProviderState> emit,
  ) async {
    emit(ServiceProviderLoading());

    final result = await getServiceProvidersUseCase(
      GetServiceProvidersParams(
        userLocation: event.userLocation,
        radiusKm: event.radiusKm,
        serviceType: event.serviceType,
        limit: event.limit,
        isVerified: true,
        isOnline: true,
      ),
    );

    result.fold(
      (failure) => emit(ServiceProviderError(message: failure.message)),
      (providers) {
        if (providers.isEmpty) {
          emit(const ServiceProviderEmpty(message: 'No nearby providers found'));
        } else {
          emit(ServiceProviderLoaded(providers: providers));
        }
      },
    );
  }

  Future<void> _onLoadFeaturedProviders(
    LoadFeaturedProvidersEvent event,
    Emitter<ServiceProviderState> emit,
  ) async {
    emit(ServiceProviderLoading());

    final result = await getServiceProvidersUseCase(
      GetServiceProvidersParams(
        serviceType: event.serviceType,
        limit: event.limit,
        isVerified: true,
        minRating: 4.0,
      ),
    );

    result.fold(
      (failure) => emit(ServiceProviderError(message: failure.message)),
      (providers) {
        if (providers.isEmpty) {
          emit(const ServiceProviderEmpty(message: 'No featured providers found'));
        } else {
          emit(ServiceProviderLoaded(providers: providers));
        }
      },
    );
  }

  Future<void> _onRefreshServiceProviders(
    RefreshServiceProvidersEvent event,
    Emitter<ServiceProviderState> emit,
  ) async {
    // Don't show loading state for refresh
    final result = await getServiceProvidersUseCase(
      GetServiceProvidersParams(
        serviceType: event.serviceType,
        userLocation: event.userLocation,
        radiusKm: event.radiusKm,
        limit: event.limit,
        isVerified: event.isVerified,
        isOnline: event.isOnline,
        minRating: event.minRating,
      ),
    );

    result.fold(
      (failure) => emit(ServiceProviderError(message: failure.message)),
      (providers) {
        if (providers.isEmpty) {
          emit(const ServiceProviderEmpty(message: 'No service providers found'));
        } else {
          emit(ServiceProviderLoaded(providers: providers));
        }
      },
    );
  }
}

// Events
abstract class ServiceProviderEvent extends Equatable {
  const ServiceProviderEvent();

  @override
  List<Object?> get props => [];
}

class LoadServiceProvidersEvent extends ServiceProviderEvent {
  final ServiceType? serviceType;
  final LocationModel? userLocation;
  final double? radiusKm;
  final int? limit;
  final String? lastDocumentId;
  final bool? isVerified;
  final bool? isOnline;
  final double? minRating;

  const LoadServiceProvidersEvent({
    this.serviceType,
    this.userLocation,
    this.radiusKm,
    this.limit,
    this.lastDocumentId,
    this.isVerified,
    this.isOnline,
    this.minRating,
  });

  @override
  List<Object?> get props => [
        serviceType,
        userLocation,
        radiusKm,
        limit,
        lastDocumentId,
        isVerified,
        isOnline,
        minRating,
      ];
}

class LoadServiceProviderDetailsEvent extends ServiceProviderEvent {
  final String providerId;

  const LoadServiceProviderDetailsEvent({required this.providerId});

  @override
  List<Object> get props => [providerId];
}

class SearchServiceProvidersEvent extends ServiceProviderEvent {
  final String query;
  final ServiceType? serviceType;
  final LocationModel? userLocation;
  final double? radiusKm;
  final int? limit;

  const SearchServiceProvidersEvent({
    required this.query,
    this.serviceType,
    this.userLocation,
    this.radiusKm,
    this.limit,
  });

  @override
  List<Object?> get props => [query, serviceType, userLocation, radiusKm, limit];
}

class LoadNearbyProvidersEvent extends ServiceProviderEvent {
  final LocationModel userLocation;
  final double radiusKm;
  final ServiceType? serviceType;
  final int? limit;

  const LoadNearbyProvidersEvent({
    required this.userLocation,
    this.radiusKm = 10.0,
    this.serviceType,
    this.limit,
  });

  @override
  List<Object?> get props => [userLocation, radiusKm, serviceType, limit];
}

class LoadFeaturedProvidersEvent extends ServiceProviderEvent {
  final ServiceType? serviceType;
  final int? limit;

  const LoadFeaturedProvidersEvent({
    this.serviceType,
    this.limit,
  });

  @override
  List<Object?> get props => [serviceType, limit];
}

class RefreshServiceProvidersEvent extends ServiceProviderEvent {
  final ServiceType? serviceType;
  final LocationModel? userLocation;
  final double? radiusKm;
  final int? limit;
  final bool? isVerified;
  final bool? isOnline;
  final double? minRating;

  const RefreshServiceProvidersEvent({
    this.serviceType,
    this.userLocation,
    this.radiusKm,
    this.limit,
    this.isVerified,
    this.isOnline,
    this.minRating,
  });

  @override
  List<Object?> get props => [
        serviceType,
        userLocation,
        radiusKm,
        limit,
        isVerified,
        isOnline,
        minRating,
      ];
}

// States
abstract class ServiceProviderState extends Equatable {
  const ServiceProviderState();

  @override
  List<Object?> get props => [];
}

class ServiceProviderInitial extends ServiceProviderState {}

class ServiceProviderLoading extends ServiceProviderState {}

class ServiceProviderLoaded extends ServiceProviderState {
  final List<ServiceProviderModel> providers;

  const ServiceProviderLoaded({required this.providers});

  @override
  List<Object> get props => [providers];
}

class ServiceProviderEmpty extends ServiceProviderState {
  final String message;

  const ServiceProviderEmpty({required this.message});

  @override
  List<Object> get props => [message];
}

class ServiceProviderError extends ServiceProviderState {
  final String message;

  const ServiceProviderError({required this.message});

  @override
  List<Object> get props => [message];
}

class ServiceProviderDetailsLoading extends ServiceProviderState {}

class ServiceProviderDetailsLoaded extends ServiceProviderState {
  final ServiceProviderModel provider;

  const ServiceProviderDetailsLoaded({required this.provider});

  @override
  List<Object> get props => [provider];
}

class ServiceProviderSearchLoading extends ServiceProviderState {}

class ServiceProviderSearchLoaded extends ServiceProviderState {
  final List<ServiceProviderModel> providers;
  final String query;

  const ServiceProviderSearchLoaded({
    required this.providers,
    required this.query,
  });

  @override
  List<Object> get props => [providers, query];
}

class ServiceProviderSearchEmpty extends ServiceProviderState {
  final String message;

  const ServiceProviderSearchEmpty({required this.message});

  @override
  List<Object> get props => [message];
}
