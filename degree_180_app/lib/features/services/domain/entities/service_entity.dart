import 'package:equatable/equatable.dart';
import '../../../auth/domain/entities/user_entity.dart';

enum ServiceStatus { available, busy, offline }

class ServiceEntity extends Equatable {
  final String id;
  final String providerId;
  final String name;
  final String description;
  final ServiceType serviceType;
  final List<String> imageUrls;
  final double price;
  final int duration; // in minutes
  final double rating;
  final int reviewCount;
  final ServiceStatus status;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalData;

  const ServiceEntity({
    required this.id,
    required this.providerId,
    required this.name,
    required this.description,
    required this.serviceType,
    this.imageUrls = const [],
    required this.price,
    required this.duration,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.status = ServiceStatus.available,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.additionalData,
  });

  @override
  List<Object?> get props => [
        id,
        providerId,
        name,
        description,
        serviceType,
        imageUrls,
        price,
        duration,
        rating,
        reviewCount,
        status,
        isActive,
        createdAt,
        updatedAt,
        additionalData,
      ];

  ServiceEntity copyWith({
    String? id,
    String? providerId,
    String? name,
    String? description,
    ServiceType? serviceType,
    List<String>? imageUrls,
    double? price,
    int? duration,
    double? rating,
    int? reviewCount,
    ServiceStatus? status,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return ServiceEntity(
      id: id ?? this.id,
      providerId: providerId ?? this.providerId,
      name: name ?? this.name,
      description: description ?? this.description,
      serviceType: serviceType ?? this.serviceType,
      imageUrls: imageUrls ?? this.imageUrls,
      price: price ?? this.price,
      duration: duration ?? this.duration,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      status: status ?? this.status,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  String get serviceTypeString {
    switch (serviceType) {
      case ServiceType.salon:
        return 'salon';
      case ServiceType.barbershop:
        return 'barbershop';
      case ServiceType.spa:
        return 'spa';
      case ServiceType.fitness:
        return 'fitness';
    }
  }

  String get statusString {
    switch (status) {
      case ServiceStatus.available:
        return 'available';
      case ServiceStatus.busy:
        return 'busy';
      case ServiceStatus.offline:
        return 'offline';
    }
  }

  String get formattedPrice => '${price.toStringAsFixed(0)} KD';
  
  String get formattedDuration {
    if (duration < 60) {
      return '${duration}m';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}m';
      }
    }
  }

  bool get hasImages => imageUrls.isNotEmpty;
  
  String? get primaryImageUrl => hasImages ? imageUrls.first : null;
}
