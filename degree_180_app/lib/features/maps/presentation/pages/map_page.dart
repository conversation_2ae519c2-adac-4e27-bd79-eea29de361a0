import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/provider_info_card.dart';
import '../widgets/map_search_bar.dart';

class MapPage extends StatefulWidget {
  const MapPage({super.key});

  @override
  State<MapPage> createState() => _MapPageState();
}

class _MapPageState extends State<MapPage> {
  GoogleMapController? _mapController;
  Position? _currentPosition;
  Set<Marker> _markers = {};
  String? _selectedProviderId;
  
  static const CameraPosition _initialPosition = CameraPosition(
    target: LatLng(29.3759, 47.9774), // Kuwait City
    zoom: 12,
  );

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
    _loadProviders();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Find Nearby'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showMapOptions,
            icon: const Icon(Icons.layers),
          ),
        ],
      ),
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            initialCameraPosition: _initialPosition,
            onMapCreated: _onMapCreated,
            markers: _markers,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            onTap: (_) => _clearSelection(),
          ),
          
          // Search Bar
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: MapSearchBar(
              onSearch: _searchLocation,
              onFilterPressed: _showFilters,
            ),
          ),
          
          // Current Location Button
          Positioned(
            bottom: _selectedProviderId != null ? 200 : 100,
            right: 16,
            child: FloatingActionButton(
              onPressed: _goToCurrentLocation,
              backgroundColor: AppColors.backgroundWhite,
              foregroundColor: AppColors.primaryPurple,
              mini: true,
              child: const Icon(Icons.my_location),
            ),
          ),
          
          // Provider Info Card
          if (_selectedProviderId != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: ProviderInfoCard(
                providerId: _selectedProviderId!,
                onClose: _clearSelection,
                onBookNow: () => _bookProvider(_selectedProviderId!),
                onViewProfile: () => _viewProviderProfile(_selectedProviderId!),
              ),
            ),
        ],
      ),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    if (_currentPosition != null) {
      _goToCurrentLocation();
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final requestedPermission = await Geolocator.requestPermission();
        if (requestedPermission == LocationPermission.denied) {
          return;
        }
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        _currentPosition = position;
      });

      if (_mapController != null) {
        _goToCurrentLocation();
      }
    } catch (e) {
      debugPrint('Error getting location: $e');
    }
  }

  void _goToCurrentLocation() {
    if (_currentPosition != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLng(
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
        ),
      );
    }
  }

  void _loadProviders() {
    // Mock provider data
    final providers = [
      {
        'id': '1',
        'name': 'Ahmed Hassan Barbershop',
        'type': 'barbershop',
        'rating': 4.8,
        'lat': 29.3759,
        'lng': 47.9774,
        'distance': '0.5 km',
        'isOpen': true,
      },
      {
        'id': '2',
        'name': 'Fatima Beauty Salon',
        'type': 'salon',
        'rating': 4.9,
        'lat': 29.3800,
        'lng': 47.9800,
        'distance': '1.2 km',
        'isOpen': true,
      },
      {
        'id': '3',
        'name': 'Luxury Spa Center',
        'type': 'spa',
        'rating': 4.7,
        'lat': 29.3700,
        'lng': 47.9700,
        'distance': '2.1 km',
        'isOpen': false,
      },
    ];

    final markers = providers.map((provider) {
      return Marker(
        markerId: MarkerId(provider['id'] as String),
        position: LatLng(
          provider['lat'] as double,
          provider['lng'] as double,
        ),
        icon: _getMarkerIcon(provider['type'] as String),
        onTap: () => _selectProvider(provider['id'] as String),
        infoWindow: InfoWindow(
          title: provider['name'] as String,
          snippet: '⭐ ${provider['rating']} • ${provider['distance']}',
        ),
      );
    }).toSet();

    setState(() {
      _markers = markers;
    });
  }

  BitmapDescriptor _getMarkerIcon(String type) {
    // In a real app, you would use custom marker icons
    return BitmapDescriptor.defaultMarkerWithHue(
      type == 'salon' ? BitmapDescriptor.hueRose :
      type == 'spa' ? BitmapDescriptor.hueBlue :
      BitmapDescriptor.hueViolet,
    );
  }

  void _selectProvider(String providerId) {
    setState(() {
      _selectedProviderId = providerId;
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedProviderId = null;
    });
  }

  void _searchLocation(String query) {
    // TODO: Implement location search
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Searching for: $query')),
    );
  }

  void _showFilters() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter by Service Type',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildFilterChip('All', true),
            _buildFilterChip('Salon', false),
            _buildFilterChip('Barbershop', false),
            _buildFilterChip('Spa', false),
            const SizedBox(height: AppConstants.defaultPadding),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Apply Filters'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          // TODO: Implement filter logic
        },
        selectedColor: AppColors.primaryPurple.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primaryPurple,
      ),
    );
  }

  void _showMapOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.map),
              title: const Text('Normal'),
              onTap: () {
                // TODO: Change map type
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.satellite),
              title: const Text('Satellite'),
              onTap: () {
                // TODO: Change map type
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.terrain),
              title: const Text('Terrain'),
              onTap: () {
                // TODO: Change map type
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _bookProvider(String providerId) {
    Navigator.pushNamed(
      context,
      '/booking',
      arguments: {'providerId': providerId},
    );
  }

  void _viewProviderProfile(String providerId) {
    Navigator.pushNamed(
      context,
      '/provider-profile',
      arguments: {'providerId': providerId},
    );
  }
}
