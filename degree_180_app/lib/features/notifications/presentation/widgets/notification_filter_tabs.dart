import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class NotificationFilterTabs extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const NotificationFilterTabs({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final filters = [
      {'id': 'all', 'label': 'All'},
      {'id': 'unread', 'label': 'Unread'},
      {'id': 'booking', 'label': 'Bookings'},
      {'id': 'message', 'label': 'Messages'},
      {'id': 'promotion', 'label': 'Offers'},
    ];

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: filters.map((filter) {
            final isSelected = selectedFilter == filter['id'];
            return Padding(
              padding: const EdgeInsets.only(right: AppConstants.smallPadding),
              child: _buildFilterTab(
                label: filter['label']!,
                isSelected: isSelected,
                onTap: () => onFilterChanged(filter['id']!),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFilterTab({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          decoration: BoxDecoration(
            color: isSelected 
                ? AppColors.buttonWhite.withValues(alpha: 0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
            border: Border.all(
              color: isSelected 
                  ? AppColors.buttonWhite.withValues(alpha: 0.5)
                  : Colors.transparent,
            ),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: AppColors.textWhite,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
        ),
      ),
    );
  }
}
