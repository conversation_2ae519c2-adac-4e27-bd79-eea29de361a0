import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/theme_manager.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/security/rate_limiter.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/auth_header.dart';
import '../widgets/social_login_buttons.dart';
import '../widgets/gender_theme_selector.dart';
import '../bloc/auth_bloc.dart';

class LoginPage extends StatefulWidget {
  final String? userType;
  final String? serviceType;

  const LoginPage({
    super.key,
    this.userType,
    this.serviceType,
  });

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _rememberMe = false;
  Gender? _selectedGender;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeManager.currentTheme,
      child: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthLoading) {
            setState(() {
              _isLoading = true;
            });
          } else if (state is AuthAuthenticated) {
            setState(() {
              _isLoading = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  AppStrings.loginSuccess,
                  style: GoogleFonts.cairo(),
                ),
                backgroundColor: Colors.green,
              ),
            );

            // Navigate based on user type
            if (widget.userType == 'customer') {
              context.go('/customer-home');
            } else {
              context.go('/worker-dashboard');
            }
          } else if (state is AuthError) {
            setState(() {
              _isLoading = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: GoogleFonts.cairo(),
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.backgroundWhite,
          body: SingleChildScrollView(
            child: Column(
              children: [
                // Header with gradient background
                AnimatedAuthHeader(
                  title: '${AppStrings.welcomeTo}\n${AppStrings.appName}',
                  subtitle: 'سجل دخولك للاستمتاع بأفضل خدمات التجميل',
                  showLogo: true,
                ),

            // Login Form
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: AppConstants.defaultPadding),

                  // Gender Theme Selector
                  if (_selectedGender == null) ...[
                    GenderThemeSelector(
                      selectedGender: _selectedGender,
                      onGenderChanged: (gender) {
                        setState(() {
                          _selectedGender = gender;
                        });
                      },
                    ),
                    const SizedBox(height: AppConstants.extraLargePadding),
                  ] else ...[
                    // Compact gender selector
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'الثيم المحدد: ',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: AppColors.textGray,
                          ),
                        ),
                        GenderThemeSelector(
                          selectedGender: _selectedGender,
                          onGenderChanged: (gender) {
                            setState(() {
                              _selectedGender = gender;
                            });
                          },
                          showTitle: false,
                          isCompact: true,
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                  ],

                // Login Form
                SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // Email Field
                          CustomTextField(
                            controller: _emailController,
                            hintText: AppStrings.emailUsernameOrNumber,
                            keyboardType: TextInputType.emailAddress,
                            validator: _validateEmail,
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          // Password Field
                          CustomTextField(
                            controller: _passwordController,
                            hintText: AppStrings.password,
                            obscureText: _obscurePassword,
                            suffixIcon: IconButton(
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                              icon: Icon(
                                _obscurePassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: AppColors.textWhite,
                              ),
                            ),
                            validator: _validatePassword,
                          ),
                          
                          const SizedBox(height: AppConstants.extraLargePadding),
                          
                          // Login Button
                          CustomButton(
                            text: AppStrings.enter,
                            onPressed: _handleLogin,
                            isLoading: _isLoading,
                            backgroundColor: AppColors.buttonWhite,
                            textColor: AppColors.textDark,
                          ),
                          
                          const SizedBox(height: AppConstants.largePadding),
                          
                          // Forgot Password
                          TextButton(
                            onPressed: _handleForgotPassword,
                            child: Text(
                              AppStrings.forgetPassword,
                              style: Theme.of(context).textTheme.labelMedium,
                            ),
                          ),
                          
                          const SizedBox(height: AppConstants.smallPadding),
                          
                          // Create Account
                          TextButton(
                            onPressed: _navigateToRegister,
                            child: Text(
                              AppStrings.createAccount,
                              style: Theme.of(context).textTheme.labelMedium,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (value.length < AppConstants.minPasswordLength) {
      return AppStrings.passwordTooShort;
    }
    return null;
  }

  void _handleLogin() async {
    if (_selectedGender == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى اختيار الثيم المناسب أولاً',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_formKey.currentState!.validate()) {
      // فحص Rate Limiting قبل المحاولة
      final email = _emailController.text.trim();
      final rateLimitResult = await RateLimiter.instance.canAttempt(email);

      if (!mounted) return;

      if (!rateLimitResult.canAttempt) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              rateLimitResult.reason ?? 'تم تجاوز الحد الأقصى للمحاولات',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: rateLimitResult.formattedRemainingTime != null
                ? SnackBarAction(
                    label: 'الوقت المتبقي: ${rateLimitResult.formattedRemainingTime}',
                    onPressed: () {},
                    textColor: Colors.white,
                  )
                : null,
          ),
        );
        return;
      }

      // Use AuthBloc to handle login
      context.read<AuthBloc>().add(
        SignInWithEmailEvent(
          email: email,
          password: _passwordController.text,
        ),
      );
    }
  }

  void _handleForgotPassword() {
    context.push('/forgot-password');
  }

  void _navigateToRegister() {
    final queryParams = <String, String>{};
    
    if (widget.userType != null) {
      queryParams['userType'] = widget.userType!;
    }
    
    if (widget.serviceType != null) {
      queryParams['serviceType'] = widget.serviceType!;
    }
    
    context.pushReplacementNamed(
      'register',
      queryParameters: queryParams,
    );
  }
}
