import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

enum MessageType {
  text,
  image,
  voice,
  file,
  location,
  system,
}

class ChatModel extends Equatable {
  final String id;
  final String participantId;
  final String participantName;
  final String? participantImage;
  final String lastMessage;
  final DateTime lastMessageTime;
  final int unreadCount;
  final bool isOnline;
  final DateTime? lastSeen;
  final bool isArchived;
  final bool isMuted;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const ChatModel({
    required this.id,
    required this.participantId,
    required this.participantName,
    this.participantImage,
    required this.lastMessage,
    required this.lastMessageTime,
    this.unreadCount = 0,
    this.isOnline = false,
    this.lastSeen,
    this.isArchived = false,
    this.isMuted = false,
    required this.createdAt,
    this.updatedAt,
  });

  ChatModel copyWith({
    String? id,
    String? participantId,
    String? participantName,
    String? participantImage,
    String? lastMessage,
    DateTime? lastMessageTime,
    int? unreadCount,
    bool? isOnline,
    DateTime? lastSeen,
    bool? isArchived,
    bool? isMuted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ChatModel(
      id: id ?? this.id,
      participantId: participantId ?? this.participantId,
      participantName: participantName ?? this.participantName,
      participantImage: participantImage ?? this.participantImage,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCount: unreadCount ?? this.unreadCount,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      isArchived: isArchived ?? this.isArchived,
      isMuted: isMuted ?? this.isMuted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'participantId': participantId,
      'participantName': participantName,
      'participantImage': participantImage,
      'lastMessage': lastMessage,
      'lastMessageTime': Timestamp.fromDate(lastMessageTime),
      'unreadCount': unreadCount,
      'isOnline': isOnline,
      'lastSeen': lastSeen != null ? Timestamp.fromDate(lastSeen!) : null,
      'isArchived': isArchived,
      'isMuted': isMuted,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  factory ChatModel.fromMap(Map<String, dynamic> map) {
    return ChatModel(
      id: map['id'] ?? '',
      participantId: map['participantId'] ?? '',
      participantName: map['participantName'] ?? '',
      participantImage: map['participantImage'],
      lastMessage: map['lastMessage'] ?? '',
      lastMessageTime: (map['lastMessageTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      unreadCount: map['unreadCount'] ?? 0,
      isOnline: map['isOnline'] ?? false,
      lastSeen: (map['lastSeen'] as Timestamp?)?.toDate(),
      isArchived: map['isArchived'] ?? false,
      isMuted: map['isMuted'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  factory ChatModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ChatModel.fromMap({...data, 'id': doc.id});
  }

  String get lastSeenText {
    if (isOnline) return 'Online';
    if (lastSeen == null) return 'Last seen recently';
    
    final now = DateTime.now();
    final difference = now.difference(lastSeen!);
    
    if (difference.inMinutes < 1) {
      return 'Last seen just now';
    } else if (difference.inMinutes < 60) {
      return 'Last seen ${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return 'Last seen ${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return 'Last seen ${difference.inDays}d ago';
    } else {
      return 'Last seen recently';
    }
  }

  @override
  List<Object?> get props => [
        id,
        participantId,
        participantName,
        participantImage,
        lastMessage,
        lastMessageTime,
        unreadCount,
        isOnline,
        lastSeen,
        isArchived,
        isMuted,
        createdAt,
        updatedAt,
      ];
}

class MessageModel extends Equatable {
  final String id;
  final String chatId;
  final String senderId;
  final String senderName;
  final String message;
  final MessageType messageType;
  final DateTime timestamp;
  final bool isRead;
  final DateTime? readAt;
  final String? imageUrl;
  final String? fileUrl;
  final String? fileName;
  final int? fileSize;
  final double? latitude;
  final double? longitude;
  final String? locationName;
  final int? voiceDuration;
  final bool isEdited;
  final DateTime? editedAt;
  final String? replyToMessageId;

  const MessageModel({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    required this.message,
    this.messageType = MessageType.text,
    required this.timestamp,
    this.isRead = false,
    this.readAt,
    this.imageUrl,
    this.fileUrl,
    this.fileName,
    this.fileSize,
    this.latitude,
    this.longitude,
    this.locationName,
    this.voiceDuration,
    this.isEdited = false,
    this.editedAt,
    this.replyToMessageId,
  });

  MessageModel copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? senderName,
    String? message,
    MessageType? messageType,
    DateTime? timestamp,
    bool? isRead,
    DateTime? readAt,
    String? imageUrl,
    String? fileUrl,
    String? fileName,
    int? fileSize,
    double? latitude,
    double? longitude,
    String? locationName,
    int? voiceDuration,
    bool? isEdited,
    DateTime? editedAt,
    String? replyToMessageId,
  }) {
    return MessageModel(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      messageType: messageType ?? this.messageType,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      readAt: readAt ?? this.readAt,
      imageUrl: imageUrl ?? this.imageUrl,
      fileUrl: fileUrl ?? this.fileUrl,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationName: locationName ?? this.locationName,
      voiceDuration: voiceDuration ?? this.voiceDuration,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'chatId': chatId,
      'senderId': senderId,
      'senderName': senderName,
      'message': message,
      'messageType': messageType.name,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'imageUrl': imageUrl,
      'fileUrl': fileUrl,
      'fileName': fileName,
      'fileSize': fileSize,
      'latitude': latitude,
      'longitude': longitude,
      'locationName': locationName,
      'voiceDuration': voiceDuration,
      'isEdited': isEdited,
      'editedAt': editedAt != null ? Timestamp.fromDate(editedAt!) : null,
      'replyToMessageId': replyToMessageId,
    };
  }

  factory MessageModel.fromMap(Map<String, dynamic> map) {
    return MessageModel(
      id: map['id'] ?? '',
      chatId: map['chatId'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? '',
      message: map['message'] ?? '',
      messageType: MessageType.values.firstWhere(
        (type) => type.name == map['messageType'],
        orElse: () => MessageType.text,
      ),
      timestamp: (map['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRead: map['isRead'] ?? false,
      readAt: (map['readAt'] as Timestamp?)?.toDate(),
      imageUrl: map['imageUrl'],
      fileUrl: map['fileUrl'],
      fileName: map['fileName'],
      fileSize: map['fileSize'],
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      locationName: map['locationName'],
      voiceDuration: map['voiceDuration'],
      isEdited: map['isEdited'] ?? false,
      editedAt: (map['editedAt'] as Timestamp?)?.toDate(),
      replyToMessageId: map['replyToMessageId'],
    );
  }

  factory MessageModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return MessageModel.fromMap({...data, 'id': doc.id});
  }

  String get displayText {
    switch (messageType) {
      case MessageType.text:
        return message;
      case MessageType.image:
        return '📷 Image';
      case MessageType.voice:
        return '🎤 Voice message';
      case MessageType.file:
        return '📎 ${fileName ?? 'File'}';
      case MessageType.location:
        return '📍 Location';
      case MessageType.system:
        return message;
    }
  }

  @override
  List<Object?> get props => [
        id,
        chatId,
        senderId,
        senderName,
        message,
        messageType,
        timestamp,
        isRead,
        readAt,
        imageUrl,
        fileUrl,
        fileName,
        fileSize,
        latitude,
        longitude,
        locationName,
        voiceDuration,
        isEdited,
        editedAt,
        replyToMessageId,
      ];
}
