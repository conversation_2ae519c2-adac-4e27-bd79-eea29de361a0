import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/theme_manager.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/enums/gender.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  bool _notificationsEnabled = true;
  bool _locationEnabled = true;
  bool _darkModeEnabled = false;
  String _selectedLanguage = 'ar';
  Gender _selectedGender = Gender.female;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
    _loadSettings();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadSettings() {
    // Load settings from SharedPreferences or Firebase
    // For now, using default values
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeManager.currentTheme,
      child: Scaffold(
        backgroundColor: AppColors.backgroundGray,
        appBar: AppBar(
          title: Text(
            'الإعدادات',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: ThemeManager.currentColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // App Settings
                _buildAppSettings(),
                
                const SizedBox(height: 24),
                
                // Notification Settings
                _buildNotificationSettings(),
                
                const SizedBox(height: 24),
                
                // Privacy Settings
                _buildPrivacySettings(),
                
                const SizedBox(height: 24),
                
                // Theme Settings
                _buildThemeSettings(),
                
                const SizedBox(height: 24),
                
                // Account Actions
                _buildAccountActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppSettings() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات التطبيق',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Language Setting
            _buildSettingItem(
              icon: Icons.language,
              title: 'اللغة',
              subtitle: _selectedLanguage == 'ar' ? 'العربية' : 'English',
              onTap: () => _showLanguageDialog(),
            ),
            
            const Divider(),
            
            // Notifications Toggle
            _buildSwitchItem(
              icon: Icons.notifications,
              title: 'الإشعارات',
              subtitle: 'تلقي إشعارات التطبيق',
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
                _saveSettings();
              },
            ),
            
            const Divider(),
            
            // Location Toggle
            _buildSwitchItem(
              icon: Icons.location_on,
              title: 'الموقع',
              subtitle: 'السماح بالوصول للموقع',
              value: _locationEnabled,
              onChanged: (value) {
                setState(() {
                  _locationEnabled = value;
                });
                _saveSettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الإشعارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildSettingItem(
              icon: Icons.notifications_active,
              title: 'إشعارات الحجز',
              subtitle: 'تأكيد وتذكير المواعيد',
              onTap: () {},
            ),
            
            const Divider(),
            
            _buildSettingItem(
              icon: Icons.message,
              title: 'إشعارات الرسائل',
              subtitle: 'رسائل جديدة من مقدمي الخدمة',
              onTap: () {},
            ),
            
            const Divider(),
            
            _buildSettingItem(
              icon: Icons.local_offer,
              title: 'إشعارات العروض',
              subtitle: 'عروض وخصومات خاصة',
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacySettings() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الخصوصية والأمان',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildSettingItem(
              icon: Icons.lock,
              title: 'تغيير كلمة المرور',
              subtitle: 'تحديث كلمة مرور الحساب',
              onTap: () => context.push('/change-password'),
            ),
            
            const Divider(),
            
            _buildSettingItem(
              icon: Icons.privacy_tip,
              title: 'سياسة الخصوصية',
              subtitle: 'اطلع على سياسة الخصوصية',
              onTap: () => context.push('/privacy-policy'),
            ),
            
            const Divider(),
            
            _buildSettingItem(
              icon: Icons.description,
              title: 'شروط الاستخدام',
              subtitle: 'اطلع على شروط الاستخدام',
              onTap: () => context.push('/terms-of-service'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeSettings() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات المظهر',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Gender/Theme Selection
            _buildSettingItem(
              icon: Icons.palette,
              title: 'نمط التطبيق',
              subtitle: _selectedGender == Gender.female ? 'نسائي (وردي)' : 'رجالي (أزرق)',
              onTap: () => _showGenderDialog(),
            ),
            
            const Divider(),
            
            // Dark Mode Toggle
            _buildSwitchItem(
              icon: Icons.dark_mode,
              title: 'الوضع الليلي',
              subtitle: 'تفعيل الوضع الليلي',
              value: _darkModeEnabled,
              onChanged: (value) {
                setState(() {
                  _darkModeEnabled = value;
                });
                _saveSettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountActions() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات الحساب',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildSettingItem(
              icon: Icons.logout,
              title: 'تسجيل الخروج',
              subtitle: 'الخروج من الحساب الحالي',
              onTap: () => _showLogoutDialog(),
              titleColor: Colors.red,
            ),
            
            const Divider(),
            
            _buildSettingItem(
              icon: Icons.delete_forever,
              title: 'حذف الحساب',
              subtitle: 'حذف الحساب نهائياً',
              onTap: () => _showDeleteAccountDialog(),
              titleColor: Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? titleColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ThemeManager.currentColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: ThemeManager.currentColors.primary,
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: titleColor ?? AppColors.textDark,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppColors.textGray,
                    ),
                  ),
                ],
              ),
            ),
            
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textGray,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: ThemeManager.currentColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: ThemeManager.currentColors.primary,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDark,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.textGray,
                  ),
                ),
              ],
            ),
          ),
          
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: ThemeManager.currentColors.primary,
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'اختر اللغة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: Text('العربية', style: GoogleFonts.cairo()),
              value: 'ar',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value!;
                });
                Navigator.pop(context);
                _saveSettings();
              },
            ),
            RadioListTile<String>(
              title: Text('English', style: GoogleFonts.cairo()),
              value: 'en',
              groupValue: _selectedLanguage,
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value!;
                });
                Navigator.pop(context);
                _saveSettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showGenderDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'اختر نمط التطبيق',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<Gender>(
              title: Text('نسائي (وردي)', style: GoogleFonts.cairo()),
              value: Gender.female,
              groupValue: _selectedGender,
              onChanged: (value) {
                setState(() {
                  _selectedGender = value!;
                });
                Navigator.pop(context);
                ThemeManager.setGender(value!);
                _saveSettings();
              },
            ),
            RadioListTile<Gender>(
              title: Text('رجالي (أزرق)', style: GoogleFonts.cairo()),
              value: Gender.male,
              groupValue: _selectedGender,
              onChanged: (value) {
                setState(() {
                  _selectedGender = value!;
                });
                Navigator.pop(context);
                ThemeManager.setGender(value!);
                _saveSettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تسجيل الخروج',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من تسجيل الخروج؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthBloc>().add(SignOutEvent());
              context.go('/login');
            },
            child: Text(
              'تسجيل الخروج',
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'حذف الحساب',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        content: Text(
          'هل أنت متأكد من حذف الحساب؟ هذا الإجراء لا يمكن التراجع عنه.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement account deletion
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'سيتم تفعيل هذه الميزة قريباً',
                    style: GoogleFonts.cairo(),
                  ),
                ),
              );
            },
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _saveSettings() {
    // Save settings to SharedPreferences or Firebase
    // For now, just showing a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم حفظ الإعدادات',
          style: GoogleFonts.cairo(),
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}
