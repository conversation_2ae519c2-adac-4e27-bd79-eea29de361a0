import 'package:flutter/material.dart';

/// أيقونات مخصصة للتطبيق
class CustomIcons {
  CustomIcons._();

  /// أيقونة Google
  static const IconData google = Icons.g_mobiledata;
  
  /// أيقونة Apple
  static const IconData apple = Icons.phone_iphone;
  
  /// أيقونة Facebook
  static const IconData facebook = Icons.facebook;
  
  /// أيقونة Twitter
  static const IconData twitter = Icons.alternate_email;
  
  /// أيقونة Instagram
  static const IconData instagram = Icons.camera_alt;
  
  /// أيقونة LinkedIn
  static const IconData linkedin = Icons.business;
  
  /// أيقونة WhatsApp
  static const IconData whatsapp = Icons.chat;
  
  /// أيقونة Telegram
  static const IconData telegram = Icons.send;
  
  /// أيقونة YouTube
  static const IconData youtube = Icons.play_circle;
  
  /// أيقونة TikTok
  static const IconData tiktok = Icons.music_video;
  
  /// أيقونة Snapchat
  static const IconData snapchat = Icons.camera;
  
  /// أيقونة PayPal
  static const IconData paypal = Icons.account_balance_wallet;
  
  /// أيقونة Visa
  static const IconData visa = Icons.credit_card;
  
  /// أيقونة MasterCard
  static const IconData mastercard = Icons.payment;
  
  /// أيقونة American Express
  static const IconData amex = Icons.credit_card_outlined;
  
  /// أيقونة K-Net
  static const IconData knet = Icons.account_balance;
  
  /// أيقونة Bitcoin
  static const IconData bitcoin = Icons.currency_bitcoin;
  
  /// أيقونة Ethereum
  static const IconData ethereum = Icons.diamond;
  
  /// أيقونة Salon
  static const IconData salon = Icons.content_cut;
  
  /// أيقونة Barbershop
  static const IconData barbershop = Icons.face;
  
  /// أيقونة Spa
  static const IconData spa = Icons.spa;
  
  /// أيقونة Fitness
  static const IconData fitness = Icons.fitness_center;
  
  /// أيقونة Beauty
  static const IconData beauty = Icons.face_retouching_natural;
  
  /// أيقونة Massage
  static const IconData massage = Icons.healing;
  
  /// أيقونة Nails
  static const IconData nails = Icons.colorize;
  
  /// أيقونة Hair
  static const IconData hair = Icons.content_cut;
  
  /// أيقونة Makeup
  static const IconData makeup = Icons.brush;
  
  /// أيقونة Skincare
  static const IconData skincare = Icons.face;
  
  /// أيقونة Eyebrows
  static const IconData eyebrows = Icons.remove_red_eye;
  
  /// أيقونة Eyelashes
  static const IconData eyelashes = Icons.visibility;
  
  /// أيقونة Tattoo
  static const IconData tattoo = Icons.brush;
  
  /// أيقونة Piercing
  static const IconData piercing = Icons.circle;
  
  /// أيقونة Waxing
  static const IconData waxing = Icons.cleaning_services;
  
  /// أيقونة Threading
  static const IconData threading = Icons.linear_scale;
  
  /// أيقونة Facial
  static const IconData facial = Icons.face_retouching_natural;
  
  /// أيقونة Pedicure
  static const IconData pedicure = Icons.self_improvement;
  
  /// أيقونة Manicure
  static const IconData manicure = Icons.pan_tool;
  
  /// أيقونة Yoga
  static const IconData yoga = Icons.self_improvement;
  
  /// أيقونة Pilates
  static const IconData pilates = Icons.accessibility_new;
  
  /// أيقونة Gym
  static const IconData gym = Icons.fitness_center;
  
  /// أيقونة Swimming
  static const IconData swimming = Icons.pool;
  
  /// أيقونة Running
  static const IconData running = Icons.directions_run;
  
  /// أيقونة Cycling
  static const IconData cycling = Icons.directions_bike;
  
  /// أيقونة Boxing
  static const IconData boxing = Icons.sports_mma;
  
  /// أيقونة Martial Arts
  static const IconData martialArts = Icons.sports_kabaddi;
  
  /// أيقونة Dance
  static const IconData dance = Icons.music_note;
  
  /// أيقونة Zumba
  static const IconData zumba = Icons.music_video;
  
  /// أيقونة Crossfit
  static const IconData crossfit = Icons.fitness_center;
  
  /// أيقونة Personal Training
  static const IconData personalTraining = Icons.person;
  
  /// أيقونة Group Training
  static const IconData groupTraining = Icons.group;
  
  /// أيقونة Nutrition
  static const IconData nutrition = Icons.restaurant;
  
  /// أيقونة Diet
  static const IconData diet = Icons.local_dining;
  
  /// أيقونة Supplements
  static const IconData supplements = Icons.medication;
  
  /// أيقونة Wellness
  static const IconData wellness = Icons.favorite;
  
  /// أيقونة Meditation
  static const IconData meditation = Icons.self_improvement;
  
  /// أيقونة Therapy
  static const IconData therapy = Icons.psychology;
  
  /// أيقونة Consultation
  static const IconData consultation = Icons.chat;
  
  /// أيقونة Appointment
  static const IconData appointment = Icons.event;
  
  /// أيقونة Calendar
  static const IconData calendar = Icons.calendar_today;
  
  /// أيقونة Time
  static const IconData time = Icons.access_time;
  
  /// أيقونة Duration
  static const IconData duration = Icons.timer;
  
  /// أيقونة Price
  static const IconData price = Icons.attach_money;
  
  /// أيقونة Discount
  static const IconData discount = Icons.local_offer;
  
  /// أيقونة Coupon
  static const IconData coupon = Icons.confirmation_number;
  
  /// أيقونة Gift
  static const IconData gift = Icons.card_giftcard;
  
  /// أيقونة Loyalty
  static const IconData loyalty = Icons.loyalty;
  
  /// أيقونة Points
  static const IconData points = Icons.stars;
  
  /// أيقونة Rewards
  static const IconData rewards = Icons.emoji_events;
  
  /// أيقونة Badge
  static const IconData badge = Icons.military_tech;
  
  /// أيقونة Achievement
  static const IconData achievement = Icons.workspace_premium;
  
  /// أيقونة Certificate
  static const IconData certificate = Icons.verified;
  
  /// أيقونة Award
  static const IconData award = Icons.emoji_events;
  
  /// أيقونة Trophy
  static const IconData trophy = Icons.emoji_events;
  
  /// أيقونة Medal
  static const IconData medal = Icons.military_tech;
  
  /// أيقونة Crown
  static const IconData crown = Icons.workspace_premium;
  
  /// أيقونة Diamond
  static const IconData diamond = Icons.diamond;
  
  /// أيقونة Premium
  static const IconData premium = Icons.workspace_premium;
  
  /// أيقونة VIP
  static const IconData vip = Icons.star;
  
  /// أيقونة Elite
  static const IconData elite = Icons.stars;
  
  /// أيقونة Pro
  static const IconData pro = Icons.verified_user;
  
  /// أيقونة Plus
  static const IconData plus = Icons.add_circle;
  
  /// أيقونة Max
  static const IconData max = Icons.trending_up;
  
  /// أيقونة Ultra
  static const IconData ultra = Icons.flash_on;
  
  /// أيقونة Super
  static const IconData super_ = Icons.bolt;
  
  /// أيقونة Mega
  static const IconData mega = Icons.whatshot;
  
  /// أيقونة Giga
  static const IconData giga = Icons.speed;
  
  /// أيقونة Tera
  static const IconData tera = Icons.rocket_launch;
}

/// مساعد للحصول على الأيقونات حسب النوع
class IconHelper {
  /// الحصول على أيقونة نوع الخدمة
  static IconData getServiceTypeIcon(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'salon':
        return CustomIcons.salon;
      case 'barbershop':
        return CustomIcons.barbershop;
      case 'spa':
        return CustomIcons.spa;
      case 'fitness':
        return CustomIcons.fitness;
      case 'beauty':
        return CustomIcons.beauty;
      case 'massage':
        return CustomIcons.massage;
      case 'nails':
        return CustomIcons.nails;
      case 'hair':
        return CustomIcons.hair;
      case 'makeup':
        return CustomIcons.makeup;
      case 'skincare':
        return CustomIcons.skincare;
      default:
        return Icons.business;
    }
  }

  /// الحصول على أيقونة طريقة الدفع
  static IconData getPaymentMethodIcon(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'google':
      case 'googlepay':
        return CustomIcons.google;
      case 'apple':
      case 'applepay':
        return CustomIcons.apple;
      case 'paypal':
        return CustomIcons.paypal;
      case 'visa':
        return CustomIcons.visa;
      case 'mastercard':
        return CustomIcons.mastercard;
      case 'knet':
        return CustomIcons.knet;
      default:
        return Icons.payment;
    }
  }

  /// الحصول على أيقونة وسائل التواصل الاجتماعي
  static IconData getSocialMediaIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return CustomIcons.facebook;
      case 'twitter':
        return CustomIcons.twitter;
      case 'instagram':
        return CustomIcons.instagram;
      case 'linkedin':
        return CustomIcons.linkedin;
      case 'whatsapp':
        return CustomIcons.whatsapp;
      case 'telegram':
        return CustomIcons.telegram;
      case 'youtube':
        return CustomIcons.youtube;
      case 'tiktok':
        return CustomIcons.tiktok;
      case 'snapchat':
        return CustomIcons.snapchat;
      default:
        return Icons.link;
    }
  }
}
