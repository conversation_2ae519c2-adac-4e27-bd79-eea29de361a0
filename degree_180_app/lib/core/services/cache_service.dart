import 'dart:convert';
// import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:path_provider/path_provider.dart';
import '../models/user_model.dart';
import '../models/service_provider_model.dart';
import '../models/service_model.dart';
import '../models/booking_model.dart';

class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  late SharedPreferences _prefs;
  late Box<dynamic> _hiveBox;
  late Box<String> _imageCache;
  late Box<Map<dynamic, dynamic>> _dataCache;

  // Cache keys
  static const String _userKey = 'cached_user';
  static const String _providersKey = 'cached_providers';
  static const String _servicesKey = 'cached_services';
  static const String _bookingsKey = 'cached_bookings';
  static const String _searchHistoryKey = 'search_history';
  static const String _favoritesKey = 'favorites';
  static const String _settingsKey = 'app_settings';
  static const String _onboardingKey = 'onboarding_completed';

  // Cache expiration times (in hours)
  static const int _userCacheExpiry = 24;
  static const int _providersCacheExpiry = 6;
  static const int _servicesCacheExpiry = 12;
  static const int _bookingsCacheExpiry = 1;
  static const int _imageCacheExpiry = 168; // 1 week

  Future<void> initialize() async {
    try {
      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();
      
      // Initialize Hive
      await Hive.initFlutter();
      
      // Open Hive boxes
      _hiveBox = await Hive.openBox('app_cache');
      _imageCache = await Hive.openBox<String>('image_cache');
      _dataCache = await Hive.openBox<Map<dynamic, dynamic>>('data_cache');
      
      // Clean expired cache on startup
      await _cleanExpiredCache();
      
      debugPrint('CacheService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing CacheService: $e');
    }
  }

  // User Cache
  Future<void> cacheUser(UserModel user) async {
    try {
      final cacheData = {
        'data': user.toJson(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await _hiveBox.put(_userKey, cacheData);
    } catch (e) {
      debugPrint('Error caching user: $e');
    }
  }

  Future<UserModel?> getCachedUser() async {
    try {
      final cacheData = _hiveBox.get(_userKey);
      if (cacheData == null) return null;

      final timestamp = cacheData['timestamp'] as int;
      if (_isExpired(timestamp, _userCacheExpiry)) {
        await _hiveBox.delete(_userKey);
        return null;
      }

      return UserModel.fromJson(Map<String, dynamic>.from(cacheData['data']));
    } catch (e) {
      debugPrint('Error getting cached user: $e');
      return null;
    }
  }

  // Service Providers Cache
  Future<void> cacheProviders(List<ServiceProviderModel> providers) async {
    try {
      final cacheData = {
        'data': providers.map((p) => p.toJson()).toList(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await _dataCache.put(_providersKey, cacheData);
    } catch (e) {
      debugPrint('Error caching providers: $e');
    }
  }

  Future<List<ServiceProviderModel>?> getCachedProviders() async {
    try {
      final cacheData = _dataCache.get(_providersKey);
      if (cacheData == null) return null;

      final timestamp = cacheData['timestamp'] as int;
      if (_isExpired(timestamp, _providersCacheExpiry)) {
        await _dataCache.delete(_providersKey);
        return null;
      }

      final data = List<Map<String, dynamic>>.from(cacheData['data']);
      return data.map((json) => ServiceProviderModel.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting cached providers: $e');
      return null;
    }
  }

  // Services Cache
  Future<void> cacheServices(List<ServiceModel> services) async {
    try {
      final cacheData = {
        'data': services.map((s) => s.toJson()).toList(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await _dataCache.put(_servicesKey, cacheData);
    } catch (e) {
      debugPrint('Error caching services: $e');
    }
  }

  Future<List<ServiceModel>?> getCachedServices() async {
    try {
      final cacheData = _dataCache.get(_servicesKey);
      if (cacheData == null) return null;

      final timestamp = cacheData['timestamp'] as int;
      if (_isExpired(timestamp, _servicesCacheExpiry)) {
        await _dataCache.delete(_servicesKey);
        return null;
      }

      final data = List<Map<String, dynamic>>.from(cacheData['data']);
      return data.map((json) => ServiceModel.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting cached services: $e');
      return null;
    }
  }

  // Bookings Cache
  Future<void> cacheBookings(List<BookingModel> bookings) async {
    try {
      final cacheData = {
        'data': bookings.map((b) => b.toJson()).toList(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await _dataCache.put(_bookingsKey, cacheData);
    } catch (e) {
      debugPrint('Error caching bookings: $e');
    }
  }

  Future<List<BookingModel>?> getCachedBookings() async {
    try {
      final cacheData = _dataCache.get(_bookingsKey);
      if (cacheData == null) return null;

      final timestamp = cacheData['timestamp'] as int;
      if (_isExpired(timestamp, _bookingsCacheExpiry)) {
        await _dataCache.delete(_bookingsKey);
        return null;
      }

      final data = List<Map<String, dynamic>>.from(cacheData['data']);
      return data.map((json) => BookingModel.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting cached bookings: $e');
      return null;
    }
  }

  // Search History
  Future<void> addToSearchHistory(String query) async {
    try {
      final history = await getSearchHistory();
      history.remove(query); // Remove if exists to avoid duplicates
      history.insert(0, query); // Add to beginning
      
      // Keep only last 20 searches
      if (history.length > 20) {
        history.removeRange(20, history.length);
      }
      
      await _prefs.setStringList(_searchHistoryKey, history);
    } catch (e) {
      debugPrint('Error adding to search history: $e');
    }
  }

  Future<List<String>> getSearchHistory() async {
    try {
      return _prefs.getStringList(_searchHistoryKey) ?? [];
    } catch (e) {
      debugPrint('Error getting search history: $e');
      return [];
    }
  }

  Future<void> clearSearchHistory() async {
    try {
      await _prefs.remove(_searchHistoryKey);
    } catch (e) {
      debugPrint('Error clearing search history: $e');
    }
  }

  // Favorites
  Future<void> addToFavorites(String itemId, String itemType) async {
    try {
      final favorites = await getFavorites();
      final favoriteItem = '$itemType:$itemId';
      
      if (!favorites.contains(favoriteItem)) {
        favorites.add(favoriteItem);
        await _prefs.setStringList(_favoritesKey, favorites);
      }
    } catch (e) {
      debugPrint('Error adding to favorites: $e');
    }
  }

  Future<void> removeFromFavorites(String itemId, String itemType) async {
    try {
      final favorites = await getFavorites();
      final favoriteItem = '$itemType:$itemId';
      
      favorites.remove(favoriteItem);
      await _prefs.setStringList(_favoritesKey, favorites);
    } catch (e) {
      debugPrint('Error removing from favorites: $e');
    }
  }

  Future<List<String>> getFavorites() async {
    try {
      return _prefs.getStringList(_favoritesKey) ?? [];
    } catch (e) {
      debugPrint('Error getting favorites: $e');
      return [];
    }
  }

  Future<bool> isFavorite(String itemId, String itemType) async {
    try {
      final favorites = await getFavorites();
      return favorites.contains('$itemType:$itemId');
    } catch (e) {
      debugPrint('Error checking favorite: $e');
      return false;
    }
  }

  // App Settings
  Future<void> saveSetting(String key, dynamic value) async {
    try {
      final settings = await getSettings();
      settings[key] = value;
      await _prefs.setString(_settingsKey, jsonEncode(settings));
    } catch (e) {
      debugPrint('Error saving setting: $e');
    }
  }

  Future<T?> getSetting<T>(String key) async {
    try {
      final settings = await getSettings();
      return settings[key] as T?;
    } catch (e) {
      debugPrint('Error getting setting: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>> getSettings() async {
    try {
      final settingsString = _prefs.getString(_settingsKey);
      if (settingsString == null) return {};
      return Map<String, dynamic>.from(jsonDecode(settingsString));
    } catch (e) {
      debugPrint('Error getting settings: $e');
      return {};
    }
  }

  // Onboarding
  Future<void> setOnboardingCompleted(bool completed) async {
    try {
      await _prefs.setBool(_onboardingKey, completed);
    } catch (e) {
      debugPrint('Error setting onboarding: $e');
    }
  }

  Future<bool> isOnboardingCompleted() async {
    try {
      return _prefs.getBool(_onboardingKey) ?? false;
    } catch (e) {
      debugPrint('Error getting onboarding status: $e');
      return false;
    }
  }

  // Image Cache
  Future<void> cacheImage(String url, String base64Data) async {
    try {
      final cacheData = {
        'data': base64Data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await _imageCache.put(url, jsonEncode(cacheData));
    } catch (e) {
      debugPrint('Error caching image: $e');
    }
  }

  Future<String?> getCachedImage(String url) async {
    try {
      final cacheDataString = _imageCache.get(url);
      if (cacheDataString == null) return null;

      final cacheData = jsonDecode(cacheDataString);
      final timestamp = cacheData['timestamp'] as int;
      
      if (_isExpired(timestamp, _imageCacheExpiry)) {
        await _imageCache.delete(url);
        return null;
      }

      return cacheData['data'] as String;
    } catch (e) {
      debugPrint('Error getting cached image: $e');
      return null;
    }
  }

  // Generic Cache Methods
  Future<void> cacheData(String key, Map<String, dynamic> data, {int? expiryHours}) async {
    try {
      final cacheData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiry_hours': expiryHours ?? 24,
      };
      await _dataCache.put(key, cacheData);
    } catch (e) {
      debugPrint('Error caching data for key $key: $e');
    }
  }

  Future<Map<String, dynamic>?> getCachedData(String key) async {
    try {
      final cacheData = _dataCache.get(key);
      if (cacheData == null) return null;

      final timestamp = cacheData['timestamp'] as int;
      final expiryHours = cacheData['expiry_hours'] as int;
      
      if (_isExpired(timestamp, expiryHours)) {
        await _dataCache.delete(key);
        return null;
      }

      return Map<String, dynamic>.from(cacheData['data']);
    } catch (e) {
      debugPrint('Error getting cached data for key $key: $e');
      return null;
    }
  }

  // Cache Management
  Future<void> clearAllCache() async {
    try {
      await _hiveBox.clear();
      await _imageCache.clear();
      await _dataCache.clear();
      await _prefs.clear();
      debugPrint('All cache cleared');
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }

  Future<void> clearExpiredCache() async {
    await _cleanExpiredCache();
  }

  Future<int> getCacheSize() async {
    try {
      int totalSize = 0;
      
      // Calculate Hive cache size
      final hiveKeys = _hiveBox.keys;
      for (final key in hiveKeys) {
        final value = _hiveBox.get(key);
        totalSize += jsonEncode(value).length;
      }
      
      // Calculate image cache size
      final imageKeys = _imageCache.keys;
      for (final key in imageKeys) {
        final value = _imageCache.get(key);
        if (value != null) {
          totalSize += value.length;
        }
      }
      
      // Calculate data cache size
      final dataKeys = _dataCache.keys;
      for (final key in dataKeys) {
        final value = _dataCache.get(key);
        if (value != null) {
          totalSize += jsonEncode(value).length;
        }
      }
      
      return totalSize;
    } catch (e) {
      debugPrint('Error calculating cache size: $e');
      return 0;
    }
  }

  // Private Methods
  bool _isExpired(int timestamp, int expiryHours) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final expiryTime = timestamp + (expiryHours * 60 * 60 * 1000);
    return now > expiryTime;
  }

  Future<void> _cleanExpiredCache() async {
    try {
      // Clean Hive cache
      final hiveKeys = _hiveBox.keys.toList();
      for (final key in hiveKeys) {
        final value = _hiveBox.get(key);
        if (value is Map && value.containsKey('timestamp')) {
          final timestamp = value['timestamp'] as int;
          final expiryHours = value['expiry_hours'] as int? ?? 24;
          if (_isExpired(timestamp, expiryHours)) {
            await _hiveBox.delete(key);
          }
        }
      }
      
      // Clean image cache
      final imageKeys = _imageCache.keys.toList();
      for (final key in imageKeys) {
        final value = _imageCache.get(key);
        if (value != null) {
          try {
            final cacheData = jsonDecode(value);
            final timestamp = cacheData['timestamp'] as int;
            if (_isExpired(timestamp, _imageCacheExpiry)) {
              await _imageCache.delete(key);
            }
          } catch (e) {
            // Invalid cache data, delete it
            await _imageCache.delete(key);
          }
        }
      }
      
      // Clean data cache
      final dataKeys = _dataCache.keys.toList();
      for (final key in dataKeys) {
        final value = _dataCache.get(key);
        if (value != null && value.containsKey('timestamp')) {
          final timestamp = value['timestamp'] as int;
          final expiryHours = value['expiry_hours'] as int? ?? 24;
          if (_isExpired(timestamp, expiryHours)) {
            await _dataCache.delete(key);
          }
        }
      }
      
      debugPrint('Expired cache cleaned');
    } catch (e) {
      debugPrint('Error cleaning expired cache: $e');
    }
  }

  Future<void> dispose() async {
    try {
      await _hiveBox.close();
      await _imageCache.close();
      await _dataCache.close();
    } catch (e) {
      debugPrint('Error disposing CacheService: $e');
    }
  }
}
