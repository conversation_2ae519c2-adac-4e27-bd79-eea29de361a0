import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';

/// ويدجت محسن لعرض الصور مع تحميل ذكي وتخزين مؤقت
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final Duration? fadeInDuration;
  final int? memCacheWidth;
  final int? memCacheHeight;
  final Map<String, String>? httpHeaders;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.fadeInDuration,
    this.memCacheWidth,
    this.memCacheHeight,
    this.httpHeaders,
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrl.isEmpty) {
      return _buildErrorWidget();
    }

    final optimizedUrl = _getOptimizedUrl();
    final effectiveMemCacheWidth = memCacheWidth ?? width?.toInt();
    final effectiveMemCacheHeight = memCacheHeight ?? height?.toInt();

    Widget imageWidget = CachedNetworkImage(
      imageUrl: optimizedUrl,
      width: width,
      height: height,
      fit: fit,
      memCacheWidth: effectiveMemCacheWidth,
      memCacheHeight: effectiveMemCacheHeight,
      httpHeaders: httpHeaders,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      fadeInDuration: fadeInDuration ?? const Duration(milliseconds: 300),
      useOldImageOnUrlChange: true,
      cacheManager: enableDiskCache ? null : null, // استخدام الافتراضي
    );

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// تحسين URL الصورة بناءً على الأبعاد المطلوبة
  String _getOptimizedUrl() {
    if (imageUrl.isEmpty) return imageUrl;

    // إذا كانت الصورة من Firebase Storage، إضافة معاملات التحسين
    if (imageUrl.contains('firebasestorage.googleapis.com')) {
      final uri = Uri.parse(imageUrl);
      final queryParams = Map<String, String>.from(uri.queryParameters);

      // إضافة معاملات التحسين
      if (width != null) {
        queryParams['w'] = width!.toInt().toString();
      }
      if (height != null) {
        queryParams['h'] = height!.toInt().toString();
      }
      
      // جودة الصورة (80% للتوازن بين الجودة والحجم)
      queryParams['q'] = '80';
      
      // تنسيق الصورة (WebP للمتصفحات المدعومة)
      queryParams['f'] = 'webp';

      return uri.replace(queryParameters: queryParams).toString();
    }

    // للصور الأخرى، إرجاع URL الأصلي
    return imageUrl;
  }

  /// بناء ويدجت التحميل
  Widget _buildPlaceholder() {
    if (placeholder != null) {
      return placeholder!;
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: AppColors.backgroundGray,
        borderRadius: borderRadius,
      ),
      child: const Center(
        child: ShimmerPlaceholder(),
      ),
    );
  }

  /// بناء ويدجت الخطأ
  Widget _buildErrorWidget() {
    if (errorWidget != null) {
      return errorWidget!;
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: AppColors.backgroundGray,
        borderRadius: borderRadius,
        border: Border.all(
          color: AppColors.textGray.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_outlined,
            size: (width != null && width! < 100) ? 24 : 48,
            color: AppColors.textGray.withValues(alpha: 0.6),
          ),
          if (width == null || width! >= 100) ...[
            const SizedBox(height: 8),
            Text(
              'فشل تحميل الصورة',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: AppColors.textGray.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// ويدجت Shimmer للتحميل
class ShimmerPlaceholder extends StatefulWidget {
  final Color? baseColor;
  final Color? highlightColor;
  final Duration duration;

  const ShimmerPlaceholder({
    super.key,
    this.baseColor,
    this.highlightColor,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<ShimmerPlaceholder> createState() => _ShimmerPlaceholderState();
}

class _ShimmerPlaceholderState extends State<ShimmerPlaceholder>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final baseColor = widget.baseColor ?? AppColors.backgroundGray;
    final highlightColor = widget.highlightColor ?? 
        AppColors.backgroundWhite.withValues(alpha: 0.8);

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ),
          ),
        );
      },
    );
  }
}

/// ويدجت صورة دائرية محسنة
class OptimizedCircleImage extends StatelessWidget {
  final String imageUrl;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Border? border;

  const OptimizedCircleImage({
    super.key,
    required this.imageUrl,
    required this.radius,
    this.placeholder,
    this.errorWidget,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: border,
      ),
      child: ClipOval(
        child: OptimizedImage(
          imageUrl: imageUrl,
          width: radius * 2,
          height: radius * 2,
          fit: BoxFit.cover,
          placeholder: placeholder,
          errorWidget: errorWidget,
        ),
      ),
    );
  }
}

/// ويدجت صورة مع تأثير التدرج
class OptimizedGradientImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final Gradient gradient;
  final BlendMode blendMode;
  final Widget? child;

  const OptimizedGradientImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    required this.gradient,
    this.blendMode = BlendMode.darken,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      child: Stack(
        fit: StackFit.expand,
        children: [
          OptimizedImage(
            imageUrl: imageUrl,
            width: width,
            height: height,
            fit: BoxFit.cover,
          ),
          Container(
            decoration: BoxDecoration(
              gradient: gradient,
            ),
          ),
          if (child != null) child!,
        ],
      ),
    );
  }
}

/// ويدجت صورة مع تأثير الضبابية
class OptimizedBlurredImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final double sigmaX;
  final double sigmaY;
  final Widget? child;

  const OptimizedBlurredImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.sigmaX = 5.0,
    this.sigmaY = 5.0,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      child: Stack(
        fit: StackFit.expand,
        children: [
          OptimizedImage(
            imageUrl: imageUrl,
            width: width,
            height: height,
            fit: BoxFit.cover,
          ),
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: sigmaX, sigmaY: sigmaY),
            child: Container(
              color: Colors.black.withValues(alpha: 0.1),
            ),
          ),
          if (child != null) child!,
        ],
      ),
    );
  }
}
