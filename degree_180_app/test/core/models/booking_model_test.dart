import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:degree_180_app/core/models/booking_model.dart';
import 'package:degree_180_app/core/models/service_model.dart';
import 'package:degree_180_app/core/models/location_model.dart';
import 'package:degree_180_app/core/enums/booking_status.dart';

void main() {
  group('BookingModel', () {
    late BookingModel testBooking;
    late ServiceModel testService;
    late DateTime testDate;
    late DateTime futureDate;
    late DateTime pastDate;

    setUp(() {
      testDate = DateTime(2024, 1, 1);
      futureDate = DateTime.now().add(const Duration(days: 1));
      pastDate = DateTime.now().subtract(const Duration(days: 1));
      
      testService = ServiceModel(
        id: 'service-1',
        name: 'Hair Cut',
        description: 'Professional hair cutting service',
        price: 25.0,
        durationMinutes: 60,
        category: 'Hair',
        createdAt: testDate,
        updatedAt: testDate,
      );

      testBooking = BookingModel(
        id: 'booking-1',
        customerId: 'customer-1',
        customerName: '<PERSON>',
        customerPhone: '+**********',
        customerEmail: '<EMAIL>',
        providerId: 'provider-1',
        providerName: 'Test Provider',
        serviceId: 'service-1',
        serviceName: 'Test Service',
        service: testService,
        bookingDate: futureDate,
        appointmentDateTime: futureDate.add(const Duration(hours: 10)),
        timeSlot: '10:00 - 11:00',
        status: BookingStatus.confirmed,
        totalAmount: 25.0,
        currency: 'KD',
        discountAmount: 5.0,
        couponCode: 'DISCOUNT20',
        notes: 'Please be on time',
        location: const LocationModel(
          latitude: 29.3759,
          longitude: 47.9774,
          address: 'Test Address',
        ),
        paymentInfo: {'method': 'credit_card'},
        isReminderSent: false,
        isReviewSubmitted: false,
        createdAt: testDate,
        updatedAt: testDate,
      );
    });

    group('Constructor', () {
      test('should create BookingModel with required fields', () {
        final booking = BookingModel(
          id: 'booking-1',
          customerId: 'customer-1',
          customerName: 'John Doe',
          providerId: 'provider-1',
          providerName: 'Test Provider',
          serviceId: 'service-1',
          serviceName: 'Test Service',
          service: testService,
          bookingDate: futureDate,
          appointmentDateTime: futureDate.add(const Duration(hours: 10)),
          timeSlot: '10:00 - 11:00',
          status: BookingStatus.pending,
          totalAmount: 25.0,
          createdAt: testDate,
          updatedAt: testDate,
        );

        expect(booking.id, 'booking-1');
        expect(booking.customerId, 'customer-1');
        expect(booking.customerName, 'John Doe');
        expect(booking.providerId, 'provider-1');
        expect(booking.providerName, 'Test Provider');
        expect(booking.serviceId, 'service-1');
        expect(booking.service, testService);
        expect(booking.bookingDate, futureDate);
        expect(booking.timeSlot, '10:00 - 11:00');
        expect(booking.status, BookingStatus.pending);
        expect(booking.totalAmount, 25.0);
        expect(booking.currency, 'KD');
        expect(booking.isReminderSent, false);
        expect(booking.isReviewSubmitted, false);
      });

      test('should create BookingModel with all fields', () {
        expect(testBooking.id, 'booking-1');
        expect(testBooking.customerId, 'customer-1');
        expect(testBooking.customerName, 'John Doe');
        expect(testBooking.customerPhone, '+**********');
        expect(testBooking.customerEmail, '<EMAIL>');
        expect(testBooking.providerId, 'provider-1');
        expect(testBooking.providerName, 'Test Provider');
        expect(testBooking.serviceId, 'service-1');
        expect(testBooking.service, testService);
        expect(testBooking.bookingDate, futureDate);
        expect(testBooking.timeSlot, '10:00 - 11:00');
        expect(testBooking.status, BookingStatus.confirmed);
        expect(testBooking.totalAmount, 25.0);
        expect(testBooking.currency, 'KD');
        expect(testBooking.discountAmount, 5.0);
        expect(testBooking.couponCode, 'DISCOUNT20');
        expect(testBooking.notes, 'Please be on time');
        expect(testBooking.location, isNotNull);
        expect(testBooking.paymentInfo, {'method': 'credit_card'});
        expect(testBooking.isReminderSent, false);
        expect(testBooking.isReviewSubmitted, false);
        expect(testBooking.createdAt, testDate);
        expect(testBooking.updatedAt, testDate);
      });
    });

    group('JSON Serialization', () {
      test('should convert to JSON correctly', () {
        final json = testBooking.toJson();

        expect(json['id'], 'booking-1');
        expect(json['customerId'], 'customer-1');
        expect(json['customerName'], 'John Doe');
        expect(json['customerPhone'], '+**********');
        expect(json['customerEmail'], '<EMAIL>');
        expect(json['providerId'], 'provider-1');
        expect(json['providerName'], 'Test Provider');
        expect(json['serviceId'], 'service-1');
        expect(json['service'], isNotNull);
        expect(json['bookingDate'], isA<Timestamp>());
        expect(json['timeSlot'], '10:00 - 11:00');
        expect(json['status'], 'confirmed');
        expect(json['totalAmount'], 25.0);
        expect(json['currency'], 'KD');
        expect(json['discountAmount'], 5.0);
        expect(json['couponCode'], 'DISCOUNT20');
        expect(json['notes'], 'Please be on time');
        expect(json['location'], isNotNull);
        expect(json['paymentInfo'], {'method': 'credit_card'});
        expect(json['isReminderSent'], false);
        expect(json['isReviewSubmitted'], false);
        expect(json['createdAt'], isA<Timestamp>());
        expect(json['updatedAt'], isA<Timestamp>());
      });

      test('should create from JSON correctly', () {
        final json = testBooking.toJson();
        final fromJson = BookingModel.fromJson(json);

        expect(fromJson.id, testBooking.id);
        expect(fromJson.customerId, testBooking.customerId);
        expect(fromJson.customerName, testBooking.customerName);
        expect(fromJson.customerPhone, testBooking.customerPhone);
        expect(fromJson.customerEmail, testBooking.customerEmail);
        expect(fromJson.providerId, testBooking.providerId);
        expect(fromJson.providerName, testBooking.providerName);
        expect(fromJson.serviceId, testBooking.serviceId);
        expect(fromJson.timeSlot, testBooking.timeSlot);
        expect(fromJson.status, testBooking.status);
        expect(fromJson.totalAmount, testBooking.totalAmount);
        expect(fromJson.currency, testBooking.currency);
        expect(fromJson.discountAmount, testBooking.discountAmount);
        expect(fromJson.couponCode, testBooking.couponCode);
        expect(fromJson.notes, testBooking.notes);
        expect(fromJson.isReminderSent, testBooking.isReminderSent);
        expect(fromJson.isReviewSubmitted, testBooking.isReviewSubmitted);
      });

      test('should handle missing optional fields in JSON', () {
        final minimalJson = {
          'id': 'booking-1',
          'customerId': 'customer-1',
          'customerName': 'John Doe',
          'providerId': 'provider-1',
          'providerName': 'Test Provider',
          'serviceId': 'service-1',
          'service': testService.toJson(),
          'bookingDate': Timestamp.fromDate(futureDate),
          'timeSlot': '10:00 - 11:00',
          'status': 'pending',
          'totalAmount': 25.0,
          'createdAt': Timestamp.fromDate(testDate),
          'updatedAt': Timestamp.fromDate(testDate),
        };

        final booking = BookingModel.fromJson(minimalJson);

        expect(booking.id, 'booking-1');
        expect(booking.customerId, 'customer-1');
        expect(booking.customerName, 'John Doe');
        expect(booking.customerPhone, isNull);
        expect(booking.customerEmail, isNull);
        expect(booking.status, BookingStatus.pending);
        expect(booking.currency, 'KD');
        expect(booking.discountAmount, isNull);
        expect(booking.couponCode, isNull);
        expect(booking.notes, isNull);
        expect(booking.location, isNull);
        expect(booking.paymentInfo, isNull);
        expect(booking.isReminderSent, false);
        expect(booking.isReviewSubmitted, false);
      });
    });

    group('Computed Properties', () {
      test('formattedDate should return correct format', () {
        final booking = testBooking.copyWith(
          bookingDate: DateTime(2024, 12, 25),
        );
        
        expect(booking.formattedDate, '25/12/2024');
      });

      test('formattedTime should return time slot', () {
        expect(testBooking.formattedTime, '10:00 - 11:00');
      });

      test('formattedAmount should return formatted amount with currency', () {
        expect(testBooking.formattedAmount, '25.0 KD');
      });

      test('statusDisplayName should return correct status name', () {
        expect(testBooking.statusDisplayName, 'Confirmed');
        
        final pendingBooking = testBooking.copyWith(status: BookingStatus.pending);
        expect(pendingBooking.statusDisplayName, 'Pending');
        
        final completedBooking = testBooking.copyWith(status: BookingStatus.completed);
        expect(completedBooking.statusDisplayName, 'Completed');
        
        final cancelledBooking = testBooking.copyWith(status: BookingStatus.cancelled);
        expect(cancelledBooking.statusDisplayName, 'Cancelled');
      });

      test('canBeCancelled should return correct value based on status', () {
        final pendingBooking = testBooking.copyWith(status: BookingStatus.pending);
        expect(pendingBooking.canBeCancelled, true);
        
        final confirmedBooking = testBooking.copyWith(status: BookingStatus.confirmed);
        expect(confirmedBooking.canBeCancelled, true);
        
        final completedBooking = testBooking.copyWith(status: BookingStatus.completed);
        expect(completedBooking.canBeCancelled, false);
        
        final cancelledBooking = testBooking.copyWith(status: BookingStatus.cancelled);
        expect(cancelledBooking.canBeCancelled, false);
      });

      test('canBeModified should return correct value based on status', () {
        final pendingBooking = testBooking.copyWith(status: BookingStatus.pending);
        expect(pendingBooking.canBeModified, true);
        
        final confirmedBooking = testBooking.copyWith(status: BookingStatus.confirmed);
        expect(confirmedBooking.canBeModified, false);
        
        final completedBooking = testBooking.copyWith(status: BookingStatus.completed);
        expect(completedBooking.canBeModified, false);
      });

      test('isUpcoming should return correct value based on date and status', () {
        final upcomingBooking = testBooking.copyWith(
          bookingDate: futureDate,
          status: BookingStatus.confirmed,
        );
        expect(upcomingBooking.isUpcoming, true);
        
        final pastBooking = testBooking.copyWith(
          bookingDate: pastDate,
          status: BookingStatus.confirmed,
        );
        expect(pastBooking.isUpcoming, false);
        
        final cancelledBooking = testBooking.copyWith(
          bookingDate: futureDate,
          status: BookingStatus.cancelled,
        );
        expect(cancelledBooking.isUpcoming, false);
      });

      test('isPast should return correct value based on date', () {
        final pastBooking = testBooking.copyWith(bookingDate: pastDate);
        expect(pastBooking.isPast, true);
        
        final futureBooking = testBooking.copyWith(bookingDate: futureDate);
        expect(futureBooking.isPast, false);
      });

      test('timeUntilBooking should return correct duration', () {
        final now = DateTime.now();
        final bookingIn2Hours = testBooking.copyWith(
          bookingDate: now.add(const Duration(hours: 2)),
        );
        
        final duration = bookingIn2Hours.timeUntilBooking;
        expect(duration.inHours, closeTo(2, 1)); // Allow 1 hour tolerance
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        final updated = testBooking.copyWith(
          status: BookingStatus.completed,
          totalAmount: 30.0,
          isReviewSubmitted: true,
        );

        expect(updated.status, BookingStatus.completed);
        expect(updated.totalAmount, 30.0);
        expect(updated.isReviewSubmitted, true);
        
        // Other fields should remain the same
        expect(updated.id, testBooking.id);
        expect(updated.customerId, testBooking.customerId);
        expect(updated.customerName, testBooking.customerName);
        expect(updated.providerId, testBooking.providerId);
        expect(updated.serviceId, testBooking.serviceId);
        expect(updated.bookingDate, testBooking.bookingDate);
        expect(updated.timeSlot, testBooking.timeSlot);
        expect(updated.currency, testBooking.currency);
      });
    });

    group('toString', () {
      test('should return formatted string representation', () {
        final string = testBooking.toString();
        
        expect(string, contains('BookingModel'));
        expect(string, contains('booking-1'));
        expect(string, contains('Hair Cut'));
        expect(string, contains('Confirmed'));
      });
    });
  });
}
