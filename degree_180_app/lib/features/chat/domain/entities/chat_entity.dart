import 'package:equatable/equatable.dart';
import 'message_entity.dart';

enum ChatType {
  direct,
  group,
  support,
}

enum ChatStatus {
  active,
  archived,
  blocked,
  deleted,
}

class ChatEntity extends Equatable {
  final String id;
  final ChatType type;
  final String name;
  final String? description;
  final String? imageUrl;
  final List<String> participantIds;
  final MessageEntity? lastMessage;
  final DateTime? lastMessageTime;
  final int unreadCount;
  final ChatStatus status;
  final bool isMuted;
  final bool isPinned;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  const ChatEntity({
    required this.id,
    required this.type,
    required this.name,
    this.description,
    this.imageUrl,
    required this.participantIds,
    this.lastMessage,
    this.lastMessageTime,
    this.unreadCount = 0,
    this.status = ChatStatus.active,
    this.isMuted = false,
    this.isPinned = false,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        type,
        name,
        description,
        imageUrl,
        participantIds,
        lastMessage,
        lastMessageTime,
        unreadCount,
        status,
        isMuted,
        isPinned,
        createdAt,
        updatedAt,
        metadata,
      ];

  ChatEntity copyWith({
    String? id,
    ChatType? type,
    String? name,
    String? description,
    String? imageUrl,
    List<String>? participantIds,
    MessageEntity? lastMessage,
    DateTime? lastMessageTime,
    int? unreadCount,
    ChatStatus? status,
    bool? isMuted,
    bool? isPinned,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ChatEntity(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      participantIds: participantIds ?? this.participantIds,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCount: unreadCount ?? this.unreadCount,
      status: status ?? this.status,
      isMuted: isMuted ?? this.isMuted,
      isPinned: isPinned ?? this.isPinned,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  String get typeString {
    switch (type) {
      case ChatType.direct:
        return 'direct';
      case ChatType.group:
        return 'group';
      case ChatType.support:
        return 'support';
    }
  }

  String get statusString {
    switch (status) {
      case ChatStatus.active:
        return 'active';
      case ChatStatus.archived:
        return 'archived';
      case ChatStatus.blocked:
        return 'blocked';
      case ChatStatus.deleted:
        return 'deleted';
    }
  }

  bool get isDirect => type == ChatType.direct;
  bool get isGroup => type == ChatType.group;
  bool get isSupport => type == ChatType.support;

  bool get isActive => status == ChatStatus.active;
  bool get isArchived => status == ChatStatus.archived;
  bool get isBlocked => status == ChatStatus.blocked;
  bool get isDeleted => status == ChatStatus.deleted;

  bool get hasUnreadMessages => unreadCount > 0;
  bool get hasLastMessage => lastMessage != null;

  String get displayName {
    if (name.isNotEmpty) return name;
    if (isDirect && participantIds.length == 2) {
      return 'Direct Chat';
    }
    return 'Chat';
  }

  String get lastMessagePreview {
    if (lastMessage == null) return 'No messages yet';
    return lastMessage!.displayContent;
  }

  String get lastMessageTimeFormatted {
    if (lastMessageTime == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(lastMessageTime!);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get unreadCountDisplay {
    if (unreadCount == 0) return '';
    if (unreadCount > 99) return '99+';
    return unreadCount.toString();
  }

  bool canSendMessage(String userId) {
    return isActive && 
           !isBlocked && 
           !isDeleted && 
           participantIds.contains(userId);
  }

  bool canReceiveMessage(String userId) {
    return isActive && 
           !isBlocked && 
           !isDeleted && 
           participantIds.contains(userId);
  }
}
