import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// نظام التحقق المتقدم من كلمات المرور
/// يتضمن فحص القوة والتعقيد والكلمات الشائعة
class PasswordValidator {
  static const int minLength = 8;
  static const int maxLength = 128;
  static const int minScore = 3; // من 5

  // قائمة كلمات المرور الشائعة (مختصرة للمثال)
  static const List<String> _commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', 'monkey',
    '1234567890', 'iloveyou', 'princess', 'rockyou', '12345678',
    'سر', 'كلمةالمرور', '123456', 'مرحبا', 'أهلا'
  ];

  /// التحقق الشامل من كلمة المرور
  static PasswordValidationResult validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return PasswordValidationResult(
        isValid: false,
        score: 0,
        message: 'كلمة المرور مطلوبة',
        suggestions: ['يرجى إدخال كلمة مرور'],
      );
    }

    final issues = <String>[];
    final suggestions = <String>[];
    int score = 0;

    // فحص الطول
    if (password.length < minLength) {
      issues.add('كلمة المرور قصيرة جداً');
      suggestions.add('استخدم على الأقل $minLength أحرف');
    } else if (password.length >= minLength) {
      score += 1;
    }

    if (password.length > maxLength) {
      issues.add('كلمة المرور طويلة جداً');
      suggestions.add('استخدم أقل من $maxLength حرف');
    }

    // فحص التعقيد
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasDigits = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialChars = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    bool hasArabic = password.contains(RegExp(r'[\u0600-\u06FF]'));

    if (!hasLowercase && !hasArabic) {
      issues.add('لا تحتوي على أحرف صغيرة');
      suggestions.add('أضف أحرف صغيرة (a-z)');
    } else {
      score += 1;
    }

    if (!hasUppercase) {
      issues.add('لا تحتوي على أحرف كبيرة');
      suggestions.add('أضف أحرف كبيرة (A-Z)');
    } else {
      score += 1;
    }

    if (!hasDigits) {
      issues.add('لا تحتوي على أرقام');
      suggestions.add('أضف أرقام (0-9)');
    } else {
      score += 1;
    }

    if (!hasSpecialChars) {
      issues.add('لا تحتوي على رموز خاصة');
      suggestions.add('أضف رموز خاصة (!@#\$%^&*)');
    } else {
      score += 1;
    }

    // فحص الكلمات الشائعة
    if (_isCommonPassword(password)) {
      issues.add('كلمة مرور شائعة جداً');
      suggestions.add('استخدم كلمة مرور أكثر تعقيداً');
      score = score > 0 ? score - 1 : 0;
    }

    // فحص التكرار
    if (_hasRepeatingPatterns(password)) {
      issues.add('تحتوي على أنماط متكررة');
      suggestions.add('تجنب تكرار الأحرف أو الأرقام');
      score = score > 0 ? score - 1 : 0;
    }

    // فحص التسلسل
    if (_hasSequentialChars(password)) {
      issues.add('تحتوي على تسلسل منطقي');
      suggestions.add('تجنب التسلسل مثل 123 أو abc');
      score = score > 0 ? score - 1 : 0;
    }

    final isValid = issues.isEmpty && score >= minScore;
    final message = isValid 
        ? 'كلمة مرور قوية' 
        : issues.isNotEmpty 
            ? issues.first 
            : 'كلمة مرور ضعيفة';

    return PasswordValidationResult(
      isValid: isValid,
      score: score,
      message: message,
      issues: issues,
      suggestions: suggestions,
    );
  }

  /// فحص الكلمات الشائعة
  static bool _isCommonPassword(String password) {
    final lowerPassword = password.toLowerCase();
    return _commonPasswords.any((common) => 
        lowerPassword.contains(common.toLowerCase()) ||
        _calculateSimilarity(lowerPassword, common.toLowerCase()) > 0.8
    );
  }

  /// فحص الأنماط المتكررة
  static bool _hasRepeatingPatterns(String password) {
    // فحص تكرار الأحرف (أكثر من 3 مرات متتالية)
    for (int i = 0; i < password.length - 2; i++) {
      if (password[i] == password[i + 1] && 
          password[i + 1] == password[i + 2]) {
        return true;
      }
    }

    // فحص تكرار الأنماط القصيرة
    for (int len = 2; len <= password.length ~/ 3; len++) {
      for (int i = 0; i <= password.length - len * 2; i++) {
        String pattern = password.substring(i, i + len);
        String next = password.substring(i + len, i + len * 2);
        if (pattern == next) {
          return true;
        }
      }
    }

    return false;
  }

  /// فحص التسلسل المنطقي
  static bool _hasSequentialChars(String password) {
    const sequences = [
      'abcdefghijklmnopqrstuvwxyz',
      '0123456789',
      'qwertyuiop',
      'asdfghjkl',
      'zxcvbnm'
    ];

    for (String sequence in sequences) {
      for (int i = 0; i <= sequence.length - 3; i++) {
        String subSeq = sequence.substring(i, i + 3);
        String reverseSeq = subSeq.split('').reversed.join('');
        
        if (password.toLowerCase().contains(subSeq) ||
            password.toLowerCase().contains(reverseSeq)) {
          return true;
        }
      }
    }

    return false;
  }

  /// حساب التشابه بين نصين
  static double _calculateSimilarity(String a, String b) {
    if (a.isEmpty || b.isEmpty) return 0.0;
    
    int maxLength = a.length > b.length ? a.length : b.length;
    int distance = _levenshteinDistance(a, b);
    
    return 1.0 - (distance / maxLength);
  }

  /// حساب مسافة Levenshtein
  static int _levenshteinDistance(String a, String b) {
    List<List<int>> matrix = List.generate(
      a.length + 1, 
      (i) => List.filled(b.length + 1, 0)
    );

    for (int i = 0; i <= a.length; i++) {
      matrix[i][0] = i;
    }

    for (int j = 0; j <= b.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= a.length; i++) {
      for (int j = 1; j <= b.length; j++) {
        int cost = a[i - 1] == b[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,      // deletion
          matrix[i][j - 1] + 1,      // insertion
          matrix[i - 1][j - 1] + cost // substitution
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[a.length][b.length];
  }

  /// توليد كلمة مرور قوية
  static String generateStrongPassword({int length = 12}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*()';
    final random = Random();

    String password = '';
    for (int i = 0; i < length; i++) {
      password += chars[random.nextInt(chars.length)];
    }

    return password;
  }

  /// تقييم قوة كلمة المرور بالنسبة المئوية
  static int getPasswordStrengthPercentage(String password) {
    final result = validatePassword(password);
    return ((result.score / 5) * 100).round();
  }

  /// الحصول على لون يمثل قوة كلمة المرور
  static PasswordStrength getPasswordStrength(String password) {
    final percentage = getPasswordStrengthPercentage(password);
    
    if (percentage < 20) return PasswordStrength.veryWeak;
    if (percentage < 40) return PasswordStrength.weak;
    if (percentage < 60) return PasswordStrength.fair;
    if (percentage < 80) return PasswordStrength.good;
    return PasswordStrength.strong;
  }
}

/// نتيجة التحقق من كلمة المرور
class PasswordValidationResult {
  final bool isValid;
  final int score;
  final String message;
  final List<String> issues;
  final List<String> suggestions;

  const PasswordValidationResult({
    required this.isValid,
    required this.score,
    required this.message,
    this.issues = const [],
    this.suggestions = const [],
  });

  @override
  String toString() {
    return 'PasswordValidationResult(isValid: $isValid, score: $score, message: $message)';
  }
}

/// مستويات قوة كلمة المرور
enum PasswordStrength {
  veryWeak,
  weak,
  fair,
  good,
  strong,
}

extension PasswordStrengthExtension on PasswordStrength {
  String get label {
    switch (this) {
      case PasswordStrength.veryWeak:
        return 'ضعيفة جداً';
      case PasswordStrength.weak:
        return 'ضعيفة';
      case PasswordStrength.fair:
        return 'متوسطة';
      case PasswordStrength.good:
        return 'جيدة';
      case PasswordStrength.strong:
        return 'قوية';
    }
  }

  int get colorValue {
    switch (this) {
      case PasswordStrength.veryWeak:
        return 0xFFE53E3E; // أحمر
      case PasswordStrength.weak:
        return 0xFFED8936; // برتقالي
      case PasswordStrength.fair:
        return 0xFFECC94B; // أصفر
      case PasswordStrength.good:
        return 0xFF48BB78; // أخضر فاتح
      case PasswordStrength.strong:
        return 0xFF38A169; // أخضر غامق
    }
  }
}
