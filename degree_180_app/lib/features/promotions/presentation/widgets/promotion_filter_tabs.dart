import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class PromotionFilterTabs extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const PromotionFilterTabs({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final filters = [
      {'id': 'all', 'label': 'All'},
      {'id': 'active', 'label': 'Active'},
      {'id': 'upcoming', 'label': 'Upcoming'},
      {'id': 'expired', 'label': 'Expired'},
    ];

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: filters.map((filter) {
          final isSelected = selectedFilter == filter['id'];
          return Expanded(
            child: _buildFilterTab(
              label: filter['label']!,
              isSelected: isSelected,
              onTap: () => onFilterChanged(filter['id']!),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFilterTab({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: AppConstants.smallPadding,
          ),
          margin: const EdgeInsets.symmetric(
            horizontal: 4,
          ),
          decoration: BoxDecoration(
            color: isSelected 
                ? AppColors.buttonWhite.withValues(alpha: 0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppColors.textWhite,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
        ),
      ),
    );
  }
}
