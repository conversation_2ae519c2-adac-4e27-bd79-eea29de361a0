import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../performance/performance_manager.dart';

class EnhancedNetworkManager {
  static EnhancedNetworkManager? _instance;
  static EnhancedNetworkManager get instance => _instance ??= EnhancedNetworkManager._();
  
  EnhancedNetworkManager._() {
    _setupDio();
    _setupConnectivityMonitoring();
  }

  late Dio _dio;
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  bool _isOnline = true;
  List<ConnectivityResult> _currentConnectivity = [ConnectivityResult.none];
  final List<NetworkRequest> _pendingRequests = [];
  final Map<String, CachedResponse> _responseCache = {};
  
  // Network configuration
  static const int _connectTimeout = 30000;
  static const int _receiveTimeout = 30000;
  static const int _maxRetries = 3;
  static const int _retryDelay = 1000;
  static const int _maxCacheSize = 100;
  static const Duration _cacheExpiry = Duration(minutes: 5);

  bool get isOnline => _isOnline;
  List<ConnectivityResult> get currentConnectivity => _currentConnectivity;

  void _setupDio() {
    _dio = Dio(BaseOptions(
      connectTimeout: Duration(milliseconds: _connectTimeout),
      receiveTimeout: Duration(milliseconds: _receiveTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(_createLoggingInterceptor());
    _dio.interceptors.add(_createRetryInterceptor());
    _dio.interceptors.add(_createCacheInterceptor());
    _dio.interceptors.add(_createPerformanceInterceptor());
  }

  void _setupConnectivityMonitoring() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        _currentConnectivity = results;
        _isOnline = results.isNotEmpty && !results.contains(ConnectivityResult.none);

        if (kDebugMode) {
          debugPrint('Network connectivity changed: $results');
        }

        if (_isOnline) {
          _processPendingRequests();
        }
      },
    );

    // Check initial connectivity
    _checkInitialConnectivity();
  }

  Future<void> _checkInitialConnectivity() async {
    try {
      _currentConnectivity = await _connectivity.checkConnectivity();
      _isOnline = _currentConnectivity.isNotEmpty && !_currentConnectivity.contains(ConnectivityResult.none);
    } catch (e) {
      debugPrint('Error checking initial connectivity: $e');
    }
  }

  // Logging interceptor
  Interceptor _createLoggingInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        if (kDebugMode) {
          debugPrint('🌐 ${options.method} ${options.uri}');
          if (options.data != null) {
            debugPrint('📤 Request data: ${options.data}');
          }
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        if (kDebugMode) {
          debugPrint('✅ ${response.statusCode} ${response.requestOptions.uri}');
        }
        handler.next(response);
      },
      onError: (error, handler) {
        if (kDebugMode) {
          debugPrint('❌ ${error.response?.statusCode} ${error.requestOptions.uri}');
          debugPrint('Error: ${error.message}');
        }
        handler.next(error);
      },
    );
  }

  // Retry interceptor
  Interceptor _createRetryInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) async {
        final requestOptions = error.requestOptions;
        final retryCount = requestOptions.extra['retryCount'] as int? ?? 0;

        if (retryCount < _maxRetries && _shouldRetry(error)) {
          requestOptions.extra['retryCount'] = retryCount + 1;
          
          // Wait before retry
          await Future.delayed(Duration(milliseconds: _retryDelay * (retryCount + 1)));
          
          if (kDebugMode) {
            debugPrint('🔄 Retrying request (${retryCount + 1}/$_maxRetries): ${requestOptions.uri}');
          }

          try {
            final response = await _dio.fetch(requestOptions);
            handler.resolve(response);
          } catch (e) {
            handler.next(error);
          }
        } else {
          handler.next(error);
        }
      },
    );
  }

  // Cache interceptor
  Interceptor _createCacheInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        // Check cache for GET requests
        if (options.method == 'GET') {
          final cacheKey = _generateCacheKey(options);
          final cachedResponse = _responseCache[cacheKey];
          
          if (cachedResponse != null && !cachedResponse.isExpired) {
            if (kDebugMode) {
              debugPrint('📦 Cache hit: ${options.uri}');
            }
            handler.resolve(Response(
              requestOptions: options,
              data: cachedResponse.data,
              statusCode: 200,
              extra: {'fromCache': true},
            ));
            return;
          }
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        // Cache successful GET responses
        if (response.requestOptions.method == 'GET' && 
            response.statusCode == 200 &&
            response.extra['fromCache'] != true) {
          _cacheResponse(response);
        }
        handler.next(response);
      },
    );
  }

  // Performance monitoring interceptor
  Interceptor _createPerformanceInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        final operationName = 'network_${options.method}_${options.uri.path}';
        PerformanceManager.instance.startOperation(operationName);
        options.extra['performanceOperation'] = operationName;
        handler.next(options);
      },
      onResponse: (response, handler) {
        final operationName = response.requestOptions.extra['performanceOperation'] as String?;
        if (operationName != null) {
          PerformanceManager.instance.endOperation(operationName);
        }
        handler.next(response);
      },
      onError: (error, handler) {
        final operationName = error.requestOptions.extra['performanceOperation'] as String?;
        if (operationName != null) {
          PerformanceManager.instance.endOperation(operationName);
        }
        handler.next(error);
      },
    );
  }

  bool _shouldRetry(DioException error) {
    // Retry on network errors, timeouts, and 5xx server errors
    return error.type == DioExceptionType.connectionTimeout ||
           error.type == DioExceptionType.receiveTimeout ||
           error.type == DioExceptionType.connectionError ||
           (error.response?.statusCode != null && 
            error.response!.statusCode! >= 500);
  }

  String _generateCacheKey(RequestOptions options) {
    return '${options.method}_${options.uri}_${options.queryParameters}';
  }

  void _cacheResponse(Response response) {
    if (_responseCache.length >= _maxCacheSize) {
      _evictOldestCacheEntry();
    }

    final cacheKey = _generateCacheKey(response.requestOptions);
    _responseCache[cacheKey] = CachedResponse(
      data: response.data,
      timestamp: DateTime.now(),
    );
  }

  void _evictOldestCacheEntry() {
    if (_responseCache.isEmpty) return;

    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _responseCache.entries) {
      if (oldestTime == null || entry.value.timestamp.isBefore(oldestTime)) {
        oldestTime = entry.value.timestamp;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _responseCache.remove(oldestKey);
    }
  }

  // Public API methods
  Future<Response> get(String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    bool useCache = true,
  }) async {
    if (!_isOnline) {
      return _handleOfflineRequest('GET', path, queryParameters: queryParameters);
    }

    return await _dio.get(
      path,
      queryParameters: queryParameters,
      options: options,
    );
  }

  Future<Response> post(String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!_isOnline) {
      return _handleOfflineRequest('POST', path, data: data, queryParameters: queryParameters);
    }

    return await _dio.post(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }

  Future<Response> put(String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!_isOnline) {
      return _handleOfflineRequest('PUT', path, data: data, queryParameters: queryParameters);
    }

    return await _dio.put(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }

  Future<Response> delete(String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!_isOnline) {
      return _handleOfflineRequest('DELETE', path, data: data, queryParameters: queryParameters);
    }

    return await _dio.delete(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }

  Future<Response> _handleOfflineRequest(String method, String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    // For GET requests, try to return cached data
    if (method == 'GET') {
      final cacheKey = _generateCacheKey(RequestOptions(
        path: path,
        method: method,
        queryParameters: queryParameters ?? {},
      ));
      
      final cachedResponse = _responseCache[cacheKey];
      if (cachedResponse != null) {
        return Response(
          requestOptions: RequestOptions(path: path, method: method),
          data: cachedResponse.data,
          statusCode: 200,
          extra: {'fromCache': true, 'offline': true},
        );
      }
    }

    // For other requests, queue them for later
    _pendingRequests.add(NetworkRequest(
      method: method,
      path: path,
      data: data,
      queryParameters: queryParameters,
      timestamp: DateTime.now(),
    ));

    throw DioException(
      requestOptions: RequestOptions(path: path, method: method),
      message: 'No internet connection. Request queued for later.',
      type: DioExceptionType.connectionError,
    );
  }

  Future<void> _processPendingRequests() async {
    if (_pendingRequests.isEmpty) return;

    final requests = List<NetworkRequest>.from(_pendingRequests);
    _pendingRequests.clear();

    for (final request in requests) {
      try {
        switch (request.method) {
          case 'POST':
            await post(request.path, data: request.data, queryParameters: request.queryParameters);
            break;
          case 'PUT':
            await put(request.path, data: request.data, queryParameters: request.queryParameters);
            break;
          case 'DELETE':
            await delete(request.path, data: request.data, queryParameters: request.queryParameters);
            break;
        }
      } catch (e) {
        debugPrint('Failed to process pending request: $e');
      }
    }
  }

  // Clear cache
  void clearCache() {
    _responseCache.clear();
  }

  // Get network statistics
  Map<String, dynamic> getNetworkStats() {
    return {
      'isOnline': _isOnline,
      'connectivity': _currentConnectivity.toString(),
      'cacheSize': _responseCache.length,
      'pendingRequests': _pendingRequests.length,
    };
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _responseCache.clear();
    _pendingRequests.clear();
  }
}

class CachedResponse {
  final dynamic data;
  final DateTime timestamp;

  const CachedResponse({
    required this.data,
    required this.timestamp,
  });

  bool get isExpired => DateTime.now().isAfter(timestamp.add(EnhancedNetworkManager._cacheExpiry));
}

class NetworkRequest {
  final String method;
  final String path;
  final dynamic data;
  final Map<String, dynamic>? queryParameters;
  final DateTime timestamp;

  const NetworkRequest({
    required this.method,
    required this.path,
    this.data,
    this.queryParameters,
    required this.timestamp,
  });
}
