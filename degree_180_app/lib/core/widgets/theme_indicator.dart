import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../services/theme_service.dart';
import '../enums/gender.dart';

class ThemeIndicator extends StatelessWidget {
  final bool showLabel;
  final double size;
  final VoidCallback? onTap;

  const ThemeIndicator({
    super.key,
    this.showLabel = true,
    this.size = 40,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: ThemeService(),
      builder: (context, child) {
        final themeService = ThemeService();
        
        return GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.backgroundWhite,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: themeService.primaryColor.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Theme Color Indicator
                Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: themeService.gradientColors,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: themeService.primaryColor.withValues(alpha: 0.3),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    themeService.getGenderIcon(),
                    color: Colors.white,
                    size: size * 0.6,
                  ),
                ),
                
                if (showLabel) ...[
                  const SizedBox(width: 12),
                  
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _getThemeTitle(themeService.currentGender),
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      
                      const SizedBox(height: 2),
                      
                      Row(
                        children: [
                          Icon(
                            themeService.isDarkMode 
                                ? Icons.dark_mode 
                                : Icons.light_mode,
                            size: 12,
                            color: AppColors.textLight,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            themeService.isDarkMode ? 'داكن' : 'فاتح',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textLight,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
                
                if (onTap != null) ...[
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.textLight,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  String _getThemeTitle(Gender gender) {
    switch (gender) {
      case Gender.female:
        return 'مظهر أنثوي';
      case Gender.male:
        return 'مظهر ذكوري';
      default:
        return 'مظهر افتراضي';
    }
  }
}

class ThemeQuickSwitcher extends StatelessWidget {
  const ThemeQuickSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: ThemeService(),
      builder: (context, child) {
        final themeService = ThemeService();
        
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.backgroundWhite,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: themeService.primaryColor.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.palette,
                    color: themeService.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'المظهر الحالي',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              Row(
                children: [
                  // Male Theme Button
                  Expanded(
                    child: _buildThemeButton(
                      context: context,
                      gender: Gender.male,
                      title: 'ذكوري',
                      icon: Icons.male,
                      colors: [
                        const Color(0xFF6C63FF),
                        const Color(0xFF9C88FF),
                      ],
                      isSelected: themeService.currentGender == Gender.male,
                      onTap: () => themeService.setGender(Gender.male),
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Female Theme Button
                  Expanded(
                    child: _buildThemeButton(
                      context: context,
                      gender: Gender.female,
                      title: 'أنثوي',
                      icon: Icons.female,
                      colors: [
                        const Color(0xFFE91E63),
                        const Color(0xFFF8BBD9),
                      ],
                      isSelected: themeService.currentGender == Gender.female,
                      onTap: () => themeService.setGender(Gender.female),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Dark Mode Toggle
              Row(
                children: [
                  Icon(
                    themeService.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                    color: themeService.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'الوضع الليلي',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const Spacer(),
                  Switch(
                    value: themeService.isDarkMode,
                    onChanged: (value) => themeService.toggleDarkMode(),
                    activeColor: themeService.primaryColor,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeButton({
    required BuildContext context,
    required Gender gender,
    required String title,
    required IconData icon,
    required List<Color> colors,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: colors,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : AppColors.backgroundGray,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.transparent : AppColors.borderGray,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : AppColors.textLight,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isSelected ? Colors.white : AppColors.textDark,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ThemePreviewCard extends StatelessWidget {
  final Gender gender;
  final bool isSelected;
  final VoidCallback onTap;

  const ThemePreviewCard({
    super.key,
    required this.gender,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colors = _getColorsForGender(gender);
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: colors,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : AppColors.backgroundWhite,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? Colors.transparent : AppColors.borderGray,
            width: isSelected ? 0 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: colors.first.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Row(
          children: [
            // Theme Icon
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white.withValues(alpha: 0.2)
                    : colors.first.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getIconForGender(gender),
                color: isSelected ? Colors.white : colors.first,
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Theme Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getTitleForGender(gender),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getDescriptionForGender(gender),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Colors.white.withValues(alpha: 0.8)
                          : AppColors.textLight,
                    ),
                  ),
                ],
              ),
            ),
            
            // Selection Indicator
            if (isSelected)
              Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check,
                  color: colors.first,
                  size: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  List<Color> _getColorsForGender(Gender gender) {
    switch (gender) {
      case Gender.female:
        return [const Color(0xFFE91E63), const Color(0xFFF8BBD9)];
      case Gender.male:
      default:
        return [const Color(0xFF6C63FF), const Color(0xFF9C88FF)];
    }
  }

  IconData _getIconForGender(Gender gender) {
    switch (gender) {
      case Gender.female:
        return Icons.female;
      case Gender.male:
        return Icons.male;
      default:
        return Icons.auto_awesome;
    }
  }

  String _getTitleForGender(Gender gender) {
    switch (gender) {
      case Gender.female:
        return 'مظهر أنثوي';
      case Gender.male:
        return 'مظهر ذكوري';
      default:
        return 'مظهر افتراضي';
    }
  }

  String _getDescriptionForGender(Gender gender) {
    switch (gender) {
      case Gender.female:
        return 'ألوان وردية ونعومة أنثوية';
      case Gender.male:
        return 'ألوان بنفسجية وزرقاء قوية';
      default:
        return 'مظهر متوازن ومحايد';
    }
  }
}
