import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/painting.dart';

/// نظام إدارة الذاكرة المحسن للتطبيق
class MemoryManager {
  static MemoryManager? _instance;
  static MemoryManager get instance => _instance ??= MemoryManager._();
  
  MemoryManager._();

  Timer? _memoryCheckTimer;
  final List<MemoryWarningCallback> _warningCallbacks = [];
  final List<LowMemoryCallback> _lowMemoryCallbacks = [];
  
  // إعدادات الذاكرة
  static const int _warningThresholdMB = 100; // تحذير عند 100 ميجابايت
  static const int _criticalThresholdMB = 50;  // حرج عند 50 ميجابايت
  static const Duration _checkInterval = Duration(seconds: 30);

  bool _isInitialized = false;
  int _lastMemoryUsage = 0;
  int _peakMemoryUsage = 0;

  /// تهيئة نظام إدارة الذاكرة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // بدء مراقبة الذاكرة
      _startMemoryMonitoring();
      
      // تسجيل مستمع تحذيرات النظام
      _registerSystemMemoryWarnings();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('MemoryManager: تم تهيئة نظام إدارة الذاكرة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MemoryManager: خطأ في التهيئة: $e');
      }
    }
  }

  /// بدء مراقبة الذاكرة الدورية
  void _startMemoryMonitoring() {
    _memoryCheckTimer?.cancel();
    _memoryCheckTimer = Timer.periodic(_checkInterval, (_) {
      _checkMemoryUsage();
    });
  }

  /// تسجيل مستمعي تحذيرات النظام
  void _registerSystemMemoryWarnings() {
    if (Platform.isIOS || Platform.isAndroid) {
      // استخدام MethodChannel للحصول على تحذيرات الذاكرة من النظام
      const MethodChannel('memory_manager')
          .setMethodCallHandler(_handleSystemMemoryWarning);
    }
  }

  /// معالجة تحذيرات الذاكرة من النظام
  Future<void> _handleSystemMemoryWarning(MethodCall call) async {
    switch (call.method) {
      case 'onMemoryWarning':
        await _handleMemoryWarning();
        break;
      case 'onLowMemory':
        await _handleLowMemory();
        break;
    }
  }

  /// فحص استخدام الذاكرة
  Future<void> _checkMemoryUsage() async {
    try {
      final memoryInfo = await _getMemoryInfo();
      _lastMemoryUsage = memoryInfo.usedMemoryMB;
      
      if (_lastMemoryUsage > _peakMemoryUsage) {
        _peakMemoryUsage = _lastMemoryUsage;
      }

      // فحص العتبات
      if (memoryInfo.availableMemoryMB < _criticalThresholdMB) {
        await _handleLowMemory();
      } else if (memoryInfo.availableMemoryMB < _warningThresholdMB) {
        await _handleMemoryWarning();
      }

      if (kDebugMode) {
        print('MemoryManager: الذاكرة المستخدمة: ${memoryInfo.usedMemoryMB}MB, '
              'المتاحة: ${memoryInfo.availableMemoryMB}MB');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MemoryManager: خطأ في فحص الذاكرة: $e');
      }
    }
  }

  /// الحصول على معلومات الذاكرة
  Future<MemoryInfo> _getMemoryInfo() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // استخدام MethodChannel للحصول على معلومات الذاكرة من النظام
        final result = await const MethodChannel('memory_manager')
            .invokeMethod<Map>('getMemoryInfo');
        
        if (result != null) {
          return MemoryInfo(
            totalMemoryMB: result['totalMemory'] ?? 0,
            usedMemoryMB: result['usedMemory'] ?? 0,
            availableMemoryMB: result['availableMemory'] ?? 0,
          );
        }
      }
      
      // قيم افتراضية في حالة عدم توفر معلومات النظام
      return MemoryInfo(
        totalMemoryMB: 1024, // 1GB افتراضي
        usedMemoryMB: _lastMemoryUsage,
        availableMemoryMB: 1024 - _lastMemoryUsage,
      );
    } catch (e) {
      if (kDebugMode) {
        print('MemoryManager: خطأ في الحصول على معلومات الذاكرة: $e');
      }
      
      return MemoryInfo(
        totalMemoryMB: 1024,
        usedMemoryMB: 0,
        availableMemoryMB: 1024,
      );
    }
  }

  /// معالجة تحذير الذاكرة
  Future<void> _handleMemoryWarning() async {
    if (kDebugMode) {
      print('MemoryManager: تحذير الذاكرة - بدء تنظيف خفيف');
    }

    // تنفيذ callbacks التحذير
    for (final callback in _warningCallbacks) {
      try {
        await callback();
      } catch (e) {
        if (kDebugMode) {
          print('MemoryManager: خطأ في callback التحذير: $e');
        }
      }
    }

    // تنظيف خفيف
    await _performLightCleanup();
  }

  /// معالجة نقص الذاكرة الحرج
  Future<void> _handleLowMemory() async {
    if (kDebugMode) {
      print('MemoryManager: نقص حرج في الذاكرة - بدء تنظيف شامل');
    }

    // تنفيذ callbacks النقص الحرج
    for (final callback in _lowMemoryCallbacks) {
      try {
        await callback();
      } catch (e) {
        if (kDebugMode) {
          print('MemoryManager: خطأ في callback النقص الحرج: $e');
        }
      }
    }

    // تنظيف شامل
    await _performAggressiveCleanup();
  }

  /// تنظيف خفيف للذاكرة
  Future<void> _performLightCleanup() async {
    try {
      // تنظيف cache الصور
      PaintingBinding.instance.imageCache.clear();
      
      // تشغيل garbage collector
      await _triggerGarbageCollection();
      
      if (kDebugMode) {
        print('MemoryManager: تم التنظيف الخفيف');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MemoryManager: خطأ في التنظيف الخفيف: $e');
      }
    }
  }

  /// تنظيف شامل للذاكرة
  Future<void> _performAggressiveCleanup() async {
    try {
      // تنظيف cache الصور بالكامل
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      // تقليل حجم cache الصور
      PaintingBinding.instance.imageCache.maximumSize = 50;
      PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50MB
      
      // تشغيل garbage collector متعدد المرات
      for (int i = 0; i < 3; i++) {
        await _triggerGarbageCollection();
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      if (kDebugMode) {
        print('MemoryManager: تم التنظيف الشامل');
      }
    } catch (e) {
      if (kDebugMode) {
        print('MemoryManager: خطأ في التنظيف الشامل: $e');
      }
    }
  }

  /// تشغيل garbage collector
  Future<void> _triggerGarbageCollection() async {
    try {
      await const MethodChannel('memory_manager')
          .invokeMethod('triggerGC');
    } catch (e) {
      // في حالة عدم توفر native method، لا نفعل شيئاً
      if (kDebugMode) {
        print('MemoryManager: Native GC غير متوفر');
      }
    }
  }

  /// تسجيل callback لتحذيرات الذاكرة
  void registerMemoryWarningCallback(MemoryWarningCallback callback) {
    _warningCallbacks.add(callback);
  }

  /// إلغاء تسجيل callback لتحذيرات الذاكرة
  void unregisterMemoryWarningCallback(MemoryWarningCallback callback) {
    _warningCallbacks.remove(callback);
  }

  /// تسجيل callback لنقص الذاكرة الحرج
  void registerLowMemoryCallback(LowMemoryCallback callback) {
    _lowMemoryCallbacks.add(callback);
  }

  /// إلغاء تسجيل callback لنقص الذاكرة الحرج
  void unregisterLowMemoryCallback(LowMemoryCallback callback) {
    _lowMemoryCallbacks.remove(callback);
  }

  /// الحصول على إحصائيات الذاكرة
  Future<MemoryStats> getMemoryStats() async {
    final memoryInfo = await _getMemoryInfo();
    
    return MemoryStats(
      currentUsageMB: memoryInfo.usedMemoryMB,
      peakUsageMB: _peakMemoryUsage,
      availableMemoryMB: memoryInfo.availableMemoryMB,
      totalMemoryMB: memoryInfo.totalMemoryMB,
      imageCacheSize: PaintingBinding.instance.imageCache.currentSize,
      imageCacheSizeBytes: PaintingBinding.instance.imageCache.currentSizeBytes,
    );
  }

  /// تنظيف يدوي للذاكرة
  Future<void> manualCleanup({bool aggressive = false}) async {
    if (aggressive) {
      await _performAggressiveCleanup();
    } else {
      await _performLightCleanup();
    }
  }

  /// إيقاف نظام إدارة الذاكرة
  void dispose() {
    _memoryCheckTimer?.cancel();
    _warningCallbacks.clear();
    _lowMemoryCallbacks.clear();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('MemoryManager: تم إيقاف نظام إدارة الذاكرة');
    }
  }
}

/// معلومات الذاكرة
class MemoryInfo {
  final int totalMemoryMB;
  final int usedMemoryMB;
  final int availableMemoryMB;

  const MemoryInfo({
    required this.totalMemoryMB,
    required this.usedMemoryMB,
    required this.availableMemoryMB,
  });
}

/// إحصائيات الذاكرة
class MemoryStats {
  final int currentUsageMB;
  final int peakUsageMB;
  final int availableMemoryMB;
  final int totalMemoryMB;
  final int imageCacheSize;
  final int imageCacheSizeBytes;

  const MemoryStats({
    required this.currentUsageMB,
    required this.peakUsageMB,
    required this.availableMemoryMB,
    required this.totalMemoryMB,
    required this.imageCacheSize,
    required this.imageCacheSizeBytes,
  });

  double get usagePercentage => 
      totalMemoryMB > 0 ? (currentUsageMB / totalMemoryMB) * 100 : 0;

  String get imageCacheSizeMB => 
      (imageCacheSizeBytes / (1024 * 1024)).toStringAsFixed(1);
}

/// نوع callback لتحذيرات الذاكرة
typedef MemoryWarningCallback = Future<void> Function();

/// نوع callback لنقص الذاكرة الحرج
typedef LowMemoryCallback = Future<void> Function();
