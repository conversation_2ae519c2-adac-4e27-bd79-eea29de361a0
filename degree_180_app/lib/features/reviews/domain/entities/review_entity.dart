import 'package:equatable/equatable.dart';

enum ReviewStatus {
  pending,
  approved,
  rejected,
  hidden,
}

class ReviewEntity extends Equatable {
  final String id;
  final String customerId;
  final String providerId;
  final String? bookingId;
  final double overallRating;
  final double? serviceQualityRating;
  final double? timelinessRating;
  final double? cleanlinessRating;
  final double? professionalismRating;
  final double? valueForMoneyRating;
  final String? comment;
  final List<String> imageUrls;
  final ReviewStatus status;
  final bool isAnonymous;
  final bool isRecommended;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? providerResponse;
  final DateTime? providerResponseDate;
  final int helpfulCount;
  final int notHelpfulCount;
  final Map<String, dynamic>? metadata;

  const ReviewEntity({
    required this.id,
    required this.customerId,
    required this.providerId,
    this.bookingId,
    required this.overallRating,
    this.serviceQualityRating,
    this.timelinessRating,
    this.cleanlinessRating,
    this.professionalismRating,
    this.valueForMoneyRating,
    this.comment,
    this.imageUrls = const [],
    this.status = ReviewStatus.pending,
    this.isAnonymous = false,
    this.isRecommended = true,
    required this.createdAt,
    required this.updatedAt,
    this.providerResponse,
    this.providerResponseDate,
    this.helpfulCount = 0,
    this.notHelpfulCount = 0,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        customerId,
        providerId,
        bookingId,
        overallRating,
        serviceQualityRating,
        timelinessRating,
        cleanlinessRating,
        professionalismRating,
        valueForMoneyRating,
        comment,
        imageUrls,
        status,
        isAnonymous,
        isRecommended,
        createdAt,
        updatedAt,
        providerResponse,
        providerResponseDate,
        helpfulCount,
        notHelpfulCount,
        metadata,
      ];

  ReviewEntity copyWith({
    String? id,
    String? customerId,
    String? providerId,
    String? bookingId,
    double? overallRating,
    double? serviceQualityRating,
    double? timelinessRating,
    double? cleanlinessRating,
    double? professionalismRating,
    double? valueForMoneyRating,
    String? comment,
    List<String>? imageUrls,
    ReviewStatus? status,
    bool? isAnonymous,
    bool? isRecommended,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? providerResponse,
    DateTime? providerResponseDate,
    int? helpfulCount,
    int? notHelpfulCount,
    Map<String, dynamic>? metadata,
  }) {
    return ReviewEntity(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      providerId: providerId ?? this.providerId,
      bookingId: bookingId ?? this.bookingId,
      overallRating: overallRating ?? this.overallRating,
      serviceQualityRating: serviceQualityRating ?? this.serviceQualityRating,
      timelinessRating: timelinessRating ?? this.timelinessRating,
      cleanlinessRating: cleanlinessRating ?? this.cleanlinessRating,
      professionalismRating: professionalismRating ?? this.professionalismRating,
      valueForMoneyRating: valueForMoneyRating ?? this.valueForMoneyRating,
      comment: comment ?? this.comment,
      imageUrls: imageUrls ?? this.imageUrls,
      status: status ?? this.status,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      isRecommended: isRecommended ?? this.isRecommended,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      providerResponse: providerResponse ?? this.providerResponse,
      providerResponseDate: providerResponseDate ?? this.providerResponseDate,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      notHelpfulCount: notHelpfulCount ?? this.notHelpfulCount,
      metadata: metadata ?? this.metadata,
    );
  }

  String get statusString {
    switch (status) {
      case ReviewStatus.pending:
        return 'pending';
      case ReviewStatus.approved:
        return 'approved';
      case ReviewStatus.rejected:
        return 'rejected';
      case ReviewStatus.hidden:
        return 'hidden';
    }
  }

  bool get isPending => status == ReviewStatus.pending;
  bool get isApproved => status == ReviewStatus.approved;
  bool get isRejected => status == ReviewStatus.rejected;
  bool get isHidden => status == ReviewStatus.hidden;

  bool get hasComment => comment != null && comment!.isNotEmpty;
  bool get hasImages => imageUrls.isNotEmpty;
  bool get hasProviderResponse => providerResponse != null && providerResponse!.isNotEmpty;

  bool get hasDetailedRatings {
    return serviceQualityRating != null ||
           timelinessRating != null ||
           cleanlinessRating != null ||
           professionalismRating != null ||
           valueForMoneyRating != null;
  }

  double get averageDetailedRating {
    final ratings = [
      serviceQualityRating,
      timelinessRating,
      cleanlinessRating,
      professionalismRating,
      valueForMoneyRating,
    ].where((rating) => rating != null).cast<double>();

    if (ratings.isEmpty) return overallRating;
    
    return ratings.reduce((a, b) => a + b) / ratings.length;
  }

  int get totalHelpfulnessVotes => helpfulCount + notHelpfulCount;

  double get helpfulnessRatio {
    if (totalHelpfulnessVotes == 0) return 0.0;
    return helpfulCount / totalHelpfulnessVotes;
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '${years} year${years > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '${months} month${months > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  bool get isRecent => DateTime.now().difference(createdAt).inDays <= 7;
}
