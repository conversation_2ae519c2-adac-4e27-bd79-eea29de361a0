import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
// import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import '../models/location_model.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  StreamSubscription<Position>? _positionStream;
  Position? _currentPosition;
  LocationModel? _currentLocation;
  bool _isTracking = false;

  // Location settings
  static const LocationSettings _locationSettings = LocationSettings(
    accuracy: LocationAccuracy.high,
    distanceFilter: 10, // Update every 10 meters
  );

  // Get current location
  Future<LocationModel?> getCurrentLocation() async {
    try {
      // Check permissions
      final hasPermission = await _checkLocationPermission();
      if (!hasPermission) {
        throw Exception('Location permission denied');
      }

      // Check if location service is enabled
      final isEnabled = await Geolocator.isLocationServiceEnabled();
      if (!isEnabled) {
        throw Exception('Location service is disabled');
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      );

      _currentPosition = position;

      // Get address from coordinates
      final address = await _getAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );

      _currentLocation = LocationModel(
        latitude: position.latitude,
        longitude: position.longitude,
        address: address,
      );

      return _currentLocation;
    } catch (e) {
      print('Error getting current location: $e');
      return null;
    }
  }

  // Start location tracking
  Future<void> startLocationTracking({
    Function(LocationModel)? onLocationUpdate,
  }) async {
    if (_isTracking) return;

    try {
      final hasPermission = await _checkLocationPermission();
      if (!hasPermission) return;

      _isTracking = true;
      _positionStream = Geolocator.getPositionStream(
        locationSettings: _locationSettings,
      ).listen(
        (Position position) async {
          _currentPosition = position;
          
          final address = await _getAddressFromCoordinates(
            position.latitude,
            position.longitude,
          );

          _currentLocation = LocationModel(
            latitude: position.latitude,
            longitude: position.longitude,
            address: address,
          );

          onLocationUpdate?.call(_currentLocation!);
        },
        onError: (error) {
          print('Location tracking error: $error');
          stopLocationTracking();
        },
      );
    } catch (e) {
      print('Error starting location tracking: $e');
      _isTracking = false;
    }
  }

  // Stop location tracking
  void stopLocationTracking() {
    _positionStream?.cancel();
    _positionStream = null;
    _isTracking = false;
  }

  // Check location permission
  Future<bool> _checkLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }
    
    if (permission == LocationPermission.deniedForever) {
      return false;
    }
    
    return true;
  }

  // Get address from coordinates
  Future<String> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return _formatAddress(placemark);
      }
      return 'Unknown location';
    } catch (e) {
      print('Error getting address: $e');
      return 'Unknown location';
    }
  }

  // Format address from placemark
  String _formatAddress(Placemark placemark) {
    final components = <String>[];
    
    if (placemark.street?.isNotEmpty == true) {
      components.add(placemark.street!);
    }
    
    if (placemark.subLocality?.isNotEmpty == true) {
      components.add(placemark.subLocality!);
    }
    
    if (placemark.locality?.isNotEmpty == true) {
      components.add(placemark.locality!);
    }
    
    if (placemark.administrativeArea?.isNotEmpty == true) {
      components.add(placemark.administrativeArea!);
    }
    
    if (placemark.country?.isNotEmpty == true) {
      components.add(placemark.country!);
    }
    
    return components.join(', ');
  }

  // Get coordinates from address
  Future<LocationModel?> getLocationFromAddress(String address) async {
    try {
      final locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        final location = locations.first;
        return LocationModel(
          latitude: location.latitude,
          longitude: location.longitude,
          address: address,
        );
      }
      return null;
    } catch (e) {
      print('Error getting location from address: $e');
      return null;
    }
  }

  // Calculate distance between two points
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    ) / 1000; // Convert to kilometers
  }

  // Calculate bearing between two points
  double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  // Check if location is within radius
  bool isWithinRadius(
    LocationModel center,
    LocationModel target,
    double radiusKm,
  ) {
    final distance = calculateDistance(
      center.latitude,
      center.longitude,
      target.latitude,
      target.longitude,
    );
    return distance <= radiusKm;
  }

  // Get nearby locations
  List<T> getNearbyItems<T>(
    LocationModel userLocation,
    List<T> items,
    double radiusKm,
    LocationModel Function(T) getLocation,
  ) {
    return items.where((item) {
      final itemLocation = getLocation(item);
      return isWithinRadius(userLocation, itemLocation, radiusKm);
    }).toList();
  }

  // Sort by distance
  List<T> sortByDistance<T>(
    LocationModel userLocation,
    List<T> items,
    LocationModel Function(T) getLocation,
  ) {
    items.sort((a, b) {
      final locationA = getLocation(a);
      final locationB = getLocation(b);
      
      final distanceA = calculateDistance(
        userLocation.latitude,
        userLocation.longitude,
        locationA.latitude,
        locationA.longitude,
      );
      
      final distanceB = calculateDistance(
        userLocation.latitude,
        userLocation.longitude,
        locationB.latitude,
        locationB.longitude,
      );
      
      return distanceA.compareTo(distanceB);
    });
    
    return items;
  }

  // Open location in maps app
  Future<void> openInMaps(LocationModel location) async {
    try {
      final url = 'https://www.google.com/maps/search/?api=1&query=${location.latitude},${location.longitude}';
      // Use url_launcher to open the URL
      print('Opening maps: $url');
    } catch (e) {
      print('Error opening maps: $e');
    }
  }

  // Get directions URL
  String getDirectionsUrl(LocationModel from, LocationModel to) {
    return 'https://www.google.com/maps/dir/?api=1&origin=${from.latitude},${from.longitude}&destination=${to.latitude},${to.longitude}';
  }

  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  // Open location settings
  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  // Open app settings
  Future<void> openAppSettings() async {
    await Geolocator.openAppSettings();
  }

  // Get location accuracy
  Future<LocationAccuracyStatus> getLocationAccuracy() async {
    return await Geolocator.getLocationAccuracy();
  }

  // Request temporary full accuracy (iOS 14+)
  Future<LocationAccuracyStatus> requestTemporaryFullAccuracy({
    required String purposeKey,
  }) async {
    return await Geolocator.requestTemporaryFullAccuracy(
      purposeKey: purposeKey,
    );
  }

  // Get last known position
  Future<Position?> getLastKnownPosition() async {
    try {
      return await Geolocator.getLastKnownPosition();
    } catch (e) {
      print('Error getting last known position: $e');
      return null;
    }
  }

  // Format distance for display
  String formatDistance(double distanceKm) {
    if (distanceKm < 1) {
      return '${(distanceKm * 1000).round()} m';
    } else if (distanceKm < 10) {
      return '${distanceKm.toStringAsFixed(1)} km';
    } else {
      return '${distanceKm.round()} km';
    }
  }

  // Getters
  Position? get currentPosition => _currentPosition;
  LocationModel? get currentLocation => _currentLocation;
  bool get isTracking => _isTracking;

  // Dispose
  void dispose() {
    stopLocationTracking();
  }
}
