import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/models/payment_model.dart';

class PaymentMethodSelector extends StatelessWidget {
  final PaymentMethod selectedMethod;
  final Function(PaymentMethod) onMethodSelected;

  const PaymentMethodSelector({
    super.key,
    required this.selectedMethod,
    required this.onMethodSelected,
  });

  @override
  Widget build(BuildContext context) {
    final methods = [
      _PaymentMethodOption(
        method: PaymentMethod.creditCard,
        title: 'Credit Card',
        subtitle: 'Visa, Mastercard, American Express',
        icon: Icons.credit_card,
        color: AppColors.primaryPurple,
      ),
      _PaymentMethodOption(
        method: PaymentMethod.knet,
        title: 'K-Net',
        subtitle: 'Kuwait local debit cards',
        icon: Icons.payment,
        color: AppColors.success,
      ),
      _PaymentMethodOption(
        method: PaymentMethod.applePay,
        title: 'Apple Pay',
        subtitle: 'Pay with Touch ID or Face ID',
        icon: Icons.apple,
        color: AppColors.textDark,
      ),
      _PaymentMethodOption(
        method: PaymentMethod.googlePay,
        title: 'Google Pay',
        subtitle: 'Quick and secure payments',
        icon: Icons.g_mobiledata,
        color: AppColors.warning,
      ),
      _PaymentMethodOption(
        method: PaymentMethod.wallet,
        title: 'Wallet',
        subtitle: 'Pay from your app wallet',
        icon: Icons.account_balance_wallet,
        color: AppColors.primaryPurple,
      ),
      _PaymentMethodOption(
        method: PaymentMethod.cash,
        title: 'Cash',
        subtitle: 'Pay when you arrive',
        icon: Icons.money,
        color: AppColors.success,
      ),
    ];

    return Column(
      children: methods.map((option) {
        final isSelected = selectedMethod == option.method;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _PaymentMethodCard(
            option: option,
            isSelected: isSelected,
            onTap: () => onMethodSelected(option.method),
          ),
        );
      }).toList(),
    );
  }
}

class _PaymentMethodOption {
  final PaymentMethod method;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  const _PaymentMethodOption({
    required this.method,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}

class _PaymentMethodCard extends StatelessWidget {
  final _PaymentMethodOption option;
  final bool isSelected;
  final VoidCallback onTap;

  const _PaymentMethodCard({
    required this.option,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: AppColors.backgroundWhite,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          border: Border.all(
            color: isSelected ? AppColors.primaryPurple : AppColors.borderGray,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected 
                  ? AppColors.primaryPurple.withValues(alpha: 0.2)
                  : Colors.black.withValues(alpha: 0.05),
              blurRadius: isSelected ? 12 : 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Payment Method Icon
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: option.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  option.icon,
                  color: option.color,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Payment Method Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      option.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? AppColors.primaryPurple : AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      option.subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textLight,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Selection Indicator
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? AppColors.primaryPurple : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? AppColors.primaryPurple : AppColors.borderGray,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: AppColors.textWhite,
                        size: 16,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
