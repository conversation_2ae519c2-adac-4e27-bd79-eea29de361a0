import 'package:equatable/equatable.dart';

enum SortBy {
  relevance,
  rating,
  distance,
  priceAsc,
  priceDesc,
  newest,
  popularity,
}

enum ServiceType {
  all,
  salon,
  barbershop,
  spa,
  nails,
  massage,
  skincare,
}

class SearchFilterEntity extends Equatable {
  final String? query;
  final ServiceType serviceType;
  final double? minPrice;
  final double? maxPrice;
  final double? minRating;
  final double? maxDistance; // in kilometers
  final bool? isOpenNow;
  final bool? hasAvailableSlots;
  final List<String>? specificServices;
  final SortBy sortBy;
  final double? latitude;
  final double? longitude;
  final DateTime? availableDate;
  final String? availableTime;

  const SearchFilterEntity({
    this.query,
    this.serviceType = ServiceType.all,
    this.minPrice,
    this.maxPrice,
    this.minRating,
    this.maxDistance,
    this.isOpenNow,
    this.hasAvailableSlots,
    this.specificServices,
    this.sortBy = SortBy.relevance,
    this.latitude,
    this.longitude,
    this.availableDate,
    this.availableTime,
  });

  @override
  List<Object?> get props => [
        query,
        serviceType,
        minPrice,
        maxPrice,
        minRating,
        maxDistance,
        isOpenNow,
        hasAvailableSlots,
        specificServices,
        sortBy,
        latitude,
        longitude,
        availableDate,
        availableTime,
      ];

  SearchFilterEntity copyWith({
    String? query,
    ServiceType? serviceType,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    double? maxDistance,
    bool? isOpenNow,
    bool? hasAvailableSlots,
    List<String>? specificServices,
    SortBy? sortBy,
    double? latitude,
    double? longitude,
    DateTime? availableDate,
    String? availableTime,
  }) {
    return SearchFilterEntity(
      query: query ?? this.query,
      serviceType: serviceType ?? this.serviceType,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minRating: minRating ?? this.minRating,
      maxDistance: maxDistance ?? this.maxDistance,
      isOpenNow: isOpenNow ?? this.isOpenNow,
      hasAvailableSlots: hasAvailableSlots ?? this.hasAvailableSlots,
      specificServices: specificServices ?? this.specificServices,
      sortBy: sortBy ?? this.sortBy,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      availableDate: availableDate ?? this.availableDate,
      availableTime: availableTime ?? this.availableTime,
    );
  }

  String get serviceTypeString {
    switch (serviceType) {
      case ServiceType.all:
        return 'all';
      case ServiceType.salon:
        return 'salon';
      case ServiceType.barbershop:
        return 'barbershop';
      case ServiceType.spa:
        return 'spa';
      case ServiceType.nails:
        return 'nails';
      case ServiceType.massage:
        return 'massage';
      case ServiceType.skincare:
        return 'skincare';
    }
  }

  String get sortByString {
    switch (sortBy) {
      case SortBy.relevance:
        return 'relevance';
      case SortBy.rating:
        return 'rating';
      case SortBy.distance:
        return 'distance';
      case SortBy.priceAsc:
        return 'price_asc';
      case SortBy.priceDesc:
        return 'price_desc';
      case SortBy.newest:
        return 'newest';
      case SortBy.popularity:
        return 'popularity';
    }
  }

  bool get hasLocationFilter => latitude != null && longitude != null;
  bool get hasPriceFilter => minPrice != null || maxPrice != null;
  bool get hasRatingFilter => minRating != null;
  bool get hasDistanceFilter => maxDistance != null && hasLocationFilter;
  bool get hasAvailabilityFilter => isOpenNow != null || hasAvailableSlots != null;
  bool get hasDateTimeFilter => availableDate != null || availableTime != null;

  bool get hasActiveFilters {
    return serviceType != ServiceType.all ||
           hasPriceFilter ||
           hasRatingFilter ||
           hasDistanceFilter ||
           hasAvailabilityFilter ||
           hasDateTimeFilter ||
           (specificServices != null && specificServices!.isNotEmpty);
  }

  int get activeFilterCount {
    int count = 0;
    if (serviceType != ServiceType.all) count++;
    if (hasPriceFilter) count++;
    if (hasRatingFilter) count++;
    if (hasDistanceFilter) count++;
    if (hasAvailabilityFilter) count++;
    if (hasDateTimeFilter) count++;
    if (specificServices != null && specificServices!.isNotEmpty) count++;
    return count;
  }

  SearchFilterEntity clearFilters() {
    return const SearchFilterEntity();
  }

  Map<String, dynamic> toMap() {
    return {
      'query': query,
      'serviceType': serviceTypeString,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'minRating': minRating,
      'maxDistance': maxDistance,
      'isOpenNow': isOpenNow,
      'hasAvailableSlots': hasAvailableSlots,
      'specificServices': specificServices,
      'sortBy': sortByString,
      'latitude': latitude,
      'longitude': longitude,
      'availableDate': availableDate?.toIso8601String(),
      'availableTime': availableTime,
    };
  }
}
