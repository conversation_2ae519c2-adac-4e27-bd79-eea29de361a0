/// أنواع الإشعارات
enum NotificationType {
  booking,
  reminder,
  promotion,
  system,
  chat,
  payment,
  review,
  general,
}

/// امتداد لأنواع الإشعارات
extension NotificationTypeExtension on NotificationType {
  /// الحصول على اسم نوع الإشعار للعرض
  String get displayName {
    switch (this) {
      case NotificationType.booking:
        return 'حجوزات';
      case NotificationType.reminder:
        return 'تذكيرات';
      case NotificationType.promotion:
        return 'عروض';
      case NotificationType.system:
        return 'نظام';
      case NotificationType.chat:
        return 'رسائل';
      case NotificationType.payment:
        return 'مدفوعات';
      case NotificationType.review:
        return 'تقييمات';
      case NotificationType.general:
        return 'عام';
    }
  }

  /// الحصول على اسم نوع الإشعار بالإنجليزية
  String get englishName {
    switch (this) {
      case NotificationType.booking:
        return 'booking';
      case NotificationType.reminder:
        return 'reminder';
      case NotificationType.promotion:
        return 'promotion';
      case NotificationType.system:
        return 'system';
      case NotificationType.chat:
        return 'chat';
      case NotificationType.payment:
        return 'payment';
      case NotificationType.review:
        return 'review';
      case NotificationType.general:
        return 'general';
    }
  }

  /// الحصول على وصف نوع الإشعار
  String get description {
    switch (this) {
      case NotificationType.booking:
        return 'إشعارات متعلقة بالحجوزات والمواعيد';
      case NotificationType.reminder:
        return 'تذكيرات بالمواعيد والأحداث المهمة';
      case NotificationType.promotion:
        return 'عروض وخصومات خاصة';
      case NotificationType.system:
        return 'إشعارات النظام والتحديثات';
      case NotificationType.chat:
        return 'رسائل جديدة ومحادثات';
      case NotificationType.payment:
        return 'إشعارات الدفع والفواتير';
      case NotificationType.review:
        return 'طلبات التقييم والمراجعات';
      case NotificationType.general:
        return 'إشعارات عامة ومتنوعة';
    }
  }

  /// تحويل من نص إلى enum
  static NotificationType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'booking':
        return NotificationType.booking;
      case 'reminder':
        return NotificationType.reminder;
      case 'promotion':
        return NotificationType.promotion;
      case 'system':
        return NotificationType.system;
      case 'chat':
        return NotificationType.chat;
      case 'payment':
        return NotificationType.payment;
      case 'review':
        return NotificationType.review;
      case 'general':
        return NotificationType.general;
      default:
        return NotificationType.general;
    }
  }

  /// تحويل إلى نص
  String toStringValue() {
    return englishName;
  }

  /// فحص ما إذا كان النوع مهم
  bool get isImportant {
    switch (this) {
      case NotificationType.booking:
      case NotificationType.reminder:
      case NotificationType.payment:
        return true;
      case NotificationType.promotion:
      case NotificationType.system:
      case NotificationType.chat:
      case NotificationType.review:
      case NotificationType.general:
        return false;
    }
  }

  /// فحص ما إذا كان النوع يتطلب إجراء
  bool get requiresAction {
    switch (this) {
      case NotificationType.booking:
      case NotificationType.payment:
      case NotificationType.review:
        return true;
      case NotificationType.reminder:
      case NotificationType.promotion:
      case NotificationType.system:
      case NotificationType.chat:
      case NotificationType.general:
        return false;
    }
  }

  /// الحصول على لون نوع الإشعار
  String get colorHex {
    switch (this) {
      case NotificationType.booking:
        return '#4CAF50'; // أخضر
      case NotificationType.reminder:
        return '#FF9800'; // برتقالي
      case NotificationType.promotion:
        return '#E91E63'; // وردي
      case NotificationType.system:
        return '#2196F3'; // أزرق
      case NotificationType.chat:
        return '#9C27B0'; // بنفسجي
      case NotificationType.payment:
        return '#F44336'; // أحمر
      case NotificationType.review:
        return '#FFEB3B'; // أصفر
      case NotificationType.general:
        return '#607D8B'; // رمادي
    }
  }
}

/// فئة مساعدة لأنواع الإشعارات
class NotificationTypeHelper {
  /// الحصول على جميع أنواع الإشعارات
  static List<NotificationType> get allTypes => NotificationType.values;

  /// الحصول على الأنواع المهمة
  static List<NotificationType> get importantTypes =>
      NotificationType.values.where((type) => type.isImportant).toList();

  /// الحصول على الأنواع التي تتطلب إجراء
  static List<NotificationType> get actionRequiredTypes =>
      NotificationType.values.where((type) => type.requiresAction).toList();

  /// الحصول على الأنواع العادية
  static List<NotificationType> get normalTypes =>
      NotificationType.values.where((type) => !type.isImportant).toList();

  /// فحص ما إذا كان النوع صالح
  static bool isValidType(String type) {
    try {
      NotificationTypeExtension.fromString(type);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على نوع افتراضي
  static NotificationType get defaultType => NotificationType.general;

  /// تجميع الأنواع حسب الأهمية
  static Map<String, List<NotificationType>> groupByImportance() {
    return {
      'important': importantTypes,
      'normal': normalTypes,
    };
  }

  /// تجميع الأنواع حسب الحاجة للإجراء
  static Map<String, List<NotificationType>> groupByActionRequired() {
    return {
      'actionRequired': actionRequiredTypes,
      'noAction': NotificationType.values
          .where((type) => !type.requiresAction)
          .toList(),
    };
  }
}
