import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/booking_statistics.dart';
import '../../../../core/models/time_slot_model.dart';
import '../repositories/booking_repository.dart' as repo;

class GetAvailableTimeSlotsUseCase implements UseCase<List<TimeSlotModel>, GetAvailableTimeSlotsParams> {
  final repo.BookingRepository repository;

  GetAvailableTimeSlotsUseCase(this.repository);

  @override
  Future<Either<Failure, List<TimeSlotModel>>> call(GetAvailableTimeSlotsParams params) async {
    return repository.getAvailableTimeSlots(
      params.providerId,
      params.date,
      params.serviceDurationMinutes,
    );
  }
}

class CreateAdvancedBookingUseCase implements UseCase<BookingModel, CreateAdvancedBookingParams> {
  final repo.BookingRepository repository;

  CreateAdvancedBookingUseCase(this.repository);

  @override
  Future<Either<Failure, BookingModel>> call(CreateAdvancedBookingParams params) async {
    // Validate booking parameters
    final validation = _validateBookingParams(params);
    if (validation != null) {
      return Left(ValidationFailure(message: validation));
    }

    // Check if time slot is still available
    final availableSlots = await repository.getAvailableTimeSlots(
      params.providerId,
      params.bookingDate,
      params.serviceDurationMinutes,
    );

    return availableSlots.fold(
      (failure) => Left(failure),
      (slots) {
        final isSlotAvailable = slots.any((slot) => 
          slot.id == params.timeSlotId && slot.isAvailable);
        
        if (!isSlotAvailable) {
          return Left(const BookingFailure(message: 'Selected time slot is no longer available'));
        }

        // Create booking
        return repository.createBooking(repo.CreateBookingParams(
          customerId: params.customerId,
          customerName: params.customerName,
          customerPhone: params.customerPhone,
          customerEmail: params.customerEmail,
          providerId: params.providerId,
          providerName: params.providerName,
          serviceId: params.serviceId,
          bookingDate: params.bookingDate,
          timeSlot: params.timeSlot,
          totalAmount: params.totalAmount,
          currency: params.currency,
          discountAmount: params.discountAmount,
          couponCode: params.couponCode,
          notes: params.notes,
          paymentInfo: params.paymentInfo,
        ));
      },
    );
  }

  String? _validateBookingParams(CreateAdvancedBookingParams params) {
    // Check if booking date is in the future
    if (params.bookingDate.isBefore(DateTime.now())) {
      return 'Booking date must be in the future';
    }

    // Check if booking is not too far in advance (e.g., 3 months)
    final maxAdvanceDate = DateTime.now().add(const Duration(days: 90));
    if (params.bookingDate.isAfter(maxAdvanceDate)) {
      return 'Booking date cannot be more than 3 months in advance';
    }

    // Check minimum notice period (e.g., 2 hours)
    final minNoticeTime = DateTime.now().add(const Duration(hours: 2));
    if (params.bookingDate.isBefore(minNoticeTime)) {
      return 'Booking must be made at least 2 hours in advance';
    }

    // Validate amount
    if (params.totalAmount <= 0) {
      return 'Total amount must be greater than zero';
    }

    return null;
  }
}

class RescheduleBookingUseCase implements UseCase<BookingModel, RescheduleBookingParams> {
  final repo.BookingRepository repository;

  RescheduleBookingUseCase(this.repository);

  @override
  Future<Either<Failure, BookingModel>> call(RescheduleBookingParams params) async {
    // Get current booking
    final currentBookingResult = await repository.getBookingById(params.bookingId);
    
    return currentBookingResult.fold(
      (failure) => Left(failure),
      (currentBooking) async {
        // Check if booking can be rescheduled
        if (!currentBooking.canBeModified) {
          return Left(const BookingFailure(message: 'This booking cannot be rescheduled'));
        }

        // Check if new time slot is available
        final availableSlots = await repository.getAvailableTimeSlots(
          currentBooking.providerId,
          params.newDate,
          currentBooking.service.durationMinutes,
        );

        return availableSlots.fold(
          (failure) => Left(failure),
          (slots) {
            final isSlotAvailable = slots.any((slot) =>
              slot.formattedTime == params.newTimeSlot && slot.isAvailable);
            
            if (!isSlotAvailable) {
              return Left(const BookingFailure(message: 'Selected time slot is not available'));
            }

            // Reschedule booking
            return repository.rescheduleBooking(
              params.bookingId,
              params.newDate,
              params.newTimeSlot,
              params.reason,
            );
          },
        );
      },
    );
  }
}

class GetBookingStatisticsUseCase implements UseCase<BookingStatistics, GetBookingStatisticsParams> {
  final repo.BookingRepository repository;

  GetBookingStatisticsUseCase(this.repository);

  @override
  Future<Either<Failure, BookingStatistics>> call(GetBookingStatisticsParams params) async {
    return await repository.getBookingStatistics(
      params.providerId,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

class SendBookingRemindersUseCase implements UseCase<void, NoParams> {
  final repo.BookingRepository repository;

  SendBookingRemindersUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(NoParams params) async {
    // Get bookings that need reminders (e.g., 24 hours before)
    final reminderTime = DateTime.now().add(const Duration(hours: 24));
    
    final upcomingBookingsResult = await repository.getUpcomingBookings(reminderTime);
    
    return upcomingBookingsResult.fold(
      (failure) => Left(failure),
      (bookings) async {
        // Send reminders for each booking
        for (final booking in bookings) {
          if (!booking.isReminderSent) {
            // Here you would integrate with notification service
            // For now, just mark as sent
            await repository.markReminderSent(booking.id);
          }
        }
        return const Right(null);
      },
    );
  }
}

// Parameter classes
class GetAvailableTimeSlotsParams {
  final String providerId;
  final DateTime date;
  final int serviceDurationMinutes;

  const GetAvailableTimeSlotsParams({
    required this.providerId,
    required this.date,
    required this.serviceDurationMinutes,
  });
}

class CreateAdvancedBookingParams {
  final String customerId;
  final String customerName;
  final String? customerPhone;
  final String? customerEmail;
  final String providerId;
  final String providerName;
  final String serviceId;
  final String timeSlotId;
  final DateTime bookingDate;
  final String timeSlot;
  final double totalAmount;
  final String currency;
  final double? discountAmount;
  final String? couponCode;
  final String? notes;
  final Map<String, dynamic>? paymentInfo;
  final int serviceDurationMinutes;

  const CreateAdvancedBookingParams({
    required this.customerId,
    required this.customerName,
    this.customerPhone,
    this.customerEmail,
    required this.providerId,
    required this.providerName,
    required this.serviceId,
    required this.timeSlotId,
    required this.bookingDate,
    required this.timeSlot,
    required this.totalAmount,
    this.currency = 'KD',
    this.discountAmount,
    this.couponCode,
    this.notes,
    this.paymentInfo,
    required this.serviceDurationMinutes,
  });
}

class RescheduleBookingParams {
  final String bookingId;
  final DateTime newDate;
  final String newTimeSlot;
  final String? reason;

  const RescheduleBookingParams({
    required this.bookingId,
    required this.newDate,
    required this.newTimeSlot,
    this.reason,
  });
}

class GetBookingStatisticsParams {
  final String providerId;
  final DateTime? startDate;
  final DateTime? endDate;

  const GetBookingStatisticsParams({
    required this.providerId,
    this.startDate,
    this.endDate,
  });
}

// Custom Failures
class BookingFailure extends Failure {
  const BookingFailure({required String message}) : super(message: message);
}

class ValidationFailure extends Failure {
  const ValidationFailure({required String message}) : super(message: message);
}
