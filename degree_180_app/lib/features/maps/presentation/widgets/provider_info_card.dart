import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class ProviderInfoCard extends StatelessWidget {
  final String providerId;
  final VoidCallback onClose;
  final VoidCallback onBookNow;
  final VoidCallback onViewProfile;

  const ProviderInfoCard({
    super.key,
    required this.providerId,
    required this.onClose,
    required this.onBookNow,
    required this.onViewProfile,
  });

  @override
  Widget build(BuildContext context) {
    final provider = _getProviderData(providerId);
    
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with close button
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    provider['name'],
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: onClose,
                  icon: const Icon(Icons.close),
                  color: AppColors.textGray,
                ),
              ],
            ),
          ),
          
          // Provider info
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
            ),
            child: Row(
              children: [
                // Provider image
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _getServiceColor(provider['type']).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                  ),
                  child: Icon(
                    _getServiceIcon(provider['type']),
                    color: _getServiceColor(provider['type']),
                    size: 30,
                  ),
                ),
                
                const SizedBox(width: AppConstants.defaultPadding),
                
                // Provider details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: AppColors.starYellow,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            provider['rating'].toString(),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.textDark,
                            ),
                          ),
                          const SizedBox(width: AppConstants.smallPadding),
                          Text(
                            '(${provider['reviewCount']} reviews)',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textGray,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            color: AppColors.textGray,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            provider['distance'],
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textGray,
                            ),
                          ),
                          const SizedBox(width: AppConstants.defaultPadding),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: provider['isOpen'] 
                                  ? AppColors.success.withValues(alpha: 0.1)
                                  : AppColors.error.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                            ),
                            child: Text(
                              provider['isOpen'] ? 'Open' : 'Closed',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: provider['isOpen'] ? AppColors.success : AppColors.error,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Action buttons
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onViewProfile,
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: AppColors.primaryPurple),
                    ),
                    child: const Text('View Profile'),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: ElevatedButton(
                    onPressed: provider['isOpen'] ? onBookNow : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryPurple,
                      foregroundColor: AppColors.textWhite,
                    ),
                    child: const Text('Book Now'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getProviderData(String providerId) {
    // Mock data - in real app, this would come from a service
    final providers = {
      '1': {
        'name': 'Ahmed Hassan Barbershop',
        'type': 'barbershop',
        'rating': 4.8,
        'reviewCount': 127,
        'distance': '0.5 km',
        'isOpen': true,
      },
      '2': {
        'name': 'Fatima Beauty Salon',
        'type': 'salon',
        'rating': 4.9,
        'reviewCount': 89,
        'distance': '1.2 km',
        'isOpen': true,
      },
      '3': {
        'name': 'Luxury Spa Center',
        'type': 'spa',
        'rating': 4.7,
        'reviewCount': 156,
        'distance': '2.1 km',
        'isOpen': false,
      },
    };

    return providers[providerId] ?? {
      'name': 'Unknown Provider',
      'type': 'unknown',
      'rating': 0.0,
      'reviewCount': 0,
      'distance': '0 km',
      'isOpen': false,
    };
  }

  Color _getServiceColor(String type) {
    switch (type) {
      case 'salon':
        return AppColors.salonColor;
      case 'barbershop':
        return AppColors.barbershopColor;
      case 'spa':
        return AppColors.spaColor;
      default:
        return AppColors.primaryPurple;
    }
  }

  IconData _getServiceIcon(String type) {
    switch (type) {
      case 'salon':
        return Icons.content_cut;
      case 'barbershop':
        return Icons.face;
      case 'spa':
        return Icons.spa;
      default:
        return Icons.store;
    }
  }
}
