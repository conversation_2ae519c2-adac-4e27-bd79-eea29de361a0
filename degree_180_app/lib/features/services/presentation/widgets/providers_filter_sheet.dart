import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/service_type.dart';

class ProvidersFilterSheet extends StatefulWidget {
  final ServiceType? selectedServiceType;
  final double selectedRadius;
  final double minRating;
  final bool isVerified;
  final bool isOnline;
  final Function({
    ServiceType? serviceType,
    double? radius,
    double? minRating,
    bool? isVerified,
    bool? isOnline,
  }) onApplyFilters;

  const ProvidersFilterSheet({
    super.key,
    this.selectedServiceType,
    required this.selectedRadius,
    required this.minRating,
    required this.isVerified,
    required this.isOnline,
    required this.onApplyFilters,
  });

  @override
  State<ProvidersFilterSheet> createState() => _ProvidersFilterSheetState();
}

class _ProvidersFilterSheetState extends State<ProvidersFilterSheet> {
  late ServiceType? _selectedServiceType;
  late double _selectedRadius;
  late double _minRating;
  late bool _isVerified;
  late bool _isOnline;

  @override
  void initState() {
    super.initState();
    _selectedServiceType = widget.selectedServiceType;
    _selectedRadius = widget.selectedRadius;
    _minRating = widget.minRating;
    _isVerified = widget.isVerified;
    _isOnline = widget.isOnline;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Providers',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedServiceType = null;
                      _selectedRadius = 10.0;
                      _minRating = 0.0;
                    });
                    // Clear filters
                  },
                  child: const Text('Clear All'),
                ),
              ],
            ),
          ),

          const Divider(),

          // Content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Service Type
                  Text(
                    'Service Type',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: ServiceType.values.map((type) {
                      final isSelected = _selectedServiceType == type;
                      return FilterChip(
                        label: Text(type.displayName),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedServiceType = selected ? type : null;
                          });
                        },
                        selectedColor: AppColors.primaryPurple.withValues(alpha: 0.2),
                        checkmarkColor: AppColors.primaryPurple,
                      );
                    }).toList(),
                  ),

                  const SizedBox(height: 24),

                  // Radius
                  Text(
                    'Distance (${_selectedRadius.toInt()} km)',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Slider(
                    value: _selectedRadius,
                    min: 1,
                    max: 50,
                    divisions: 49,
                    activeColor: AppColors.primaryPurple,
                    onChanged: (value) {
                      setState(() {
                        _selectedRadius = value;
                      });
                    },
                  ),

                  const SizedBox(height: 24),

                  // Rating
                  Text(
                    'Minimum Rating (${_minRating.toStringAsFixed(1)})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Slider(
                    value: _minRating,
                    min: 0,
                    max: 5,
                    divisions: 10,
                    activeColor: AppColors.primaryPurple,
                    onChanged: (value) {
                      setState(() {
                        _minRating = value;
                      });
                    },
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('0.0', style: Theme.of(context).textTheme.bodySmall),
                      Text('5.0', style: Theme.of(context).textTheme.bodySmall),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Actions
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onApplyFilters(
                        serviceType: _selectedServiceType,
                        radius: _selectedRadius,
                        minRating: _minRating,
                        isVerified: _isVerified,
                        isOnline: _isOnline,
                      );
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryPurple,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
