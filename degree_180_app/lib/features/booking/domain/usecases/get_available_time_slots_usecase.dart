import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../repositories/booking_repository.dart' hide TimeSlotModel;

class GetAvailableTimeSlotsUseCase implements UseCase<List<TimeSlotModel>, GetAvailableTimeSlotsParams> {
  final BookingRepository repository;

  GetAvailableTimeSlotsUseCase(this.repository);

  @override
  Future<Either<Failure, List<TimeSlotModel>>> call(GetAvailableTimeSlotsParams params) async {
    return repository.getAvailableTimeSlots(
      params.providerId,
      params.date,
      params.serviceDurationMinutes,
    );
  }
}

class GetAvailableTimeSlotsParams {
  final String providerId;
  final DateTime date;
  final int serviceDurationMinutes;

  const GetAvailableTimeSlotsParams({
    required this.providerId,
    required this.date,
    required this.serviceDurationMinutes,
  });
}
