import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/models/location_model.dart';
import '../../../services/presentation/bloc/service_provider_bloc.dart';
import '../../../services/presentation/widgets/service_provider_card.dart';

class NearbyProvidersSection extends StatelessWidget {
  final ServiceProviderState state;
  final LocationModel userLocation;
  final VoidCallback onSeeAll;

  const NearbyProvidersSection({
    super.key,
    required this.state,
    required this.userLocation,
    required this.onSeeAll,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Nearby Providers',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 14,
                        color: AppColors.textLight,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Within 10 km',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textLight,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              TextButton(
                onPressed: onSeeAll,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'See All',
                      style: TextStyle(
                        color: AppColors.primaryPurple,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: AppColors.primaryPurple,
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Content
          _buildContent(context),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (state is ServiceProviderLoading) {
      return const SizedBox(
        height: 200,
        child: EnhancedLoadingWidget(
          message: 'Finding nearby providers...',
        ),
      );
    }

    if (state is ServiceProviderError) {
      return _buildErrorState(context, state.message);
    }

    if (state is ServiceProviderEmpty) {
      return _buildEmptyState(context);
    }

    if (state is ServiceProviderLoaded) {
      return _buildProvidersList(context, state.providers);
    }

    return const SizedBox.shrink();
  }

  Widget _buildProvidersList(BuildContext context, List<ServiceProviderModel> providers) {
    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: providers.length,
        itemBuilder: (context, index) {
          final provider = providers[index];
          return Container(
            width: 280,
            margin: EdgeInsets.only(
              right: index < providers.length - 1 ? 16 : 0,
            ),
            child: ServiceProviderCard(
              provider: provider,
              userLocation: userLocation,
              showDistance: true,
              onTap: () => _navigateToProviderDetails(context, provider.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 48,
              color: AppColors.warning.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 8),
            Text(
              'Location Error',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.warning,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Unable to find nearby providers',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.warning.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: () {
                // Trigger location permission request
              },
              child: Text(
                'Enable Location',
                style: TextStyle(
                  color: AppColors.warning,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppColors.backgroundGray,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(
          color: AppColors.borderGray,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_searching,
              size: 48,
              color: AppColors.textLight.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 8),
            Text(
              'No Nearby Providers',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textLight,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Try expanding your search radius',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textLight.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: onSeeAll,
              child: Text(
                'Browse All Providers',
                style: TextStyle(
                  color: AppColors.primaryPurple,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToProviderDetails(BuildContext context, String providerId) {
    Navigator.pushNamed(
      context,
      '/provider-details',
      arguments: {'providerId': providerId},
    );
  }
}
