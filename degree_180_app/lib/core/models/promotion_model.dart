import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
// import '../enums/service_type.dart';

enum PromotionType {
  percentage,
  fixedAmount,
  buyOneGetOne,
  freeService,
  bundleDeal,
}

enum PromotionStatus {
  active,
  inactive,
  expired,
  scheduled,
}

class PromotionModel extends Equatable {
  final String id;
  final String title;
  final String description;
  final String? imageUrl;
  final PromotionType type;
  final PromotionStatus status;
  final double? discountPercentage;
  final double? discountAmount;
  final double? minimumAmount;
  final double? maximumDiscount;
  final DateTime startDate;
  final DateTime endDate;
  final String? promoCode;
  final int? usageLimit;
  final int usageCount;
  final int? userUsageLimit;
  final List<String> applicableServiceTypes;
  final List<String> applicableProviders;
  final List<String> excludedProviders;
  final bool isFirstTimeOnly;
  final bool isPublic;
  final String? providerId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? terms;

  const PromotionModel({
    required this.id,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.type,
    this.status = PromotionStatus.active,
    this.discountPercentage,
    this.discountAmount,
    this.minimumAmount,
    this.maximumDiscount,
    required this.startDate,
    required this.endDate,
    this.promoCode,
    this.usageLimit,
    this.usageCount = 0,
    this.userUsageLimit,
    this.applicableServiceTypes = const [],
    this.applicableProviders = const [],
    this.excludedProviders = const [],
    this.isFirstTimeOnly = false,
    this.isPublic = true,
    this.providerId,
    required this.createdAt,
    this.updatedAt,
    this.terms,
  });

  PromotionModel copyWith({
    String? id,
    String? title,
    String? description,
    String? imageUrl,
    PromotionType? type,
    PromotionStatus? status,
    double? discountPercentage,
    double? discountAmount,
    double? minimumAmount,
    double? maximumDiscount,
    DateTime? startDate,
    DateTime? endDate,
    String? promoCode,
    int? usageLimit,
    int? usageCount,
    int? userUsageLimit,
    List<String>? applicableServiceTypes,
    List<String>? applicableProviders,
    List<String>? excludedProviders,
    bool? isFirstTimeOnly,
    bool? isPublic,
    String? providerId,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? terms,
  }) {
    return PromotionModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      type: type ?? this.type,
      status: status ?? this.status,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      minimumAmount: minimumAmount ?? this.minimumAmount,
      maximumDiscount: maximumDiscount ?? this.maximumDiscount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      promoCode: promoCode ?? this.promoCode,
      usageLimit: usageLimit ?? this.usageLimit,
      usageCount: usageCount ?? this.usageCount,
      userUsageLimit: userUsageLimit ?? this.userUsageLimit,
      applicableServiceTypes: applicableServiceTypes ?? this.applicableServiceTypes,
      applicableProviders: applicableProviders ?? this.applicableProviders,
      excludedProviders: excludedProviders ?? this.excludedProviders,
      isFirstTimeOnly: isFirstTimeOnly ?? this.isFirstTimeOnly,
      isPublic: isPublic ?? this.isPublic,
      providerId: providerId ?? this.providerId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      terms: terms ?? this.terms,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'type': type.name,
      'status': status.name,
      'discountPercentage': discountPercentage,
      'discountAmount': discountAmount,
      'minimumAmount': minimumAmount,
      'maximumDiscount': maximumDiscount,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'promoCode': promoCode,
      'usageLimit': usageLimit,
      'usageCount': usageCount,
      'userUsageLimit': userUsageLimit,
      'applicableServiceTypes': applicableServiceTypes,
      'applicableProviders': applicableProviders,
      'excludedProviders': excludedProviders,
      'isFirstTimeOnly': isFirstTimeOnly,
      'isPublic': isPublic,
      'providerId': providerId,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'terms': terms,
    };
  }

  factory PromotionModel.fromMap(Map<String, dynamic> map) {
    return PromotionModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      imageUrl: map['imageUrl'],
      type: PromotionType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => PromotionType.percentage,
      ),
      status: PromotionStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => PromotionStatus.active,
      ),
      discountPercentage: map['discountPercentage']?.toDouble(),
      discountAmount: map['discountAmount']?.toDouble(),
      minimumAmount: map['minimumAmount']?.toDouble(),
      maximumDiscount: map['maximumDiscount']?.toDouble(),
      startDate: (map['startDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endDate: (map['endDate'] as Timestamp?)?.toDate() ?? DateTime.now().add(const Duration(days: 30)),
      promoCode: map['promoCode'],
      usageLimit: map['usageLimit'],
      usageCount: map['usageCount'] ?? 0,
      userUsageLimit: map['userUsageLimit'],
      applicableServiceTypes: List<String>.from(map['applicableServiceTypes'] ?? []),
      applicableProviders: List<String>.from(map['applicableProviders'] ?? []),
      excludedProviders: List<String>.from(map['excludedProviders'] ?? []),
      isFirstTimeOnly: map['isFirstTimeOnly'] ?? false,
      isPublic: map['isPublic'] ?? true,
      providerId: map['providerId'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
      terms: map['terms'] != null ? Map<String, dynamic>.from(map['terms']) : null,
    );
  }

  factory PromotionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PromotionModel.fromMap({...data, 'id': doc.id});
  }

  bool get isActive {
    final now = DateTime.now();
    return status == PromotionStatus.active &&
           now.isAfter(startDate) &&
           now.isBefore(endDate) &&
           (usageLimit == null || usageCount < usageLimit!);
  }

  bool get isExpired {
    final now = DateTime.now();
    return now.isAfter(endDate) || status == PromotionStatus.expired;
  }

  bool get isScheduled {
    final now = DateTime.now();
    return status == PromotionStatus.scheduled || now.isBefore(startDate);
  }

  String get discountText {
    switch (type) {
      case PromotionType.percentage:
        return '${discountPercentage?.toInt()}% OFF';
      case PromotionType.fixedAmount:
        return '${discountAmount?.toStringAsFixed(3)} KWD OFF';
      case PromotionType.buyOneGetOne:
        return 'Buy 1 Get 1 Free';
      case PromotionType.freeService:
        return 'Free Service';
      case PromotionType.bundleDeal:
        return 'Bundle Deal';
    }
  }

  String get validityText {
    final now = DateTime.now();
    final difference = endDate.difference(now);
    
    if (difference.inDays > 0) {
      return 'Valid for ${difference.inDays} more days';
    } else if (difference.inHours > 0) {
      return 'Valid for ${difference.inHours} more hours';
    } else {
      return 'Expires soon';
    }
  }

  double calculateDiscount(double amount) {
    if (!isActive) return 0.0;
    
    if (minimumAmount != null && amount < minimumAmount!) return 0.0;
    
    double discount = 0.0;
    
    switch (type) {
      case PromotionType.percentage:
        if (discountPercentage != null) {
          discount = amount * (discountPercentage! / 100);
        }
        break;
      case PromotionType.fixedAmount:
        if (discountAmount != null) {
          discount = discountAmount!;
        }
        break;
      default:
        // Other types need custom logic
        break;
    }
    
    if (maximumDiscount != null && discount > maximumDiscount!) {
      discount = maximumDiscount!;
    }
    
    return discount;
  }

  bool isApplicableToService(String serviceType, String? providerId) {
    // Check if provider is excluded
    if (excludedProviders.contains(providerId)) return false;
    
    // Check if specific providers are required
    if (applicableProviders.isNotEmpty && !applicableProviders.contains(providerId)) {
      return false;
    }
    
    // Check if specific service types are required
    if (applicableServiceTypes.isNotEmpty && !applicableServiceTypes.contains(serviceType)) {
      return false;
    }
    
    return true;
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        imageUrl,
        type,
        status,
        discountPercentage,
        discountAmount,
        minimumAmount,
        maximumDiscount,
        startDate,
        endDate,
        promoCode,
        usageLimit,
        usageCount,
        userUsageLimit,
        applicableServiceTypes,
        applicableProviders,
        excludedProviders,
        isFirstTimeOnly,
        isPublic,
        providerId,
        createdAt,
        updatedAt,
        terms,
      ];
}
