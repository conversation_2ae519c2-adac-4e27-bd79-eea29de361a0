import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../enums/gender.dart';
import '../theme/app_theme.dart';
import '../services/cache_service.dart';
import '../services/analytics_service.dart';

class ThemeService extends ChangeNotifier {
  static final ThemeService _instance = ThemeService._internal();
  factory ThemeService() => _instance;
  ThemeService._internal();

  final CacheService _cacheService = CacheService();
  final AnalyticsService _analytics = AnalyticsService();

  // Current theme state
  Gender _currentGender = Gender.notSpecified;
  bool _isDarkMode = false;
  ThemeData _currentTheme = AppTheme.maleTheme;

  // Getters
  Gender get currentGender => _currentGender;
  bool get isDarkMode => _isDarkMode;
  ThemeData get currentTheme => _currentTheme;
  bool get isFemaleTheme => _currentGender == Gender.female;
  bool get isMaleTheme => _currentGender == Gender.male;

  // Theme colors based on current gender
  Color get primaryColor {
    switch (_currentGender) {
      case Gender.female:
        return const Color(0xFFE91E63); // Pink
      case Gender.male:
      default:
        return const Color(0xFF6C63FF); // Purple
    }
  }

  Color get primaryColorLight {
    switch (_currentGender) {
      case Gender.female:
        return const Color(0xFFF8BBD9); // Light Pink
      case Gender.male:
      default:
        return const Color(0xFF9C88FF); // Light Purple
    }
  }

  Color get accentColor {
    switch (_currentGender) {
      case Gender.female:
        return const Color(0xFFFF69B4); // Hot Pink
      case Gender.male:
      default:
        return const Color(0xFF8B7CF6); // Light Purple
    }
  }

  List<Color> get gradientColors {
    switch (_currentGender) {
      case Gender.female:
        return [
          const Color(0xFFE91E63),
          const Color(0xFFF8BBD9),
        ];
      case Gender.male:
      default:
        return [
          const Color(0xFF6C63FF),
          const Color(0xFF9C88FF),
        ];
    }
  }

  // Initialize theme service
  Future<void> initialize() async {
    await _loadSavedTheme();
    _updateSystemUI();
  }

  // Load saved theme from cache
  Future<void> _loadSavedTheme() async {
    try {
      final savedGender = await _cacheService.getSetting<String>('user_gender');
      final savedDarkMode = await _cacheService.getSetting<bool>('dark_mode') ?? false;

      if (savedGender != null) {
        _currentGender = Gender.values.firstWhere(
          (gender) => gender.name == savedGender,
          orElse: () => Gender.notSpecified,
        );
      }
      _isDarkMode = savedDarkMode;
      
      _updateTheme();
    } catch (e) {
      debugPrint('Error loading saved theme: $e');
    }
  }

  // Set gender and update theme
  Future<void> setGender(Gender gender) async {
    if (_currentGender != gender) {
      _currentGender = gender;
      await _saveThemeSettings();
      _updateTheme();
      _updateSystemUI();
      
      // Track theme change
      _analytics.logCustomEvent('theme_changed', {
        'gender': gender.name,
        'is_dark': _isDarkMode,
      });
      
      notifyListeners();
    }
  }

  // Toggle dark mode
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    await _saveThemeSettings();
    _updateTheme();
    _updateSystemUI();
    
    // Track dark mode toggle
    _analytics.logCustomEvent('dark_mode_toggled', {
      'is_dark': _isDarkMode,
      'gender': _currentGender.name,
    });
    
    notifyListeners();
  }

  // Set dark mode
  Future<void> setDarkMode(bool isDark) async {
    if (_isDarkMode != isDark) {
      _isDarkMode = isDark;
      await _saveThemeSettings();
      _updateTheme();
      _updateSystemUI();
      notifyListeners();
    }
  }

  // Update current theme
  void _updateTheme() {
    _currentTheme = AppTheme.getThemeForGender(_currentGender, isDark: _isDarkMode);
  }

  // Save theme settings to cache
  Future<void> _saveThemeSettings() async {
    try {
      await _cacheService.saveSetting('user_gender', _currentGender.name);
      await _cacheService.saveSetting('dark_mode', _isDarkMode);
    } catch (e) {
      debugPrint('Error saving theme settings: $e');
    }
  }

  // Update system UI based on current theme
  void _updateSystemUI() {
    final brightness = _isDarkMode ? Brightness.dark : Brightness.light;
    final statusBarBrightness = _isDarkMode ? Brightness.light : Brightness.dark;
    
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: statusBarBrightness,
        statusBarIconBrightness: statusBarBrightness,
        systemNavigationBarColor: _currentTheme.scaffoldBackgroundColor,
        systemNavigationBarIconBrightness: brightness,
      ),
    );
  }

  // Get theme-specific icons
  IconData getGenderIcon() {
    switch (_currentGender) {
      case Gender.female:
        return Icons.female;
      case Gender.male:
        return Icons.male;
      default:
        return Icons.person;
    }
  }

  // Get theme-specific decorations
  BoxDecoration getPrimaryGradientDecoration({
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      gradient: LinearGradient(
        colors: gradientColors,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      boxShadow: boxShadow,
    );
  }

  // Get theme-specific button style
  ButtonStyle getPrimaryButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      elevation: 2,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  // Get theme-specific card decoration
  BoxDecoration getCardDecoration({
    Color? color,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: color ?? _currentTheme.cardColor,
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      boxShadow: boxShadow ?? [
        BoxShadow(
          color: primaryColor.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  // Get theme-specific input decoration
  InputDecoration getInputDecoration({
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: primaryColorLight),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: primaryColor, width: 2),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: primaryColorLight),
      ),
      filled: true,
      fillColor: _currentTheme.inputDecorationTheme.fillColor,
    );
  }

  // Get theme-specific app bar
  AppBar getThemedAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
  }) {
    return AppBar(
      title: Text(title),
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle.light,
    );
  }

  // Reset to default theme
  Future<void> resetToDefault() async {
    _currentGender = Gender.notSpecified;
    _isDarkMode = false;
    await _saveThemeSettings();
    _updateTheme();
    _updateSystemUI();
    notifyListeners();
  }

  // Get theme name for display
  String getThemeName() {
    String genderName = '';
    switch (_currentGender) {
      case Gender.female:
        genderName = 'أنثوي';
        break;
      case Gender.male:
        genderName = 'ذكوري';
        break;
      default:
        genderName = 'افتراضي';
    }
    
    return '$genderName${_isDarkMode ? ' - داكن' : ' - فاتح'}';
  }

  // Check if theme is suitable for gender
  bool isThemeSuitableForGender(Gender gender) {
    return _currentGender == gender || _currentGender == Gender.notSpecified;
  }

  // Auto-detect theme based on user preferences
  Future<void> autoDetectTheme(Gender userGender) async {
    await setGender(userGender);
    
    // Auto-detect dark mode based on system
    final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
    await setDarkMode(brightness == Brightness.dark);
  }
}
