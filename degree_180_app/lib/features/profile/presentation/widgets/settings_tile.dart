import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';

class SettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? trailing;
  final VoidCallback? onTap;
  final Color? textColor;
  final Color? iconColor;
  final bool enabled;
  final EdgeInsets? contentPadding;

  const SettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.trailing,
    this.onTap,
    this.textColor,
    this.iconColor,
    this.enabled = true,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveTextColor = enabled 
        ? (textColor ?? AppColors.textDark)
        : AppColors.textLight;
    
    final effectiveIconColor = enabled
        ? (iconColor ?? AppColors.textLight)
        : AppColors.textLight.withValues(alpha: 0.5);

    return InkWell(
      onTap: enabled ? onTap : null,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: contentPadding ?? const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            // Leading Icon
            if (icon != null) ...[
              Icon(
                icon,
                color: effectiveIconColor,
                size: 24,
              ),
              const SizedBox(width: 16),
            ],
            
            // Title and Subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: effectiveTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      subtitle!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textLight,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            
            // Trailing Widget
            if (trailing != null) ...[
              const SizedBox(width: 16),
              trailing!,
            ] else if (onTap != null && enabled) ...[
              const SizedBox(width: 16),
              Icon(
                Icons.chevron_right,
                color: AppColors.textLight,
                size: 20,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Settings tile with custom content
class CustomSettingsTile extends StatelessWidget {
  final Widget content;
  final VoidCallback? onTap;
  final bool enabled;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const CustomSettingsTile({
    super.key,
    required this.content,
    this.onTap,
    this.enabled = true,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: enabled ? onTap : null,
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      child: Container(
        padding: padding ?? const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        decoration: backgroundColor != null
            ? BoxDecoration(
                color: backgroundColor,
                borderRadius: borderRadius ?? BorderRadius.circular(8),
              )
            : null,
        child: content,
      ),
    );
  }
}

// Settings tile with badge
class BadgeSettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final String? badgeText;
  final Color? badgeColor;
  final VoidCallback? onTap;
  final bool enabled;

  const BadgeSettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.badgeText,
    this.badgeColor,
    this.onTap,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      trailing: badgeText != null
          ? Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: badgeColor ?? AppColors.primaryPurple,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                badgeText!,
                style: const TextStyle(
                  color: AppColors.textWhite,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          : null,
    );
  }
}

// Settings tile with value display
class ValueSettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final String value;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? valueColor;

  const ValueSettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
    this.onTap,
    this.enabled = true,
    this.valueColor,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: valueColor ?? AppColors.textLight,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (onTap != null && enabled) ...[
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              color: AppColors.textLight,
              size: 20,
            ),
          ],
        ],
      ),
    );
  }
}

// Settings tile with progress indicator
class ProgressSettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final double progress; // 0.0 to 1.0
  final String? progressText;
  final VoidCallback? onTap;
  final bool enabled;
  final Color? progressColor;

  const ProgressSettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.progress,
    this.progressText,
    this.onTap,
    this.enabled = true,
    this.progressColor,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      enabled: enabled,
      trailing: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (progressText != null)
            Text(
              progressText!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textLight,
              ),
            ),
          const SizedBox(height: 4),
          SizedBox(
            width: 60,
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: AppColors.borderGray,
              valueColor: AlwaysStoppedAnimation<Color>(
                progressColor ?? AppColors.primaryPurple,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Settings tile with loading state
class LoadingSettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool isLoading;
  final VoidCallback? onTap;

  const LoadingSettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.isLoading,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: isLoading ? null : onTap,
      enabled: !isLoading,
      trailing: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryPurple),
              ),
            )
          : null,
    );
  }
}

// Settings tile with status indicator
class StatusSettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool isActive;
  final String? activeText;
  final String? inactiveText;
  final VoidCallback? onTap;
  final Color? activeColor;
  final Color? inactiveColor;

  const StatusSettingsTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.isActive,
    this.activeText,
    this.inactiveText,
    this.onTap,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) {
    final statusColor = isActive 
        ? (activeColor ?? AppColors.success)
        : (inactiveColor ?? AppColors.error);
    
    final statusText = isActive 
        ? (activeText ?? 'Active')
        : (inactiveText ?? 'Inactive');

    return SettingsTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      onTap: onTap,
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (onTap != null) ...[
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              color: AppColors.textLight,
              size: 20,
            ),
          ],
        ],
      ),
    );
  }
}
