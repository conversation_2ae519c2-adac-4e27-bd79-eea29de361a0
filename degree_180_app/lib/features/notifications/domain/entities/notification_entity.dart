import 'package:equatable/equatable.dart';

enum NotificationType {
  booking,
  message,
  review,
  promotion,
  reminder,
  system,
  payment,
}

enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

class NotificationEntity extends Equatable {
  final String id;
  final String userId;
  final NotificationType type;
  final String title;
  final String body;
  final String? imageUrl;
  final Map<String, dynamic>? data;
  final NotificationPriority priority;
  final bool isRead;
  final bool isActionable;
  final String? actionUrl;
  final String? actionText;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? expiresAt;

  const NotificationEntity({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.body,
    this.imageUrl,
    this.data,
    this.priority = NotificationPriority.normal,
    this.isRead = false,
    this.isActionable = false,
    this.actionUrl,
    this.actionText,
    required this.createdAt,
    this.readAt,
    this.expiresAt,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        title,
        body,
        imageUrl,
        data,
        priority,
        isRead,
        isActionable,
        actionUrl,
        actionText,
        createdAt,
        readAt,
        expiresAt,
      ];

  NotificationEntity copyWith({
    String? id,
    String? userId,
    NotificationType? type,
    String? title,
    String? body,
    String? imageUrl,
    Map<String, dynamic>? data,
    NotificationPriority? priority,
    bool? isRead,
    bool? isActionable,
    String? actionUrl,
    String? actionText,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? expiresAt,
  }) {
    return NotificationEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      title: title ?? this.title,
      body: body ?? this.body,
      imageUrl: imageUrl ?? this.imageUrl,
      data: data ?? this.data,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
      isActionable: isActionable ?? this.isActionable,
      actionUrl: actionUrl ?? this.actionUrl,
      actionText: actionText ?? this.actionText,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  String get typeString {
    switch (type) {
      case NotificationType.booking:
        return 'booking';
      case NotificationType.message:
        return 'message';
      case NotificationType.review:
        return 'review';
      case NotificationType.promotion:
        return 'promotion';
      case NotificationType.reminder:
        return 'reminder';
      case NotificationType.system:
        return 'system';
      case NotificationType.payment:
        return 'payment';
    }
  }

  String get priorityString {
    switch (priority) {
      case NotificationPriority.low:
        return 'low';
      case NotificationPriority.normal:
        return 'normal';
      case NotificationPriority.high:
        return 'high';
      case NotificationPriority.urgent:
        return 'urgent';
    }
  }

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 24;
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get typeIcon {
    switch (type) {
      case NotificationType.booking:
        return '📅';
      case NotificationType.message:
        return '💬';
      case NotificationType.review:
        return '⭐';
      case NotificationType.promotion:
        return '🎉';
      case NotificationType.reminder:
        return '⏰';
      case NotificationType.system:
        return '🔧';
      case NotificationType.payment:
        return '💳';
    }
  }

  NotificationEntity markAsRead() {
    return copyWith(
      isRead: true,
      readAt: DateTime.now(),
    );
  }
}
