import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';

class DistanceSlider extends StatelessWidget {
  final double value;
  final Function(double) onChanged;

  const DistanceSlider({
    super.key,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Slider(
          value: value,
          min: 1,
          max: 50,
          divisions: 49,
          activeColor: AppColors.primaryPurple,
          inactiveColor: AppColors.primaryPurple.withValues(alpha: 0.3),
          onChanged: onChanged,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '1 km',
              style: TextStyle(
                color: AppColors.textGray,
              ),
            ),
            Text(
              '${value.toInt()} km',
              style: const TextStyle(
                color: AppColors.textDark,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Text(
              '50 km',
              style: TextStyle(
                color: AppColors.textGray,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
