import 'package:flutter/material.dart';

class FemaleColors {
  // Primary Pink Colors
  static const Color primaryPink = Color(0xFFE91E63);
  static const Color primaryPinkLight = Color(0xFFF8BBD9);
  static const Color primaryPinkDark = Color(0xFFC2185B);
  static const Color primaryPinkExtraLight = Color(0xFFFCE4EC);

  // Rose Gold Colors
  static const Color roseGold = Color(0xFFE8B4B8);
  static const Color roseGoldLight = Color(0xFFF5D5D8);
  static const Color roseGoldDark = Color(0xFFD4969B);

  // Accent Colors
  static const Color accentRose = Color(0xFFFF69B4);
  static const Color accentCoral = Color(0xFFFF7F7F);
  static const Color accentLavender = Color(0xFFE6E6FA);
  static const Color accentPeach = Color(0xFFFFDAB9);

  // Soft Pastels
  static const Color softPink = Color(0xFFFCE4EC);
  static const Color softLavender = Color(0xFFF3E5F5);
  static const Color softPeach = Color(0xFFFFF3E0);
  static const Color softMint = Color(0xFFE8F5E8);

  // Gradient Colors
  static const List<Color> pinkGradient = [
    Color(0xFFE91E63),
    Color(0xFFF8BBD9),
  ];

  static const List<Color> roseGoldGradient = [
    Color(0xFFE8B4B8),
    Color(0xFFF5D5D8),
  ];

  static const List<Color> sunsetGradient = [
    Color(0xFFFF69B4),
    Color(0xFFFF7F7F),
    Color(0xFFFFDAB9),
  ];

  // Background Colors
  static const Color backgroundPrimary = Color(0xFFFFFBFE);
  static const Color backgroundSecondary = Color(0xFFFCE4EC);
  static const Color backgroundCard = Color(0xFFFFFFFF);
  static const Color backgroundOverlay = Color(0x80FCE4EC);

  // Text Colors
  static const Color textPrimary = Color(0xFF2D2D2D);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFF2D2D2D);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Border Colors
  static const Color borderPrimary = Color(0xFFE8B4B8);
  static const Color borderSecondary = Color(0xFFF5D5D8);
  static const Color borderLight = Color(0xFFFCE4EC);

  // Shadow Colors
  static const Color shadowLight = Color(0x1AE91E63);
  static const Color shadowMedium = Color(0x33E91E63);
  static const Color shadowDark = Color(0x4DE91E63);

  // Special Effect Colors
  static const Color shimmerBase = Color(0xFFFCE4EC);
  static const Color shimmerHighlight = Color(0xFFFFFFFF);
  static const Color ripple = Color(0x33E91E63);

  // Category Colors for Female Theme
  static const Color hairSalon = Color(0xFFE91E63);
  static const Color nails = Color(0xFFFF69B4);
  static const Color spa = Color(0xFFE6E6FA);
  static const Color skincare = Color(0xFFFFDAB9);
  static const Color makeup = Color(0xFFF8BBD9);
  static const Color massage = Color(0xFFE8F5E8);

  // Rating Colors
  static const Color ratingExcellent = Color(0xFFE91E63);
  static const Color ratingGood = Color(0xFFFF69B4);
  static const Color ratingAverage = Color(0xFFFFDAB9);
  static const Color ratingPoor = Color(0xFFFF7F7F);

  // Booking Status Colors
  static const Color bookingConfirmed = Color(0xFF4CAF50);
  static const Color bookingPending = Color(0xFFFF9800);
  static const Color bookingCancelled = Color(0xFFF44336);
  static const Color bookingCompleted = Color(0xFFE91E63);

  // Helper methods
  static LinearGradient get primaryGradient => const LinearGradient(
    colors: pinkGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static LinearGradient get roseGoldLinearGradient => const LinearGradient(
    colors: roseGoldGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static LinearGradient get sunsetLinearGradient => const LinearGradient(
    colors: sunsetGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static RadialGradient get primaryRadialGradient => const RadialGradient(
    colors: pinkGradient,
    center: Alignment.center,
    radius: 1.0,
  );

  static BoxShadow get softShadow => BoxShadow(
    color: shadowLight,
    blurRadius: 8,
    offset: const Offset(0, 2),
  );

  static BoxShadow get mediumShadow => BoxShadow(
    color: shadowMedium,
    blurRadius: 12,
    offset: const Offset(0, 4),
  );

  static BoxShadow get strongShadow => BoxShadow(
    color: shadowDark,
    blurRadius: 16,
    offset: const Offset(0, 6),
  );

  // Color with opacity helpers
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}
