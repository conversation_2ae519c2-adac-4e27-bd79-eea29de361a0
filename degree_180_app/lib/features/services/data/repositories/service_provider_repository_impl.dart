import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/models/service_model.dart';
import '../../../../core/models/review_model.dart';
import '../../../../core/models/location_model.dart';
import '../../../../core/enums/service_type.dart';
import '../../domain/repositories/service_provider_repository.dart';
import '../datasources/service_provider_remote_data_source.dart';

class ServiceProviderRepositoryImpl implements ServiceProviderRepository {
  final ServiceProviderRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  ServiceProviderRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<ServiceProviderModel>>> getServiceProviders({
    ServiceType? serviceType,
    LocationModel? userLocation,
    double? radiusKm,
    int? limit,
    String? lastDocumentId,
    bool? isVerified,
    bool? isOnline,
    double? minRating,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final providers = await remoteDataSource.getServiceProviders(
          serviceType: serviceType,
          userLocation: userLocation,
          radiusKm: radiusKm,
          limit: limit,
          lastDocumentId: lastDocumentId,
          isVerified: isVerified,
          isOnline: isOnline,
          minRating: minRating,
        );
        return Right(providers);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ServiceProviderModel>> getServiceProviderById(String providerId) async {
    if (await networkInfo.isConnected) {
      try {
        final provider = await remoteDataSource.getServiceProviderById(providerId);
        return Right(provider);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ServiceModel>>> getProviderServices(
    String providerId, {
    bool? isActive,
    String? category,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final services = await remoteDataSource.getProviderServices(
          providerId,
          isActive: isActive,
          category: category,
        );
        return Right(services);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ReviewModel>>> getProviderReviews(
    String providerId, {
    int? limit,
    String? lastDocumentId,
    double? minRating,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final reviews = await remoteDataSource.getProviderReviews(
          providerId,
          limit: limit,
          lastDocumentId: lastDocumentId,
          minRating: minRating,
        );
        return Right(reviews);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ServiceProviderModel>>> searchServiceProviders(
    String query, {
    ServiceType? serviceType,
    LocationModel? userLocation,
    double? radiusKm,
    int? limit,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final providers = await remoteDataSource.searchServiceProviders(
          query,
          serviceType: serviceType,
          userLocation: userLocation,
          radiusKm: radiusKm,
          limit: limit,
        );
        return Right(providers);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ServiceProviderModel>>> getNearbyProviders(
    LocationModel userLocation, {
    double radiusKm = 10.0,
    ServiceType? serviceType,
    int? limit,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final providers = await remoteDataSource.getNearbyProviders(
          userLocation,
          radiusKm: radiusKm,
          serviceType: serviceType,
          limit: limit,
        );
        return Right(providers);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ServiceProviderModel>>> getFeaturedProviders({
    ServiceType? serviceType,
    int? limit,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final providers = await remoteDataSource.getFeaturedProviders(
          serviceType: serviceType,
          limit: limit,
        );
        return Right(providers);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getProviderAvailability(
    String providerId,
    DateTime date,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final availability = await remoteDataSource.getProviderAvailability(providerId, date);
        return Right(availability);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> updateProviderOnlineStatus(
    String providerId,
    bool isOnline,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.updateProviderOnlineStatus(providerId, isOnline);
        return const Right(null);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ServiceModel>> addProviderService(
    String providerId,
    ServiceModel service,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final newService = await remoteDataSource.addProviderService(providerId, service);
        return Right(newService);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ServiceModel>> updateProviderService(
    String providerId,
    ServiceModel service,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedService = await remoteDataSource.updateProviderService(providerId, service);
        return Right(updatedService);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteProviderService(
    String providerId,
    String serviceId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteProviderService(providerId, serviceId);
        return const Right(null);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getProviderStatistics(
    String providerId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final statistics = await remoteDataSource.getProviderStatistics(providerId);
        return Right(statistics);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ServiceProviderModel>> updateProviderProfile(
    ServiceProviderModel provider,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedProvider = await remoteDataSource.updateProviderProfile(provider);
        return Right(updatedProvider);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> uploadProviderGallery(
    String providerId,
    List<String> imagePaths,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final imageUrls = await remoteDataSource.uploadProviderGallery(providerId, imagePaths);
        return Right(imageUrls);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteProviderGalleryImage(
    String providerId,
    String imageUrl,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteProviderGalleryImage(providerId, imageUrl);
        return const Right(null);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
