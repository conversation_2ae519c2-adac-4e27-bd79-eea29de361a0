import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/theme_manager.dart';

class UserTypeCard extends StatelessWidget {
  final String title;
  final String titleAr;
  final VoidCallback onTap;
  final Color? backgroundColor;
  final Color? textColor;

  const UserTypeCard({
    super.key,
    required this.title,
    required this.titleAr,
    required this.onTap,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.largePadding,
            vertical: AppConstants.defaultPadding,
          ),
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.buttonGray.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            border: Border.all(
              color: AppColors.textWhite.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: ThemeManager.currentColors.primary.withValues(alpha: 0.1),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // English Title
              Text(
                title,
                style: TextStyle(
                  fontSize: AppConstants.fontSizeRegular,
                  fontWeight: FontWeight.w500,
                  color: textColor ?? AppColors.textWhite,
                ),
              ),
              
              // Arabic Title
              Text(
                titleAr,
                style: TextStyle(
                  fontSize: AppConstants.fontSizeRegular,
                  fontWeight: FontWeight.w500,
                  color: textColor ?? AppColors.textWhite,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
