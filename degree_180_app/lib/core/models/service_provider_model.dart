import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import '../enums/service_type.dart';
// import '../enums/user_type.dart';
import 'location_model.dart';
import 'working_hours_model.dart';
import 'service_model.dart';

class ServiceProviderModel extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? phoneNumber;
  final String? profileImageUrl;
  final ServiceType serviceType;
  final String? description;
  final List<String> galleryImages;
  final LocationModel? location;
  final double rating;
  final int reviewCount;
  final int reviewsCount; // اسم بديل للتوافق
  final bool isActive;
  final bool isVerified;
  final bool isOnline;
  final WorkingHoursModel? workingHours;
  final List<ServiceModel> services;
  final Map<String, dynamic>? socialMedia;
  final Map<String, dynamic>? businessInfo;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ServiceProviderModel({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    this.profileImageUrl,
    required this.serviceType,
    this.description,
    this.galleryImages = const [],
    this.location,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.reviewsCount = 0,
    this.isActive = true,
    this.isVerified = false,
    this.isOnline = false,
    this.workingHours,
    this.services = const [],
    this.socialMedia,
    this.businessInfo,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ServiceProviderModel.fromJson(Map<String, dynamic> json) {
    return ServiceProviderModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phoneNumber: json['phoneNumber'],
      profileImageUrl: json['profileImageUrl'],
      serviceType: ServiceType.values.firstWhere(
        (e) => e.toString().split('.').last == json['serviceType'],
        orElse: () => ServiceType.salon,
      ),
      description: json['description'],
      galleryImages: List<String>.from(json['galleryImages'] ?? []),
      location: json['location'] != null 
          ? LocationModel.fromJson(json['location']) 
          : null,
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      reviewsCount: json['reviewsCount'] ?? json['reviewCount'] ?? 0,
      isActive: json['isActive'] ?? true,
      isVerified: json['isVerified'] ?? false,
      isOnline: json['isOnline'] ?? false,
      workingHours: json['workingHours'] != null
          ? WorkingHoursModel.fromJson(json['workingHours'])
          : null,
      services: (json['services'] as List<dynamic>?)
          ?.map((service) => ServiceModel.fromJson(service))
          .toList() ?? [],
      socialMedia: json['socialMedia'],
      businessInfo: json['businessInfo'],
      createdAt: _parseDateTime(json['createdAt']),
      updatedAt: _parseDateTime(json['updatedAt']),
    );
  }

  factory ServiceProviderModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    data['id'] = doc.id;
    return ServiceProviderModel.fromJson(data);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'profileImageUrl': profileImageUrl,
      'serviceType': serviceType.toString().split('.').last,
      'description': description,
      'galleryImages': galleryImages,
      'location': location?.toJson(),
      'rating': rating,
      'reviewCount': reviewCount,
      'reviewsCount': reviewsCount,
      'isActive': isActive,
      'isVerified': isVerified,
      'isOnline': isOnline,
      'workingHours': workingHours?.toJson(),
      'services': services.map((service) => service.toJson()).toList(),
      'socialMedia': socialMedia,
      'businessInfo': businessInfo,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id'); // Remove id as it's handled by Firestore document ID
    return json;
  }

  ServiceProviderModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phoneNumber,
    String? profileImageUrl,
    ServiceType? serviceType,
    String? description,
    List<String>? galleryImages,
    LocationModel? location,
    double? rating,
    int? reviewCount,
    int? reviewsCount,
    bool? isActive,
    bool? isVerified,
    bool? isOnline,
    WorkingHoursModel? workingHours,
    List<ServiceModel>? services,
    Map<String, dynamic>? socialMedia,
    Map<String, dynamic>? businessInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceProviderModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      serviceType: serviceType ?? this.serviceType,
      description: description ?? this.description,
      galleryImages: galleryImages ?? this.galleryImages,
      location: location ?? this.location,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      isOnline: isOnline ?? this.isOnline,
      workingHours: workingHours ?? this.workingHours,
      services: services ?? this.services,
      socialMedia: socialMedia ?? this.socialMedia,
      businessInfo: businessInfo ?? this.businessInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is String) return DateTime.parse(value);
    if (value is DateTime) return value;
    return DateTime.now();
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phoneNumber,
        profileImageUrl,
        serviceType,
        description,
        galleryImages,
        location,
        rating,
        reviewCount,
        reviewsCount,
        isActive,
        isVerified,
        isOnline,
        workingHours,
        services,
        socialMedia,
        businessInfo,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'ServiceProviderModel(id: $id, name: $name, serviceType: $serviceType, rating: $rating)';
  }
}
