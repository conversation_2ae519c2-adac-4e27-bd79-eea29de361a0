import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/models/booking_model.dart';
import '../repositories/booking_repository.dart' as repo;

class CreateAdvancedBookingUseCase implements UseCase<BookingModel, CreateAdvancedBookingParams> {
  final repo.BookingRepository repository;

  CreateAdvancedBookingUseCase(this.repository);

  @override
  Future<Either<Failure, BookingModel>> call(CreateAdvancedBookingParams params) async {
    // تحويل المعاملات إلى CreateBookingParams
    final createParams = repo.CreateBookingParams(
      customerId: params.customerId,
      customerName: params.customerName,
      customerPhone: params.customerPhone,
      customerEmail: params.customerEmail,
      providerId: params.providerId,
      providerName: params.providerName,
      serviceId: params.serviceId,
      bookingDate: params.bookingDate,
      timeSlot: params.timeSlot,
      totalAmount: params.totalAmount,
      currency: params.currency,
      notes: params.notes,
    );

    return await repository.createBooking(createParams);
  }
}

/// معاملات إنشاء الحجز المتقدم
class CreateAdvancedBookingParams {
  final String customerId;
  final String customerName;
  final String? customerPhone;
  final String? customerEmail;
  final String providerId;
  final String providerName;
  final String serviceId;
  final String serviceName;
  final dynamic service;
  final DateTime bookingDate;
  final DateTime appointmentDateTime;
  final String timeSlot;
  final double totalAmount;
  final String currency;
  final String? notes;
  final dynamic location;

  const CreateAdvancedBookingParams({
    required this.customerId,
    required this.customerName,
    this.customerPhone,
    this.customerEmail,
    required this.providerId,
    required this.providerName,
    required this.serviceId,
    required this.serviceName,
    required this.service,
    required this.bookingDate,
    required this.appointmentDateTime,
    required this.timeSlot,
    required this.totalAmount,
    required this.currency,
    this.notes,
    this.location,
  });
}
