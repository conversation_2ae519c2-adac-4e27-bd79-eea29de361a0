import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';

class ServiceProviderCard extends StatelessWidget {
  final String name;
  final String serviceType;
  final double rating;
  final int reviewCount;
  final String? imageUrl;
  final bool isAvailable;
  final VoidCallback? onTap;

  const ServiceProviderCard({
    super.key,
    required this.name,
    required this.serviceType,
    required this.rating,
    required this.reviewCount,
    this.imageUrl,
    required this.isAvailable,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.backgroundWhite,
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            border: Border.all(
              color: AppColors.borderGray,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Profile Image
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.primaryPurple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                ),
                child: imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                        child: Image.network(
                          imageUrl!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : const Icon(
                        Icons.person,
                        color: AppColors.primaryPurple,
                        size: 30,
                      ),
              ),
              
              const SizedBox(width: AppConstants.defaultPadding),
              
              // Provider Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            name,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppColors.textDark,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.smallPadding,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: isAvailable 
                                ? AppColors.success.withValues(alpha: 0.1)
                                : AppColors.error.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                          ),
                          child: Text(
                            isAvailable ? AppStrings.available : AppStrings.busy,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: isAvailable ? AppColors.success : AppColors.error,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppConstants.smallPadding),
                    
                    Text(
                      _getServiceTypeDisplayName(serviceType),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textGray,
                      ),
                    ),
                    
                    const SizedBox(height: AppConstants.smallPadding),
                    
                    Row(
                      children: [
                        // Rating
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: AppColors.starYellow,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              rating.toString(),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textDark,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '($reviewCount)',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textGray,
                              ),
                            ),
                          ],
                        ),
                        
                        const Spacer(),
                        
                        // Service Type Icon
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: _getServiceTypeColor(serviceType).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                          ),
                          child: Icon(
                            _getServiceTypeIcon(serviceType),
                            color: _getServiceTypeColor(serviceType),
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getServiceTypeDisplayName(String serviceType) {
    switch (serviceType) {
      case 'salon':
        return AppStrings.salon;
      case 'barbershop':
        return AppStrings.barbershop;
      case 'spa':
        return AppStrings.spa;
      default:
        return serviceType;
    }
  }

  Color _getServiceTypeColor(String serviceType) {
    switch (serviceType) {
      case 'salon':
        return AppColors.salonColor;
      case 'barbershop':
        return AppColors.barbershopColor;
      case 'spa':
        return AppColors.spaColor;
      default:
        return AppColors.primaryPurple;
    }
  }

  IconData _getServiceTypeIcon(String serviceType) {
    switch (serviceType) {
      case 'salon':
        return Icons.content_cut;
      case 'barbershop':
        return Icons.face;
      case 'spa':
        return Icons.spa;
      default:
        return Icons.star;
    }
  }
}
