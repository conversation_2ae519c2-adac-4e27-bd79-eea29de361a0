import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/notification_card.dart';
import '../widgets/notification_filter_tabs.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.notification),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _markAllAsRead,
            icon: const Icon(Icons.done_all),
            tooltip: 'Mark all as read',
          ),
          IconButton(
            onPressed: _showNotificationSettings,
            icon: const Icon(Icons.settings),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Filter Tabs
            Container(
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: NotificationFilterTabs(
                selectedFilter: _selectedFilter,
                onFilterChanged: (filter) {
                  setState(() {
                    _selectedFilter = filter;
                  });
                },
              ),
            ),
            
            // Notifications List
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: AppColors.backgroundWhite,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.largeBorderRadius),
                    topRight: Radius.circular(AppConstants.largeBorderRadius),
                  ),
                ),
                child: _buildNotificationsList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsList() {
    final notifications = _getFilteredNotifications();
    
    if (notifications.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: NotificationCard(
            notification: notification,
            onTap: () => _handleNotificationTap(notification),
            onDismiss: () => _dismissNotification(notification['id']),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    String message;
    IconData icon;
    
    switch (_selectedFilter) {
      case 'unread':
        message = 'No unread notifications';
        icon = Icons.mark_email_read;
        break;
      case 'booking':
        message = 'No booking notifications';
        icon = Icons.calendar_today;
        break;
      case 'message':
        message = 'No message notifications';
        icon = Icons.message;
        break;
      case 'promotion':
        message = 'No promotion notifications';
        icon = Icons.local_offer;
        break;
      default:
        message = 'No notifications yet';
        icon = Icons.notifications_none;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textGray.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textGray,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You\'re all caught up!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textGray,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredNotifications() {
    final allNotifications = _getMockNotifications();
    
    switch (_selectedFilter) {
      case 'unread':
        return allNotifications.where((n) => !(n['isRead'] as bool)).toList();
      case 'booking':
        return allNotifications.where((n) => n['type'] == 'booking').toList();
      case 'message':
        return allNotifications.where((n) => n['type'] == 'message').toList();
      case 'promotion':
        return allNotifications.where((n) => n['type'] == 'promotion').toList();
      default:
        return allNotifications;
    }
  }

  List<Map<String, dynamic>> _getMockNotifications() {
    return [
      {
        'id': '1',
        'type': 'booking',
        'title': 'Booking Confirmed',
        'body': 'Your appointment with Ahmed Hassan is confirmed for tomorrow at 3:00 PM',
        'time': DateTime.now().subtract(const Duration(minutes: 5)),
        'isRead': false,
        'priority': 'high',
        'actionable': true,
        'actionText': 'View Details',
      },
      {
        'id': '2',
        'type': 'message',
        'title': 'New Message',
        'body': 'Ahmed Hassan: Thanks for booking! See you tomorrow.',
        'time': DateTime.now().subtract(const Duration(minutes: 15)),
        'isRead': false,
        'priority': 'normal',
        'actionable': true,
        'actionText': 'Reply',
      },
      {
        'id': '3',
        'type': 'promotion',
        'title': 'Special Offer',
        'body': '20% off on all hair styling services this weekend!',
        'time': DateTime.now().subtract(const Duration(hours: 2)),
        'isRead': true,
        'priority': 'normal',
        'actionable': true,
        'actionText': 'View Offer',
      },
      {
        'id': '4',
        'type': 'reminder',
        'title': 'Appointment Reminder',
        'body': 'Don\'t forget your appointment tomorrow at 3:00 PM',
        'time': DateTime.now().subtract(const Duration(hours: 4)),
        'isRead': true,
        'priority': 'normal',
        'actionable': false,
      },
      {
        'id': '5',
        'type': 'review',
        'title': 'Review Request',
        'body': 'How was your experience with Ahmed Hassan? Please leave a review.',
        'time': DateTime.now().subtract(const Duration(days: 1)),
        'isRead': false,
        'priority': 'low',
        'actionable': true,
        'actionText': 'Write Review',
      },
      {
        'id': '6',
        'type': 'payment',
        'title': 'Payment Successful',
        'body': 'Your payment of 25 KD has been processed successfully.',
        'time': DateTime.now().subtract(const Duration(days: 2)),
        'isRead': true,
        'priority': 'normal',
        'actionable': true,
        'actionText': 'View Receipt',
      },
    ];
  }

  void _handleNotificationTap(Map<String, dynamic> notification) {
    // Mark as read
    if (!(notification['isRead'] as bool)) {
      setState(() {
        notification['isRead'] = true;
      });
    }

    // Handle navigation based on type
    final type = notification['type'] as String;
    switch (type) {
      case 'booking':
        Navigator.pushNamed(context, '/booking-details');
        break;
      case 'message':
        Navigator.pushNamed(context, '/chat');
        break;
      case 'promotion':
        Navigator.pushNamed(context, '/promotions');
        break;
      case 'review':
        Navigator.pushNamed(context, '/write-review');
        break;
      case 'payment':
        Navigator.pushNamed(context, '/payment-history');
        break;
    }
  }

  void _dismissNotification(String notificationId) {
    setState(() {
      // Remove notification from list
      // In real app, this would call a service to delete the notification
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notification dismissed'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _markAllAsRead() {
    setState(() {
      // Mark all notifications as read
      // In real app, this would call a service
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All notifications marked as read'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showNotificationSettings() {
    Navigator.pushNamed(context, '/notification-settings');
  }
}
