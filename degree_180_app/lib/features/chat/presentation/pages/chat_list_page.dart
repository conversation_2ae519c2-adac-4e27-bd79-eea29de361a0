import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/chat_list_item.dart';
import '../widgets/chat_search_bar.dart';

class ChatListPage extends StatefulWidget {
  const ChatListPage({super.key});

  @override
  State<ChatListPage> createState() => _ChatListPageState();
}

class _ChatListPageState extends State<ChatListPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.chats),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showChatOptions,
            icon: const Icon(Icons.more_vert),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Search Bar
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: ChatSearchBar(
                controller: _searchController,
                onChanged: (query) {
                  setState(() {
                    _searchQuery = query;
                  });
                },
              ),
            ),
            
            // Chat List
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: AppColors.backgroundWhite,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.largeBorderRadius),
                    topRight: Radius.circular(AppConstants.largeBorderRadius),
                  ),
                ),
                child: _buildChatList(),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _startNewChat,
        backgroundColor: AppColors.primaryPurple,
        child: const Icon(
          Icons.chat,
          color: AppColors.textWhite,
        ),
      ),
    );
  }

  Widget _buildChatList() {
    final chats = _getFilteredChats();
    
    if (chats.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: chats.length,
      itemBuilder: (context, index) {
        final chat = chats[index];
        return ChatListItem(
          chat: chat,
          onTap: () => _openChat(chat['id']),
          onLongPress: () => _showChatActions(chat),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: AppColors.textGray.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No conversations yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textGray,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Start a conversation with a service provider',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textGray,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton(
            onPressed: _startNewChat,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryPurple,
              foregroundColor: AppColors.textWhite,
            ),
            child: const Text('Start New Chat'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredChats() {
    final allChats = _getMockChats();
    
    if (_searchQuery.isEmpty) {
      return allChats;
    }
    
    return allChats.where((chat) {
      final name = chat['name'].toString().toLowerCase();
      final lastMessage = chat['lastMessage'].toString().toLowerCase();
      final query = _searchQuery.toLowerCase();
      
      return name.contains(query) || lastMessage.contains(query);
    }).toList();
  }

  List<Map<String, dynamic>> _getMockChats() {
    return [
      {
        'id': '1',
        'name': 'Ahmed Hassan',
        'lastMessage': 'Thanks for booking! See you tomorrow.',
        'lastMessageTime': DateTime.now().subtract(const Duration(minutes: 5)),
        'unreadCount': 2,
        'isOnline': true,
        'avatarUrl': null,
        'type': 'direct',
      },
      {
        'id': '2',
        'name': 'Fatima Al-Zahra',
        'lastMessage': 'Your appointment is confirmed for 3 PM',
        'lastMessageTime': DateTime.now().subtract(const Duration(hours: 1)),
        'unreadCount': 0,
        'isOnline': false,
        'avatarUrl': null,
        'type': 'direct',
      },
      {
        'id': '3',
        'name': 'Omar Khalil',
        'lastMessage': 'I might need to cut my hair tonight',
        'lastMessageTime': DateTime.now().subtract(const Duration(hours: 3)),
        'unreadCount': 1,
        'isOnline': true,
        'avatarUrl': null,
        'type': 'direct',
      },
      {
        'id': '4',
        'name': 'Layla Mohammed',
        'lastMessage': 'Perfect! Looking forward to it.',
        'lastMessageTime': DateTime.now().subtract(const Duration(days: 1)),
        'unreadCount': 0,
        'isOnline': false,
        'avatarUrl': null,
        'type': 'direct',
      },
    ];
  }

  void _openChat(String chatId) {
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: {'chatId': chatId},
    );
  }

  void _startNewChat() {
    Navigator.pushNamed(context, '/new-chat');
  }

  void _showChatOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.archive),
              title: const Text('Archived Chats'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to archived chats
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Chat Settings'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to chat settings
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showChatActions(Map<String, dynamic> chat) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.push_pin),
              title: const Text('Pin Chat'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Pin chat
              },
            ),
            ListTile(
              leading: const Icon(Icons.notifications_off),
              title: const Text('Mute Chat'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Mute chat
              },
            ),
            ListTile(
              leading: const Icon(Icons.archive),
              title: const Text('Archive Chat'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Archive chat
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: AppColors.error),
              title: const Text('Delete Chat', style: TextStyle(color: AppColors.error)),
              onTap: () {
                Navigator.pop(context);
                _confirmDeleteChat(chat);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDeleteChat(Map<String, dynamic> chat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Chat'),
        content: Text('Are you sure you want to delete this chat with ${chat['name']}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Delete chat
            },
            child: const Text('Delete', style: TextStyle(color: AppColors.error)),
          ),
        ],
      ),
    );
  }
}
