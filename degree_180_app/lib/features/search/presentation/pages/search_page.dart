import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/enums/service_type.dart';
import '../../../services/presentation/bloc/service_provider_bloc.dart';
import '../../../services/presentation/widgets/service_provider_card.dart';
import '../widgets/search_filters_widget.dart';
import '../widgets/search_suggestions_widget.dart';
import '../widgets/recent_searches_widget.dart';

class SearchPage extends StatefulWidget {
  final String? initialQuery;
  final ServiceType? serviceType;

  const SearchPage({
    super.key,
    this.initialQuery,
    this.serviceType,
  });

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  
  bool _isSearching = false;
  ServiceType? _selectedServiceType;
  double? _selectedRadius;
  double? _minRating;
  bool? _isVerified;
  bool? _isOnline;
  String? _sortBy;

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.initialQuery ?? '';
    _selectedServiceType = widget.serviceType;
    
    if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
      _performSearch();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _buildSearchBar(),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            onPressed: _showFilters,
            icon: Stack(
              children: [
                const Icon(Icons.tune),
                if (_hasActiveFilters())
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: AppColors.warning,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Active Filters
          if (_hasActiveFilters()) _buildActiveFilters(),
          
          // Search Results or Suggestions
          Expanded(
            child: _isSearching ? _buildSearchResults() : _buildSearchSuggestions(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: AppColors.textWhite.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        style: const TextStyle(color: AppColors.textWhite),
        decoration: InputDecoration(
          hintText: 'Search services, salons...',
          hintStyle: TextStyle(
            color: AppColors.textWhite.withValues(alpha: 0.7),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.textWhite.withValues(alpha: 0.7),
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: _clearSearch,
                  icon: Icon(
                    Icons.clear,
                    color: AppColors.textWhite.withValues(alpha: 0.7),
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        onChanged: _onSearchChanged,
        onSubmitted: _onSearchSubmitted,
      ),
    );
  }

  Widget _buildActiveFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: AppColors.backgroundGray,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Active Filters',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: _clearAllFilters,
                child: const Text('Clear All'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _buildFilterChips(),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildFilterChips() {
    final chips = <Widget>[];
    
    if (_selectedServiceType != null) {
      chips.add(_buildFilterChip(
        _selectedServiceType!.displayName,
        () => setState(() {
          _selectedServiceType = null;
          _performSearch();
        }),
      ));
    }
    
    if (_selectedRadius != null) {
      chips.add(_buildFilterChip(
        'Within ${_selectedRadius!.toInt()} km',
        () => setState(() {
          _selectedRadius = null;
          _performSearch();
        }),
      ));
    }
    
    if (_minRating != null) {
      chips.add(_buildFilterChip(
        '${_minRating!.toStringAsFixed(1)}+ Rating',
        () => setState(() {
          _minRating = null;
          _performSearch();
        }),
      ));
    }
    
    if (_isVerified == true) {
      chips.add(_buildFilterChip(
        'Verified Only',
        () => setState(() {
          _isVerified = null;
          _performSearch();
        }),
      ));
    }
    
    if (_isOnline == true) {
      chips.add(_buildFilterChip(
        'Online Only',
        () => setState(() {
          _isOnline = null;
          _performSearch();
        }),
      ));
    }
    
    return chips;
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label),
      onDeleted: onRemove,
      deleteIcon: const Icon(Icons.close, size: 16),
      backgroundColor: AppColors.primaryPurple.withValues(alpha: 0.1),
      labelStyle: TextStyle(
        color: AppColors.primaryPurple,
        fontSize: 12,
      ),
    );
  }

  Widget _buildSearchResults() {
    return BlocBuilder<ServiceProviderBloc, ServiceProviderState>(
      builder: (context, state) {
        if (state is ServiceProviderSearchLoading) {
          return const EnhancedLoadingWidget(
            message: 'Searching...',
          );
        }

        if (state is ServiceProviderError) {
          return _buildErrorState(state.message);
        }

        if (state is ServiceProviderSearchEmpty) {
          return _buildEmptySearchResults();
        }

        if (state is ServiceProviderSearchLoaded) {
          return _buildSearchResultsList(state.providers, state.query);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSearchResultsList(List<ServiceProviderModel> providers, String query) {
    return Column(
      children: [
        // Results Header
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          color: AppColors.backgroundGray,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${providers.length} results for "$query"',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  setState(() {
                    _sortBy = value;
                  });
                  _performSearch();
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'rating',
                    child: Text('Sort by Rating'),
                  ),
                  const PopupMenuItem(
                    value: 'distance',
                    child: Text('Sort by Distance'),
                  ),
                  const PopupMenuItem(
                    value: 'price',
                    child: Text('Sort by Price'),
                  ),
                  const PopupMenuItem(
                    value: 'popularity',
                    child: Text('Sort by Popularity'),
                  ),
                ],
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.sort,
                      size: 16,
                      color: AppColors.textLight,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Sort',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textLight,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // Results List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: providers.length,
            itemBuilder: (context, index) {
              final provider = providers[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: ServiceProviderCard(
                  provider: provider,
                  onTap: () => _navigateToProviderDetails(provider.id),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptySearchResults() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.textLight.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No Results Found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _clearAllFilters,
              child: const Text('Clear Filters'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Search Error',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _performSearch,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Searches
          const RecentSearchesWidget(),
          
          const SizedBox(height: 24),
          
          // Search Suggestions
          const SearchSuggestionsWidget(),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    return _selectedServiceType != null ||
           _selectedRadius != null ||
           _minRating != null ||
           _isVerified != null ||
           _isOnline != null;
  }

  void _onSearchChanged(String value) {
    // Implement real-time search suggestions if needed
  }

  void _onSearchSubmitted(String value) {
    if (value.trim().isNotEmpty) {
      setState(() {
        _isSearching = true;
      });
      _performSearch();
    }
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      context.read<ServiceProviderBloc>().add(
        SearchServiceProvidersEvent(
          query: query,
          serviceType: _selectedServiceType,
          radiusKm: _selectedRadius,
          limit: 50,
        ),
      );
    }
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _isSearching = false;
    });
    _searchFocusNode.unfocus();
  }

  void _clearAllFilters() {
    setState(() {
      _selectedServiceType = null;
      _selectedRadius = null;
      _minRating = null;
      _isVerified = null;
      _isOnline = null;
      _sortBy = null;
    });
    if (_isSearching) {
      _performSearch();
    }
  }

  void _showFilters() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => SearchFiltersWidget(
        selectedServiceType: _selectedServiceType,
        selectedRadius: _selectedRadius,
        minRating: _minRating,
        isVerified: _isVerified,
        isOnline: _isOnline,
        onApplyFilters: _applyFilters,
      ),
    );
  }

  void _applyFilters({
    ServiceType? serviceType,
    double? radius,
    double? minRating,
    bool? isVerified,
    bool? isOnline,
  }) {
    setState(() {
      _selectedServiceType = serviceType;
      _selectedRadius = radius;
      _minRating = minRating;
      _isVerified = isVerified;
      _isOnline = isOnline;
    });
    
    if (_isSearching) {
      _performSearch();
    }
  }

  void _navigateToProviderDetails(String providerId) {
    Navigator.pushNamed(
      context,
      '/provider-details',
      arguments: {'providerId': providerId},
    );
  }
}
