import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';

enum ConnectionType {
  none,
  mobile,
  wifi,
  ethernet,
  bluetooth,
  vpn,
  other,
}

enum ConnectionQuality {
  poor,
  fair,
  good,
  excellent,
}

class ConnectionStatus {
  final ConnectionType type;
  final bool isConnected;
  final ConnectionQuality quality;
  final int? speed; // in Mbps
  final int latency; // in milliseconds
  final DateTime timestamp;

  const ConnectionStatus({
    required this.type,
    required this.isConnected,
    required this.quality,
    this.speed,
    required this.latency,
    required this.timestamp,
  });

  ConnectionStatus copyWith({
    ConnectionType? type,
    bool? isConnected,
    ConnectionQuality? quality,
    int? speed,
    int? latency,
    DateTime? timestamp,
  }) {
    return ConnectionStatus(
      type: type ?? this.type,
      isConnected: isConnected ?? this.isConnected,
      quality: quality ?? this.quality,
      speed: speed ?? this.speed,
      latency: latency ?? this.latency,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  String toString() {
    return 'ConnectionStatus(type: $type, connected: $isConnected, quality: $quality, speed: ${speed}Mbps, latency: ${latency}ms)';
  }
}

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  final Dio _dio = Dio();
  
  ConnectionStatus _currentStatus = ConnectionStatus(
    type: ConnectionType.none,
    isConnected: false,
    quality: ConnectionQuality.poor,
    latency: 0,
    timestamp: DateTime.now(),
  );

  final StreamController<ConnectionStatus> _statusController = 
      StreamController<ConnectionStatus>.broadcast();

  Timer? _qualityCheckTimer;
  Timer? _reconnectTimer;
  
  // Configuration
  static const Duration _qualityCheckInterval = Duration(seconds: 30);
  static const Duration _reconnectCheckInterval = Duration(seconds: 5);
  static const List<String> _testUrls = [
    'https://www.google.com',
    'https://www.cloudflare.com',
    'https://httpbin.org/get',
  ];

  // Getters
  ConnectionStatus get currentStatus => _currentStatus;
  Stream<ConnectionStatus> get statusStream => _statusController.stream;
  bool get isConnected => _currentStatus.isConnected;
  ConnectionType get connectionType => _currentStatus.type;
  ConnectionQuality get connectionQuality => _currentStatus.quality;

  Future<void> initialize() async {
    try {
      // Configure Dio
      _dio.options = BaseOptions(
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        sendTimeout: const Duration(seconds: 10),
      );

      // Check initial connectivity
      await _checkConnectivity();

      // Listen to connectivity changes
      _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);

      // Start periodic quality checks
      _startQualityChecks();

      debugPrint('ConnectivityService initialized');
    } catch (e) {
      debugPrint('Error initializing ConnectivityService: $e');
    }
  }

  Future<void> _checkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(connectivityResults);
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      _updateStatus(ConnectionStatus(
        type: ConnectionType.none,
        isConnected: false,
        quality: ConnectionQuality.poor,
        latency: 0,
        timestamp: DateTime.now(),
      ));
    }
  }

  Future<void> _onConnectivityChanged(List<ConnectivityResult> results) async {
    await _updateConnectionStatus(results);
  }

  Future<void> _updateConnectionStatus(List<ConnectivityResult> results) async {
    if (results.isEmpty) {
      _updateStatus(ConnectionStatus(
        type: ConnectionType.none,
        isConnected: false,
        quality: ConnectionQuality.poor,
        latency: 0,
        timestamp: DateTime.now(),
      ));
      return;
    }

    // Get the primary connection type
    final primaryResult = results.first;
    final connectionType = _mapConnectivityResult(primaryResult);

    if (connectionType == ConnectionType.none) {
      _updateStatus(ConnectionStatus(
        type: ConnectionType.none,
        isConnected: false,
        quality: ConnectionQuality.poor,
        latency: 0,
        timestamp: DateTime.now(),
      ));
      _startReconnectChecks();
      return;
    }

    // Test actual internet connectivity
    final isReallyConnected = await _testInternetConnectivity();
    
    if (!isReallyConnected) {
      _updateStatus(ConnectionStatus(
        type: connectionType,
        isConnected: false,
        quality: ConnectionQuality.poor,
        latency: 0,
        timestamp: DateTime.now(),
      ));
      _startReconnectChecks();
      return;
    }

    // Test connection quality
    final quality = await _testConnectionQuality();
    final latency = await _measureLatency();
    final speed = await _measureSpeed();

    _updateStatus(ConnectionStatus(
      type: connectionType,
      isConnected: true,
      quality: quality,
      speed: speed,
      latency: latency,
      timestamp: DateTime.now(),
    ));

    _stopReconnectChecks();
  }

  ConnectionType _mapConnectivityResult(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return ConnectionType.wifi;
      case ConnectivityResult.mobile:
        return ConnectionType.mobile;
      case ConnectivityResult.ethernet:
        return ConnectionType.ethernet;
      case ConnectivityResult.bluetooth:
        return ConnectionType.bluetooth;
      case ConnectivityResult.vpn:
        return ConnectionType.vpn;
      case ConnectivityResult.other:
        return ConnectionType.other;
      case ConnectivityResult.none:
        return ConnectionType.none;
    }
  }

  Future<bool> _testInternetConnectivity() async {
    for (final url in _testUrls) {
      try {
        final response = await _dio.get(
          url,
          options: Options(
            receiveTimeout: const Duration(seconds: 5),
            sendTimeout: const Duration(seconds: 5),
          ),
        );
        
        if (response.statusCode == 200) {
          return true;
        }
      } catch (e) {
        // Continue to next URL
        continue;
      }
    }
    return false;
  }

  Future<ConnectionQuality> _testConnectionQuality() async {
    final latency = await _measureLatency();
    final speed = await _measureSpeed();

    // Determine quality based on latency and speed
    if (latency > 1000 || (speed != null && speed < 1)) {
      return ConnectionQuality.poor;
    } else if (latency > 500 || (speed != null && speed < 5)) {
      return ConnectionQuality.fair;
    } else if (latency > 200 || (speed != null && speed < 25)) {
      return ConnectionQuality.good;
    } else {
      return ConnectionQuality.excellent;
    }
  }

  Future<int> _measureLatency() async {
    try {
      final stopwatch = Stopwatch()..start();
      
      await _dio.head(
        'https://www.google.com',
        options: Options(
          receiveTimeout: const Duration(seconds: 5),
        ),
      );
      
      stopwatch.stop();
      return stopwatch.elapsedMilliseconds;
    } catch (e) {
      return 9999; // High latency indicates poor connection
    }
  }

  Future<int?> _measureSpeed() async {
    try {
      // Download a small file to measure speed
      const testFileUrl = 'https://httpbin.org/bytes/1048576'; // 1MB
      final stopwatch = Stopwatch()..start();
      
      final response = await _dio.get(
        testFileUrl,
        options: Options(
          receiveTimeout: const Duration(seconds: 30),
          responseType: ResponseType.bytes,
        ),
      );
      
      stopwatch.stop();
      
      if (response.statusCode == 200 && response.data != null) {
        final bytes = (response.data as List<int>).length;
        final seconds = stopwatch.elapsedMilliseconds / 1000;
        final mbps = (bytes * 8) / (seconds * 1000000); // Convert to Mbps
        return mbps.round();
      }
    } catch (e) {
      debugPrint('Error measuring speed: $e');
    }
    return null;
  }

  void _updateStatus(ConnectionStatus status) {
    _currentStatus = status;
    _statusController.add(status);
    debugPrint('Connection status updated: $status');
  }

  void _startQualityChecks() {
    _qualityCheckTimer?.cancel();
    _qualityCheckTimer = Timer.periodic(_qualityCheckInterval, (timer) {
      if (_currentStatus.isConnected) {
        _checkConnectivity();
      }
    });
  }

  void _startReconnectChecks() {
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer.periodic(_reconnectCheckInterval, (timer) {
      _checkConnectivity();
    });
  }

  void _stopReconnectChecks() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  // Public methods for manual checks
  Future<bool> hasInternetConnection() async {
    return await _testInternetConnectivity();
  }

  Future<ConnectionStatus> checkConnectionStatus() async {
    await _checkConnectivity();
    return _currentStatus;
  }

  Future<bool> isHostReachable(String host) async {
    try {
      final result = await InternetAddress.lookup(host);
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  Future<bool> canReachUrl(String url) async {
    try {
      final response = await _dio.head(
        url,
        options: Options(
          receiveTimeout: const Duration(seconds: 10),
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // Network-aware operations
  Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 2),
    bool requireConnection = true,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        if (requireConnection && !_currentStatus.isConnected) {
          await _waitForConnection();
        }
        
        return await operation();
      } catch (e) {
        attempts++;
        
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        // Wait before retry
        await Future.delayed(delay * attempts);
        
        // Check connection before retry
        if (requireConnection) {
          await _checkConnectivity();
        }
      }
    }
    
    throw Exception('Operation failed after $maxRetries attempts');
  }

  Future<void> _waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    if (_currentStatus.isConnected) return;
    
    final completer = Completer<void>();
    late StreamSubscription subscription;
    
    subscription = statusStream.listen((status) {
      if (status.isConnected && !completer.isCompleted) {
        completer.complete();
        subscription.cancel();
      }
    });
    
    // Set timeout
    Timer(timeout, () {
      if (!completer.isCompleted) {
        completer.completeError(TimeoutException('Connection timeout', timeout));
        subscription.cancel();
      }
    });
    
    return completer.future;
  }

  // Bandwidth management
  bool shouldUseHighQualityContent() {
    return _currentStatus.quality == ConnectionQuality.excellent ||
           (_currentStatus.quality == ConnectionQuality.good && 
            _currentStatus.type == ConnectionType.wifi);
  }

  bool shouldPreloadContent() {
    return _currentStatus.type == ConnectionType.wifi &&
           (_currentStatus.quality == ConnectionQuality.good ||
            _currentStatus.quality == ConnectionQuality.excellent);
  }

  bool shouldCompressImages() {
    return _currentStatus.type == ConnectionType.mobile ||
           _currentStatus.quality == ConnectionQuality.poor ||
           _currentStatus.quality == ConnectionQuality.fair;
  }

  void dispose() {
    _qualityCheckTimer?.cancel();
    _reconnectTimer?.cancel();
    _statusController.close();
    _dio.close();
  }
}
