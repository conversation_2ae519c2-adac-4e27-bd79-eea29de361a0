import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/theme_manager.dart';
import '../widgets/user_type_card.dart';

class UserTypeSelectionPage extends StatefulWidget {
  const UserTypeSelectionPage({super.key});

  @override
  State<UserTypeSelectionPage> createState() => _UserTypeSelectionPageState();
}

class _UserTypeSelectionPageState extends State<UserTypeSelectionPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeManager.currentTheme,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: ThemeManager.currentColors.gradient,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.largePadding),
              child: Column(
              children: [
                // Back Button
                Align(
                  alignment: Alignment.topLeft,
                  child: IconButton(
                    onPressed: () => context.pop(),
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: AppColors.textWhite,
                    ),
                  ),
                ),
                
                // Logo
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildLogo(),
                ),
                
                const SizedBox(height: AppConstants.extraLargePadding),
                
                // User Type Cards
                Expanded(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: [
                          // Service Provider Types
                          UserTypeCard(
                            title: AppStrings.salon,
                            titleAr: AppStrings.salonAr,
                            onTap: () => _navigateToAuth('worker', 'salon'),
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          UserTypeCard(
                            title: AppStrings.barbershop,
                            titleAr: AppStrings.barbershopAr,
                            onTap: () => _navigateToAuth('worker', 'barbershop'),
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          UserTypeCard(
                            title: AppStrings.spa,
                            titleAr: AppStrings.spaAr,
                            onTap: () => _navigateToAuth('worker', 'spa'),
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          UserTypeCard(
                            title: AppStrings.worker,
                            titleAr: AppStrings.workerAr,
                            onTap: () => _navigateToAuth('worker', null),
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          UserTypeCard(
                            title: AppStrings.freelancer,
                            titleAr: AppStrings.freelancerAr,
                            onTap: () => _navigateToAuth('freelancer', null),
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          UserTypeCard(
                            title: AppStrings.customer,
                            titleAr: AppStrings.customerAr,
                            onTap: () => _navigateToAuth('customer', null),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Create Account Button
                SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () => context.go('/register'),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: AppColors.textWhite),
                          padding: const EdgeInsets.symmetric(
                            vertical: AppConstants.defaultPadding,
                          ),
                        ),
                        child: Text(
                          AppStrings.createAccount,
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: AppConstants.smallPadding),
                
                // Back Text
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: TextButton(
                    onPressed: () => context.pop(),
                    child: Text(
                      AppStrings.back,
                      style: Theme.of(context).textTheme.labelMedium,
                    ),
                  ),
                ),
              ],
            ),
          ), // Column
        ), // Padding
      ), // SafeArea
    ), // Container
  ); // Scaffold
  }

  Widget _buildLogo() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '180',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w300,
                color: AppColors.textWhite,
                letterSpacing: 1,
              ),
            ),
            Container(
              width: 30,
              height: 1,
              color: AppColors.textWhite,
            ),
            const SizedBox(height: 2),
            Text(
              '°',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w300,
                color: AppColors.textWhite,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAuth(String userType, String? serviceType) {
    final queryParams = <String, String>{
      'userType': userType,
    };
    
    if (serviceType != null) {
      queryParams['serviceType'] = serviceType;
    }
    
    context.pushNamed(
      'login',
      queryParameters: queryParams,
    );
  }
}
