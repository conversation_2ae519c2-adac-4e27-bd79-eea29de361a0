import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/payment_model.dart';
import '../models/booking_model.dart';
import 'firebase_service.dart';

class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  final Dio _dio = Dio();
  final FirebaseService _firebaseService = FirebaseService();

  // Payment gateway configurations
  static const String _knetGatewayUrl = 'https://api.knet.com.kw/v1';
  static const String _visaGatewayUrl = 'https://api.visa.com/v1';
  static const String _mastercardGatewayUrl = 'https://api.mastercard.com/v1';

  // Initialize payment service
  Future<void> initialize() async {
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  // Process payment
  Future<PaymentResult> processPayment({
    required BookingModel booking,
    required PaymentMethod paymentMethod,
    required Map<String, dynamic> paymentData,
    String? promoCode,
  }) async {
    try {
      // Calculate final amount with discounts
      final finalAmount = await _calculateFinalAmount(booking, promoCode);
      
      // Create payment record
      final payment = PaymentModel(
        id: '',
        bookingId: booking.id,
        userId: booking.customerId,
        providerId: booking.providerId,
        amount: finalAmount.subtotal,
        currency: 'KWD',
        paymentMethod: paymentMethod,
        status: PaymentStatus.processing,
        createdAt: DateTime.now(),
        serviceFee: finalAmount.serviceFee,
        tax: finalAmount.tax,
        discount: finalAmount.discount,
        promoCode: promoCode,
        metadata: paymentData,
      );

      // Save payment to database
      final paymentId = await _firebaseService.createPayment(payment);
      final updatedPayment = payment.copyWith(id: paymentId);

      // Process payment based on method
      PaymentResult result;
      switch (paymentMethod) {
        case PaymentMethod.knet:
          result = await _processKnetPayment(updatedPayment, paymentData);
          break;
        case PaymentMethod.creditCard:
        case PaymentMethod.debitCard:
          result = await _processCreditCardPayment(updatedPayment, paymentData);
          break;
        case PaymentMethod.applePay:
          result = await _processApplePayPayment(updatedPayment, paymentData);
          break;
        case PaymentMethod.googlePay:
          result = await _processGooglePayPayment(updatedPayment, paymentData);
          break;
        case PaymentMethod.cash:
          result = await _processCashPayment(updatedPayment);
          break;
        case PaymentMethod.wallet:
          result = await _processWalletPayment(updatedPayment, paymentData);
          break;
      }

      // Update payment status
      await _updatePaymentStatus(paymentId, result);

      return result;
    } catch (e) {
      print('Error processing payment: $e');
      return PaymentResult(
        success: false,
        error: e.toString(),
        paymentId: '',
      );
    }
  }

  // Calculate final amount with fees and discounts
  Future<PaymentAmount> _calculateFinalAmount(BookingModel booking, String? promoCode) async {
    double subtotal = booking.totalAmount;
    double serviceFee = subtotal * 0.025; // 2.5% service fee
    double tax = subtotal * 0.05; // 5% tax
    double discount = 0.0;

    // Apply promo code discount
    if (promoCode != null) {
      discount = await _calculatePromoDiscount(subtotal, promoCode);
    }

    return PaymentAmount(
      subtotal: subtotal,
      serviceFee: serviceFee,
      tax: tax,
      discount: discount,
      total: subtotal + serviceFee + tax - discount,
    );
  }

  // Calculate promo code discount
  Future<double> _calculatePromoDiscount(double amount, String promoCode) async {
    try {
      // Get promo from database
      final promoSnapshot = await _firebaseService.promotions
          .where('promoCode', isEqualTo: promoCode)
          .where('status', isEqualTo: 'active')
          .limit(1)
          .get();

      if (promoSnapshot.docs.isEmpty) return 0.0;

      final promo = promoSnapshot.docs.first.data() as Map<String, dynamic>;
      final discountPercentage = promo['discountPercentage'] as double?;
      final discountAmount = promo['discountAmount'] as double?;
      final minimumAmount = promo['minimumAmount'] as double?;
      final maximumDiscount = promo['maximumDiscount'] as double?;

      // Check minimum amount
      if (minimumAmount != null && amount < minimumAmount) return 0.0;

      double discount = 0.0;
      if (discountPercentage != null) {
        discount = amount * (discountPercentage / 100);
      } else if (discountAmount != null) {
        discount = discountAmount;
      }

      // Apply maximum discount limit
      if (maximumDiscount != null && discount > maximumDiscount) {
        discount = maximumDiscount;
      }

      return discount;
    } catch (e) {
      print('Error calculating promo discount: $e');
      return 0.0;
    }
  }

  // Process K-Net payment
  Future<PaymentResult> _processKnetPayment(
    PaymentModel payment,
    Map<String, dynamic> paymentData,
  ) async {
    try {
      final response = await _dio.post(
        '$_knetGatewayUrl/payments',
        data: {
          'amount': payment.totalAmount,
          'currency': payment.currency,
          'reference': payment.id,
          'card_number': paymentData['cardNumber'],
          'expiry_month': paymentData['expiryMonth'],
          'expiry_year': paymentData['expiryYear'],
          'cvv': paymentData['cvv'],
          'cardholder_name': paymentData['cardholderName'],
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return PaymentResult(
          success: data['status'] == 'success',
          paymentId: payment.id,
          transactionId: data['transaction_id'],
          gatewayResponse: jsonEncode(data),
          error: data['status'] != 'success' ? data['message'] : null,
        );
      } else {
        return PaymentResult(
          success: false,
          paymentId: payment.id,
          error: 'Payment gateway error: ${response.statusCode}',
        );
      }
    } catch (e) {
      return PaymentResult(
        success: false,
        paymentId: payment.id,
        error: 'K-Net payment failed: $e',
      );
    }
  }

  // Process credit card payment
  Future<PaymentResult> _processCreditCardPayment(
    PaymentModel payment,
    Map<String, dynamic> paymentData,
  ) async {
    try {
      // Determine gateway based on card type
      final cardNumber = paymentData['cardNumber'] as String;
      final gatewayUrl = _getCardGatewayUrl(cardNumber);

      final response = await _dio.post(
        '$gatewayUrl/payments',
        data: {
          'amount': payment.totalAmount,
          'currency': payment.currency,
          'reference': payment.id,
          'card': {
            'number': paymentData['cardNumber'],
            'expiry_month': paymentData['expiryMonth'],
            'expiry_year': paymentData['expiryYear'],
            'cvv': paymentData['cvv'],
            'name': paymentData['cardholderName'],
          },
          'billing_address': paymentData['billingAddress'],
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return PaymentResult(
          success: data['status'] == 'approved',
          paymentId: payment.id,
          transactionId: data['transaction_id'],
          gatewayResponse: jsonEncode(data),
          error: data['status'] != 'approved' ? data['decline_reason'] : null,
        );
      } else {
        return PaymentResult(
          success: false,
          paymentId: payment.id,
          error: 'Payment gateway error: ${response.statusCode}',
        );
      }
    } catch (e) {
      return PaymentResult(
        success: false,
        paymentId: payment.id,
        error: 'Credit card payment failed: $e',
      );
    }
  }

  // Process Apple Pay payment
  Future<PaymentResult> _processApplePayPayment(
    PaymentModel payment,
    Map<String, dynamic> paymentData,
  ) async {
    try {
      // Process Apple Pay token
      final paymentToken = paymentData['paymentToken'];
      
      final response = await _dio.post(
        '$_visaGatewayUrl/applepay/payments',
        data: {
          'amount': payment.totalAmount,
          'currency': payment.currency,
          'reference': payment.id,
          'payment_token': paymentToken,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return PaymentResult(
          success: data['status'] == 'approved',
          paymentId: payment.id,
          transactionId: data['transaction_id'],
          gatewayResponse: jsonEncode(data),
        );
      } else {
        return PaymentResult(
          success: false,
          paymentId: payment.id,
          error: 'Apple Pay payment failed',
        );
      }
    } catch (e) {
      return PaymentResult(
        success: false,
        paymentId: payment.id,
        error: 'Apple Pay payment failed: $e',
      );
    }
  }

  // Process Google Pay payment
  Future<PaymentResult> _processGooglePayPayment(
    PaymentModel payment,
    Map<String, dynamic> paymentData,
  ) async {
    try {
      final paymentToken = paymentData['paymentToken'];
      
      final response = await _dio.post(
        '$_visaGatewayUrl/googlepay/payments',
        data: {
          'amount': payment.totalAmount,
          'currency': payment.currency,
          'reference': payment.id,
          'payment_token': paymentToken,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return PaymentResult(
          success: data['status'] == 'approved',
          paymentId: payment.id,
          transactionId: data['transaction_id'],
          gatewayResponse: jsonEncode(data),
        );
      } else {
        return PaymentResult(
          success: false,
          paymentId: payment.id,
          error: 'Google Pay payment failed',
        );
      }
    } catch (e) {
      return PaymentResult(
        success: false,
        paymentId: payment.id,
        error: 'Google Pay payment failed: $e',
      );
    }
  }

  // Process cash payment
  Future<PaymentResult> _processCashPayment(PaymentModel payment) async {
    // Cash payments are marked as pending until confirmed by provider
    return PaymentResult(
      success: true,
      paymentId: payment.id,
      transactionId: 'CASH_${DateTime.now().millisecondsSinceEpoch}',
      isPending: true,
    );
  }

  // Process wallet payment
  Future<PaymentResult> _processWalletPayment(
    PaymentModel payment,
    Map<String, dynamic> paymentData,
  ) async {
    try {
      // Check wallet balance
      final walletBalance = await _getWalletBalance(payment.userId);
      if (walletBalance < payment.totalAmount) {
        return PaymentResult(
          success: false,
          paymentId: payment.id,
          error: 'Insufficient wallet balance',
        );
      }

      // Deduct from wallet
      await _deductFromWallet(payment.userId, payment.totalAmount);

      return PaymentResult(
        success: true,
        paymentId: payment.id,
        transactionId: 'WALLET_${DateTime.now().millisecondsSinceEpoch}',
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        paymentId: payment.id,
        error: 'Wallet payment failed: $e',
      );
    }
  }

  // Get card gateway URL based on card number
  String _getCardGatewayUrl(String cardNumber) {
    final firstDigit = cardNumber[0];
    switch (firstDigit) {
      case '4':
        return _visaGatewayUrl; // Visa
      case '5':
        return _mastercardGatewayUrl; // Mastercard
      default:
        return _visaGatewayUrl; // Default to Visa
    }
  }

  // Update payment status
  Future<void> _updatePaymentStatus(String paymentId, PaymentResult result) async {
    final status = result.success 
        ? (result.isPending ? PaymentStatus.pending : PaymentStatus.completed)
        : PaymentStatus.failed;

    await _firebaseService.payments.doc(paymentId).update({
      'status': status.name,
      'transactionId': result.transactionId,
      'gatewayResponse': result.gatewayResponse,
      'failureReason': result.error,
      'completedAt': result.success ? Timestamp.fromDate(DateTime.now()) : null,
    });
  }

  // Get wallet balance
  Future<double> _getWalletBalance(String userId) async {
    try {
      final userDoc = await _firebaseService.users.doc(userId).get();
      final userData = userDoc.data() as Map<String, dynamic>?;
      return (userData?['walletBalance'] ?? 0.0).toDouble();
    } catch (e) {
      return 0.0;
    }
  }

  // Deduct from wallet
  Future<void> _deductFromWallet(String userId, double amount) async {
    await _firebaseService.users.doc(userId).update({
      'walletBalance': FieldValue.increment(-amount),
    });
  }

  // Refund payment
  Future<PaymentResult> refundPayment(String paymentId, double? amount) async {
    try {
      final paymentDoc = await _firebaseService.payments.doc(paymentId).get();
      if (!paymentDoc.exists) {
        return PaymentResult(
          success: false,
          paymentId: paymentId,
          error: 'Payment not found',
        );
      }

      final payment = PaymentModel.fromFirestore(paymentDoc);
      final refundAmount = amount ?? payment.amount;

      // Process refund based on original payment method
      PaymentResult result;
      switch (payment.paymentMethod) {
        case PaymentMethod.wallet:
          // Add back to wallet
          await _firebaseService.users.doc(payment.userId).update({
            'walletBalance': FieldValue.increment(refundAmount),
          });
          result = PaymentResult(
            success: true,
            paymentId: paymentId,
            transactionId: 'REFUND_${DateTime.now().millisecondsSinceEpoch}',
          );
          break;
        default:
          // Process gateway refund
          result = await _processGatewayRefund(payment, refundAmount);
          break;
      }

      // Update payment status
      final newStatus = refundAmount >= payment.amount 
          ? PaymentStatus.refunded 
          : PaymentStatus.partiallyRefunded;

      await _firebaseService.payments.doc(paymentId).update({
        'status': newStatus.name,
        'refundAmount': refundAmount,
        'refundedAt': Timestamp.fromDate(DateTime.now()),
      });

      return result;
    } catch (e) {
      return PaymentResult(
        success: false,
        paymentId: paymentId,
        error: 'Refund failed: $e',
      );
    }
  }

  // Process gateway refund
  Future<PaymentResult> _processGatewayRefund(PaymentModel payment, double amount) async {
    // Implementation would depend on the specific gateway
    // This is a placeholder
    return PaymentResult(
      success: true,
      paymentId: payment.id,
      transactionId: 'REFUND_${DateTime.now().millisecondsSinceEpoch}',
    );
  }
}

// Payment result class
class PaymentResult {
  final bool success;
  final String paymentId;
  final String? transactionId;
  final String? gatewayResponse;
  final String? error;
  final bool isPending;

  PaymentResult({
    required this.success,
    required this.paymentId,
    this.transactionId,
    this.gatewayResponse,
    this.error,
    this.isPending = false,
  });
}

// Payment amount breakdown
class PaymentAmount {
  final double subtotal;
  final double serviceFee;
  final double tax;
  final double discount;
  final double total;

  PaymentAmount({
    required this.subtotal,
    required this.serviceFee,
    required this.tax,
    required this.discount,
    required this.total,
  });
}
