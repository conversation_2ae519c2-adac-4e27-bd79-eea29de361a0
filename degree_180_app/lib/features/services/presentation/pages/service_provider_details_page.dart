import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/models/service_provider_model.dart';
import '../bloc/service_provider_bloc.dart';
import '../widgets/service_list_widget.dart';
import '../widgets/provider_gallery_widget.dart';
import '../widgets/provider_reviews_widget.dart';
import '../widgets/provider_info_widget.dart';

class ServiceProviderDetailsPage extends StatefulWidget {
  final String providerId;

  const ServiceProviderDetailsPage({
    super.key,
    required this.providerId,
  });

  @override
  State<ServiceProviderDetailsPage> createState() => _ServiceProviderDetailsPageState();
}

class _ServiceProviderDetailsPageState extends State<ServiceProviderDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ServiceProviderModel? _provider;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadProviderDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadProviderDetails() {
    context.read<ServiceProviderBloc>().add(
      LoadServiceProviderDetailsEvent(providerId: widget.providerId),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<ServiceProviderBloc, ServiceProviderState>(
        builder: (context, state) {
          if (state is ServiceProviderDetailsLoading) {
            return const EnhancedLoadingWidget(
              message: 'Loading provider details...',
            );
          }

          if (state is ServiceProviderError) {
            return _buildErrorState(state.message);
          }

          if (state is ServiceProviderDetailsLoaded) {
            _provider = state.provider;
            return _buildProviderDetails(state.provider);
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildProviderDetails(ServiceProviderModel provider) {
    return CustomScrollView(
      slivers: [
        // App Bar with Provider Image
        SliverAppBar(
          expandedHeight: 300,
          pinned: true,
          backgroundColor: AppColors.primaryPurple,
          flexibleSpace: FlexibleSpaceBar(
            background: Stack(
              fit: StackFit.expand,
              children: [
                CachedNetworkImage(
                  imageUrl: provider.profileImageUrl ?? '',
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: AppColors.borderGray,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: AppColors.borderGray,
                    child: Icon(
                      Icons.store,
                      size: 64,
                      color: AppColors.textLight,
                    ),
                  ),
                ),
                // Gradient overlay
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
                // Provider info overlay
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              provider.name,
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: AppColors.textWhite,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (provider.isVerified)
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: AppColors.primaryPurple,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.verified,
                                color: AppColors.textWhite,
                                size: 16,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: AppColors.starYellow,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            provider.rating.toStringAsFixed(1),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.textWhite,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '(${provider.reviewCount} reviews)',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.textWhite.withValues(alpha: 0.8),
                            ),
                          ),
                          const Spacer(),
                          if (provider.isOnline)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.success,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: 6,
                                    height: 6,
                                    decoration: const BoxDecoration(
                                      color: AppColors.textWhite,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Online',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textWhite,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            IconButton(
              onPressed: () => _shareProvider(provider),
              icon: const Icon(Icons.share),
            ),
            IconButton(
              onPressed: () => _toggleFavorite(provider),
              icon: const Icon(Icons.favorite_border),
            ),
          ],
        ),

        // Tab Bar
        SliverPersistentHeader(
          pinned: true,
          delegate: _SliverTabBarDelegate(
            TabBar(
              controller: _tabController,
              labelColor: AppColors.primaryPurple,
              unselectedLabelColor: AppColors.textLight,
              indicatorColor: AppColors.primaryPurple,
              tabs: const [
                Tab(text: 'Info'),
                Tab(text: 'Services'),
                Tab(text: 'Gallery'),
                Tab(text: 'Reviews'),
              ],
            ),
          ),
        ),

        // Tab Content
        SliverFillRemaining(
          child: TabBarView(
            controller: _tabController,
            children: [
              ProviderInfoWidget(provider: provider),
              ServiceListWidget(provider: provider),
              ProviderGalleryWidget(provider: provider),
              ProviderReviewsWidget(provider: provider),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String message) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Provider Details'),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.error.withValues(alpha: 0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'Failed to load provider details',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textLight,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _loadProviderDetails,
                child: const Text('Try Again'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _shareProvider(ServiceProviderModel provider) {
    // Implement share functionality
  }

  void _toggleFavorite(ServiceProviderModel provider) {
    // Implement favorite functionality
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _SliverTabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: AppColors.backgroundWhite,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
