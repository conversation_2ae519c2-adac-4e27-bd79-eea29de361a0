import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';

/// نظام تحديد معدل المحاولات للحماية من هجمات Brute Force
class RateLimiter {
  static const String _keyPrefix = 'rate_limit_';
  static const String _globalKeyPrefix = 'global_rate_limit_';
  
  // إعدادات تحديد المعدل
  static const int maxLoginAttempts = 5;
  static const int maxGlobalAttempts = 50; // لكل IP/جهاز
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const Duration globalLockoutDuration = Duration(hours: 1);
  static const Duration cleanupInterval = Duration(hours: 24);
  
  static RateLimiter? _instance;
  static RateLimiter get instance => _instance ??= RateLimiter._();
  
  RateLimiter._() {
    _startCleanupTimer();
  }

  Timer? _cleanupTimer;

  /// بدء مؤقت التنظيف التلقائي
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(cleanupInterval, (_) {
      _cleanupExpiredEntries();
    });
  }

  /// تنظيف الإدخالات المنتهية الصلاحية
  Future<void> _cleanupExpiredEntries() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => 
          key.startsWith(_keyPrefix) || key.startsWith(_globalKeyPrefix));
      
      final now = DateTime.now().millisecondsSinceEpoch;
      
      for (String key in keys) {
        final data = prefs.getString(key);
        if (data != null) {
          try {
            final decoded = jsonDecode(data) as Map<String, dynamic>;
            final lockoutUntil = decoded['lockoutUntil'] as int?;
            
            if (lockoutUntil != null && lockoutUntil < now) {
              await prefs.remove(key);
            }
          } catch (e) {
            // إزالة البيانات التالفة
            await prefs.remove(key);
          }
        }
      }
    } catch (e) {
      print('خطأ في تنظيف Rate Limiter: $e');
    }
  }

  /// توليد مفتاح فريد للمستخدم
  String _generateUserKey(String identifier) {
    final bytes = utf8.encode(identifier.toLowerCase().trim());
    final digest = sha256.convert(bytes);
    return '$_keyPrefix${digest.toString()}';
  }

  /// توليد مفتاح عام للجهاز
  String _generateGlobalKey() {
    return '${_globalKeyPrefix}device';
  }

  /// التحقق من إمكانية المحاولة
  Future<RateLimitResult> canAttempt(String identifier) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userKey = _generateUserKey(identifier);
      final globalKey = _generateGlobalKey();
      final now = DateTime.now().millisecondsSinceEpoch;

      // فحص التحديد الخاص بالمستخدم
      final userResult = await _checkUserLimit(prefs, userKey, now);
      if (!userResult.canAttempt) {
        return userResult;
      }

      // فحص التحديد العام
      final globalResult = await _checkGlobalLimit(prefs, globalKey, now);
      if (!globalResult.canAttempt) {
        return globalResult;
      }

      return RateLimitResult(
        canAttempt: true,
        remainingAttempts: maxLoginAttempts - userResult.attemptCount,
        lockoutUntil: null,
        reason: null,
      );
    } catch (e) {
      print('خطأ في فحص Rate Limit: $e');
      // في حالة الخطأ، السماح بالمحاولة
      return RateLimitResult(
        canAttempt: true,
        remainingAttempts: maxLoginAttempts,
        lockoutUntil: null,
        reason: null,
      );
    }
  }

  /// فحص التحديد الخاص بالمستخدم
  Future<RateLimitResult> _checkUserLimit(
    SharedPreferences prefs, 
    String userKey, 
    int now
  ) async {
    final userData = prefs.getString(userKey);
    
    if (userData == null) {
      return RateLimitResult(
        canAttempt: true,
        remainingAttempts: maxLoginAttempts,
        lockoutUntil: null,
        reason: null,
        attemptCount: 0,
      );
    }

    try {
      final decoded = jsonDecode(userData) as Map<String, dynamic>;
      final attempts = decoded['attempts'] as int? ?? 0;
      final lockoutUntil = decoded['lockoutUntil'] as int?;
      final firstAttempt = decoded['firstAttempt'] as int? ?? now;

      // فحص انتهاء فترة القفل
      if (lockoutUntil != null && lockoutUntil > now) {
        return RateLimitResult(
          canAttempt: false,
          remainingAttempts: 0,
          lockoutUntil: DateTime.fromMillisecondsSinceEpoch(lockoutUntil),
          reason: 'تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول متعددة فاشلة',
          attemptCount: attempts,
        );
      }

      // إعادة تعيين العداد إذا انتهت فترة القفل
      if (lockoutUntil != null && lockoutUntil <= now) {
        await prefs.remove(userKey);
        return RateLimitResult(
          canAttempt: true,
          remainingAttempts: maxLoginAttempts,
          lockoutUntil: null,
          reason: null,
          attemptCount: 0,
        );
      }

      // فحص عدد المحاولات
      if (attempts >= maxLoginAttempts) {
        final newLockoutUntil = now + lockoutDuration.inMilliseconds;
        await _updateUserData(prefs, userKey, attempts, firstAttempt, newLockoutUntil);
        
        return RateLimitResult(
          canAttempt: false,
          remainingAttempts: 0,
          lockoutUntil: DateTime.fromMillisecondsSinceEpoch(newLockoutUntil),
          reason: 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول',
          attemptCount: attempts,
        );
      }

      return RateLimitResult(
        canAttempt: true,
        remainingAttempts: maxLoginAttempts - attempts,
        lockoutUntil: null,
        reason: null,
        attemptCount: attempts,
      );
    } catch (e) {
      // في حالة خطأ في البيانات، إزالتها والسماح بالمحاولة
      await prefs.remove(userKey);
      return RateLimitResult(
        canAttempt: true,
        remainingAttempts: maxLoginAttempts,
        lockoutUntil: null,
        reason: null,
        attemptCount: 0,
      );
    }
  }

  /// فحص التحديد العام للجهاز
  Future<RateLimitResult> _checkGlobalLimit(
    SharedPreferences prefs, 
    String globalKey, 
    int now
  ) async {
    final globalData = prefs.getString(globalKey);
    
    if (globalData == null) {
      return RateLimitResult(
        canAttempt: true,
        remainingAttempts: maxGlobalAttempts,
        lockoutUntil: null,
        reason: null,
        attemptCount: 0,
      );
    }

    try {
      final decoded = jsonDecode(globalData) as Map<String, dynamic>;
      final attempts = decoded['attempts'] as int? ?? 0;
      final lockoutUntil = decoded['lockoutUntil'] as int?;

      // فحص انتهاء فترة القفل العام
      if (lockoutUntil != null && lockoutUntil > now) {
        return RateLimitResult(
          canAttempt: false,
          remainingAttempts: 0,
          lockoutUntil: DateTime.fromMillisecondsSinceEpoch(lockoutUntil),
          reason: 'تم قفل الجهاز مؤقتاً بسبب نشاط مشبوه',
          attemptCount: attempts,
        );
      }

      // إعادة تعيين العداد العام إذا انتهت فترة القفل
      if (lockoutUntil != null && lockoutUntil <= now) {
        await prefs.remove(globalKey);
        return RateLimitResult(
          canAttempt: true,
          remainingAttempts: maxGlobalAttempts,
          lockoutUntil: null,
          reason: null,
          attemptCount: 0,
        );
      }

      if (attempts >= maxGlobalAttempts) {
        final newLockoutUntil = now + globalLockoutDuration.inMilliseconds;
        await _updateGlobalData(prefs, globalKey, attempts, newLockoutUntil);
        
        return RateLimitResult(
          canAttempt: false,
          remainingAttempts: 0,
          lockoutUntil: DateTime.fromMillisecondsSinceEpoch(newLockoutUntil),
          reason: 'تم تجاوز الحد الأقصى للمحاولات على هذا الجهاز',
          attemptCount: attempts,
        );
      }

      return RateLimitResult(
        canAttempt: true,
        remainingAttempts: maxGlobalAttempts - attempts,
        lockoutUntil: null,
        reason: null,
        attemptCount: attempts,
      );
    } catch (e) {
      await prefs.remove(globalKey);
      return RateLimitResult(
        canAttempt: true,
        remainingAttempts: maxGlobalAttempts,
        lockoutUntil: null,
        reason: null,
        attemptCount: 0,
      );
    }
  }

  /// تسجيل محاولة فاشلة
  Future<void> recordFailedAttempt(String identifier) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userKey = _generateUserKey(identifier);
      final globalKey = _generateGlobalKey();
      final now = DateTime.now().millisecondsSinceEpoch;

      // تحديث عداد المستخدم
      await _incrementUserAttempts(prefs, userKey, now);
      
      // تحديث العداد العام
      await _incrementGlobalAttempts(prefs, globalKey, now);
    } catch (e) {
      print('خطأ في تسجيل المحاولة الفاشلة: $e');
    }
  }

  /// زيادة عداد محاولات المستخدم
  Future<void> _incrementUserAttempts(
    SharedPreferences prefs, 
    String userKey, 
    int now
  ) async {
    final userData = prefs.getString(userKey);
    int attempts = 1;
    int firstAttempt = now;

    if (userData != null) {
      try {
        final decoded = jsonDecode(userData) as Map<String, dynamic>;
        attempts = (decoded['attempts'] as int? ?? 0) + 1;
        firstAttempt = decoded['firstAttempt'] as int? ?? now;
      } catch (e) {
        // في حالة خطأ في البيانات، البدء من جديد
      }
    }

    await _updateUserData(prefs, userKey, attempts, firstAttempt, null);
  }

  /// زيادة العداد العام
  Future<void> _incrementGlobalAttempts(
    SharedPreferences prefs, 
    String globalKey, 
    int now
  ) async {
    final globalData = prefs.getString(globalKey);
    int attempts = 1;

    if (globalData != null) {
      try {
        final decoded = jsonDecode(globalData) as Map<String, dynamic>;
        attempts = (decoded['attempts'] as int? ?? 0) + 1;
      } catch (e) {
        // في حالة خطأ في البيانات، البدء من جديد
      }
    }

    await _updateGlobalData(prefs, globalKey, attempts, null);
  }

  /// تحديث بيانات المستخدم
  Future<void> _updateUserData(
    SharedPreferences prefs,
    String userKey,
    int attempts,
    int firstAttempt,
    int? lockoutUntil,
  ) async {
    final data = {
      'attempts': attempts,
      'firstAttempt': firstAttempt,
      'lastAttempt': DateTime.now().millisecondsSinceEpoch,
      if (lockoutUntil != null) 'lockoutUntil': lockoutUntil,
    };
    
    await prefs.setString(userKey, jsonEncode(data));
  }

  /// تحديث البيانات العامة
  Future<void> _updateGlobalData(
    SharedPreferences prefs,
    String globalKey,
    int attempts,
    int? lockoutUntil,
  ) async {
    final data = {
      'attempts': attempts,
      'lastAttempt': DateTime.now().millisecondsSinceEpoch,
      if (lockoutUntil != null) 'lockoutUntil': lockoutUntil,
    };
    
    await prefs.setString(globalKey, jsonEncode(data));
  }

  /// إعادة تعيين محاولات المستخدم (عند نجاح تسجيل الدخول)
  Future<void> resetUserAttempts(String identifier) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userKey = _generateUserKey(identifier);
      await prefs.remove(userKey);
    } catch (e) {
      print('خطأ في إعادة تعيين محاولات المستخدم: $e');
    }
  }

  /// إعادة تعيين العداد العام
  Future<void> resetGlobalAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final globalKey = _generateGlobalKey();
      await prefs.remove(globalKey);
    } catch (e) {
      print('خطأ في إعادة تعيين العداد العام: $e');
    }
  }

  /// تنظيف جميع البيانات
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => 
          key.startsWith(_keyPrefix) || key.startsWith(_globalKeyPrefix));
      
      for (String key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      print('خطأ في تنظيف بيانات Rate Limiter: $e');
    }
  }

  /// إيقاف الخدمة
  void dispose() {
    _cleanupTimer?.cancel();
  }
}

/// نتيجة فحص تحديد المعدل
class RateLimitResult {
  final bool canAttempt;
  final int remainingAttempts;
  final DateTime? lockoutUntil;
  final String? reason;
  final int attemptCount;

  const RateLimitResult({
    required this.canAttempt,
    required this.remainingAttempts,
    this.lockoutUntil,
    this.reason,
    this.attemptCount = 0,
  });

  /// الحصول على الوقت المتبقي للقفل
  Duration? get remainingLockoutTime {
    if (lockoutUntil == null) return null;
    final now = DateTime.now();
    if (lockoutUntil!.isBefore(now)) return null;
    return lockoutUntil!.difference(now);
  }

  /// تنسيق الوقت المتبقي
  String? get formattedRemainingTime {
    final remaining = remainingLockoutTime;
    if (remaining == null) return null;
    
    if (remaining.inHours > 0) {
      return '${remaining.inHours} ساعة و ${remaining.inMinutes % 60} دقيقة';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes} دقيقة';
    } else {
      return '${remaining.inSeconds} ثانية';
    }
  }

  @override
  String toString() {
    return 'RateLimitResult(canAttempt: $canAttempt, remainingAttempts: $remainingAttempts, lockoutUntil: $lockoutUntil)';
  }
}
