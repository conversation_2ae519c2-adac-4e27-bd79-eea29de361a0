import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class ServiceCategoryChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const ServiceCategoryChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          decoration: BoxDecoration(
            color: isSelected 
                ? AppColors.buttonWhite 
                : AppColors.buttonWhite.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
            border: isSelected 
                ? Border.all(
                    color: AppColors.primaryPurple,
                    width: 2,
                  )
                : null,
          ),
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isSelected 
                  ? AppColors.primaryPurple 
                  : AppColors.textWhite,
              fontWeight: isSelected 
                  ? FontWeight.w600 
                  : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}
