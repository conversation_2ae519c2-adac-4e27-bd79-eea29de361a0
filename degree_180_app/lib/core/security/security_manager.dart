import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:encrypt/encrypt.dart' as encrypt;

class SecurityManager {
  static const String _keyPrefix = 'secure_';
  static const String _saltKey = 'app_salt';
  static const String _encryptionKeyKey = 'encryption_key';
  static const int _maxLoginAttempts = 5;
  static const int _lockoutDurationMinutes = 15;

  static SecurityManager? _instance;
  static SecurityManager get instance => _instance ??= SecurityManager._();

  SecurityManager._();

  // AES Encrypter instance
  encrypt.Encrypter? _encrypter;
  encrypt.Key? _encryptionKey;

  // Generate a secure random salt
  Future<String> _getSalt() async {
    final prefs = await SharedPreferences.getInstance();
    String? salt = prefs.getString(_saltKey);
    
    if (salt == null) {
      salt = _generateRandomString(32);
      await prefs.setString(_saltKey, salt);
    }
    
    return salt;
  }

  // Generate random string for salt
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// تهيئة نظام التشفير
  Future<void> _initializeEncryption() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? keyString = prefs.getString(_encryptionKeyKey);

      if (keyString == null) {
        // توليد مفتاح تشفير جديد
        _encryptionKey = encrypt.Key.fromSecureRandom(32); // AES-256
        await prefs.setString(_encryptionKeyKey, _encryptionKey!.base64);
      } else {
        _encryptionKey = encrypt.Key.fromBase64(keyString);
      }

      _encrypter = encrypt.Encrypter(encrypt.AES(_encryptionKey!));
    } catch (e) {
      debugPrint('خطأ في تهيئة التشفير: $e');
      // في حالة الخطأ، استخدام مفتاح افتراضي (غير آمن للإنتاج)
      _encryptionKey = encrypt.Key.fromBase64('my32lengthsupersecretnooneknows1');
      _encrypter = encrypt.Encrypter(encrypt.AES(_encryptionKey!));
    }
  }

  /// تشفير البيانات الحساسة باستخدام AES-256
  Future<String> encryptData(String data) async {
    try {
      if (_encrypter == null) {
        await _initializeEncryption();
      }

      final iv = encrypt.IV.fromSecureRandom(16); // IV عشوائي لكل عملية تشفير
      final encrypted = _encrypter!.encrypt(data, iv: iv);

      // دمج IV مع البيانات المشفرة
      final combined = '${iv.base64}:${encrypted.base64}';
      return base64.encode(utf8.encode(combined));
    } catch (e) {
      debugPrint('خطأ في التشفير: $e');
      // في حالة الخطأ، استخدام تشفير بديل
      return _fallbackEncrypt(data);
    }
  }

  /// تشفير بديل في حالة فشل AES
  String _fallbackEncrypt(String data) {
    try {
      final bytes = utf8.encode(data);
      final key = utf8.encode('fallback_key_12345');
      final encrypted = <int>[];

      for (int i = 0; i < bytes.length; i++) {
        encrypted.add(bytes[i] ^ key[i % key.length]);
      }

      return base64.encode(encrypted);
    } catch (e) {
      debugPrint('خطأ في التشفير البديل: $e');
      return data; // إرجاع البيانات الأصلية كحل أخير
    }
  }

  /// فك تشفير البيانات الحساسة باستخدام AES-256
  Future<String> decryptData(String encryptedData) async {
    try {
      if (_encrypter == null) {
        await _initializeEncryption();
      }

      // فك تشفير البيانات المدمجة
      final decodedData = utf8.decode(base64.decode(encryptedData));
      final parts = decodedData.split(':');

      if (parts.length != 2) {
        // محاولة فك التشفير البديل
        return _fallbackDecrypt(encryptedData);
      }

      final iv = encrypt.IV.fromBase64(parts[0]);
      final encrypted = encrypt.Encrypted.fromBase64(parts[1]);

      final decrypted = _encrypter!.decrypt(encrypted, iv: iv);
      return decrypted;
    } catch (e) {
      debugPrint('خطأ في فك التشفير: $e');
      // محاولة فك التشفير البديل
      return _fallbackDecrypt(encryptedData);
    }
  }

  /// فك تشفير بديل في حالة فشل AES
  String _fallbackDecrypt(String encryptedData) {
    try {
      final encrypted = base64.decode(encryptedData);
      final key = utf8.encode('fallback_key_12345');
      final decrypted = <int>[];

      for (int i = 0; i < encrypted.length; i++) {
        decrypted.add(encrypted[i] ^ key[i % key.length]);
      }

      return utf8.decode(decrypted);
    } catch (e) {
      debugPrint('خطأ في فك التشفير البديل: $e');
      return encryptedData; // إرجاع البيانات المشفرة كحل أخير
    }
  }

  // Store encrypted data
  Future<bool> storeSecureData(String key, String data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = await encryptData(data);
      return await prefs.setString('$_keyPrefix$key', encryptedData);
    } catch (e) {
      debugPrint('Store secure data error: $e');
      return false;
    }
  }

  // Retrieve and decrypt data
  Future<String?> getSecureData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString('$_keyPrefix$key');
      
      if (encryptedData == null) return null;
      
      return await decryptData(encryptedData);
    } catch (e) {
      debugPrint('Get secure data error: $e');
      return null;
    }
  }

  // Remove secure data
  Future<bool> removeSecureData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove('$_keyPrefix$key');
    } catch (e) {
      debugPrint('Remove secure data error: $e');
      return false;
    }
  }

  // Hash password with salt
  Future<String> hashPassword(String password) async {
    final salt = await _getSalt();
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Verify password
  Future<bool> verifyPassword(String password, String hashedPassword) async {
    final hash = await hashPassword(password);
    return hash == hashedPassword;
  }

  // Login attempt tracking
  Future<bool> canAttemptLogin(String identifier) async {
    final prefs = await SharedPreferences.getInstance();
    final attemptsKey = 'login_attempts_$identifier';
    final lockoutKey = 'lockout_time_$identifier';
    
    // Check if currently locked out
    final lockoutTime = prefs.getInt(lockoutKey);
    if (lockoutTime != null) {
      final lockoutDateTime = DateTime.fromMillisecondsSinceEpoch(lockoutTime);
      final now = DateTime.now();
      
      if (now.isBefore(lockoutDateTime)) {
        return false; // Still locked out
      } else {
        // Lockout expired, reset attempts
        await prefs.remove(attemptsKey);
        await prefs.remove(lockoutKey);
      }
    }
    
    return true;
  }

  // Record failed login attempt
  Future<void> recordFailedLogin(String identifier) async {
    final prefs = await SharedPreferences.getInstance();
    final attemptsKey = 'login_attempts_$identifier';
    
    final currentAttempts = prefs.getInt(attemptsKey) ?? 0;
    final newAttempts = currentAttempts + 1;
    
    await prefs.setInt(attemptsKey, newAttempts);
    
    // Lock out if max attempts reached
    if (newAttempts >= _maxLoginAttempts) {
      final lockoutTime = DateTime.now()
          .add(Duration(minutes: _lockoutDurationMinutes))
          .millisecondsSinceEpoch;
      await prefs.setInt('lockout_time_$identifier', lockoutTime);
    }
  }

  // Record successful login (reset attempts)
  Future<void> recordSuccessfulLogin(String identifier) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('login_attempts_$identifier');
    await prefs.remove('lockout_time_$identifier');
  }

  // Get remaining lockout time
  Future<Duration?> getRemainingLockoutTime(String identifier) async {
    final prefs = await SharedPreferences.getInstance();
    final lockoutTime = prefs.getInt('lockout_time_$identifier');
    
    if (lockoutTime == null) return null;
    
    final lockoutDateTime = DateTime.fromMillisecondsSinceEpoch(lockoutTime);
    final now = DateTime.now();
    
    if (now.isBefore(lockoutDateTime)) {
      return lockoutDateTime.difference(now);
    }
    
    return null;
  }

  // Validate input for security
  bool isValidInput(String input, {int maxLength = 1000}) {
    if (input.isEmpty || input.length > maxLength) return false;
    
    // Check for potential SQL injection patterns
    final sqlPatterns = [
      RegExp(r"('|(\\')|(;)|(\\;)|(--)|(\s*or\s+))", caseSensitive: false),
      RegExp(r"(\s*union\s+)|(\s*select\s+)|(\s*insert\s+)|(\s*delete\s+)|(\s*update\s+)", caseSensitive: false),
    ];
    
    for (final pattern in sqlPatterns) {
      if (pattern.hasMatch(input)) return false;
    }
    
    // Check for XSS patterns
    final xssPatterns = [
      RegExp(r"<script[^>]*>.*?</script>", caseSensitive: false),
      RegExp(r"javascript:", caseSensitive: false),
      RegExp(r"on\w+\s*=", caseSensitive: false),
    ];
    
    for (final pattern in xssPatterns) {
      if (pattern.hasMatch(input)) return false;
    }
    
    return true;
  }

  // Sanitize input
  String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'[<>"]'), '')
        .replaceAll(RegExp(r"[']"), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .replaceAll(RegExp(r'on\w+\s*=', caseSensitive: false), '')
        .trim();
  }

  // Generate secure token
  String generateSecureToken({int length = 32}) {
    return _generateRandomString(length);
  }

  // Clear all secure data (for logout)
  Future<void> clearAllSecureData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_keyPrefix));
      
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      debugPrint('Clear secure data error: $e');
    }
  }

  // Check if app is running in debug mode
  bool get isDebugMode => kDebugMode;

  // Check if app is running on emulator (basic check)
  bool get isPotentiallyEmulator {
    // This is a basic check - in production, use more sophisticated detection
    return kDebugMode;
  }
}
