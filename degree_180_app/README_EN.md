# 🎉 180 Degree - Beauty & Personal Care App

<div align="center">
  <h3>Premium Beauty & Personal Care Services Platform</h3>
  
  [![Flutter](https://img.shields.io/badge/Flutter-3.0+-blue.svg)](https://flutter.dev/)
  [![Firebase](https://img.shields.io/badge/Firebase-Integrated-orange.svg)](https://firebase.google.com/)
  [![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
  [![Platform](https://img.shields.io/badge/Platform-iOS%20%7C%20Android-lightgrey.svg)](https://flutter.dev/)
</div>

## 📱 About

**180 Degree** is a comprehensive beauty and personal care services platform that connects customers with premium service providers. Built with Flutter and Firebase, it offers a seamless booking experience with advanced features.

## ✨ Key Features

### 🔐 Authentication & User Management
- **Multi-platform Authentication**: Email, Google, Apple Sign-in
- **User Profiles**: Comprehensive profile management
- **Role-based Access**: Customer and Provider roles
- **Biometric Authentication**: Touch ID / Face ID support

### 🏠 Service Discovery
- **Advanced Search**: Filter by location, rating, price, availability
- **Real-time Location**: GPS-based nearby services
- **Service Categories**: Salons, Spas, Barbershops, Nail services
- **Provider Profiles**: Detailed information, galleries, reviews

### 📅 Smart Booking System
- **Multi-step Booking**: Service selection, time slots, confirmation
- **Real-time Availability**: Live calendar integration
- **Booking Management**: View, modify, cancel appointments
- **Automated Reminders**: Push notifications and SMS

### 💳 Advanced Payment System
- **Multiple Payment Methods**: Credit cards, K-Net, Apple Pay, Google Pay, Wallet, Cash
- **Secure Processing**: PCI-compliant payment gateways
- **Payment History**: Detailed transaction records
- **Refund Management**: Automated refund processing

### 💬 Real-time Communication
- **In-app Messaging**: Chat with service providers
- **Media Sharing**: Photos, voice messages, location
- **Video Calls**: Consultation and support
- **Push Notifications**: Real-time message alerts

### ⭐ Review & Rating System
- **Detailed Reviews**: Multi-category ratings
- **Photo Reviews**: Upload service photos
- **Anonymous Reviews**: Privacy options
- **Review Analytics**: Provider performance insights

### 🗺️ Maps & Navigation
- **Interactive Maps**: Google Maps integration
- **Provider Locations**: Real-time provider mapping
- **Directions**: Turn-by-turn navigation
- **Nearby Services**: Location-based recommendations

### 🔔 Smart Notifications
- **Personalized Alerts**: Booking reminders, promotions
- **Real-time Updates**: Booking status changes
- **Promotional Offers**: Targeted marketing campaigns
- **System Notifications**: App updates and announcements

## 🏗️ Technical Architecture

### 🎯 Clean Architecture
```
├── Core Layer
│   ├── Constants
│   ├── Models
│   ├── Services
│   ├── Widgets
│   └── Utils
├── Features Layer
│   ├── Authentication
│   ├── Home
│   ├── Booking
│   ├── Payment
│   ├── Chat
│   ├── Reviews
│   ├── Maps
│   └── Profile
└── Presentation Layer
    ├── Pages
    ├── Widgets
    └── BLoC
```

### 🔥 Firebase Integration
- **Authentication**: Multi-provider auth
- **Firestore**: Real-time database
- **Storage**: File and image storage
- **Messaging**: Push notifications
- **Analytics**: User behavior tracking
- **Crashlytics**: Error monitoring

### 📊 State Management
- **BLoC Pattern**: Predictable state management
- **Event-driven**: Reactive programming
- **Dependency Injection**: GetIt service locator
- **Error Handling**: Comprehensive error management

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.0+)
- Dart SDK (3.0+)
- Android Studio / VS Code
- Firebase Project
- Google Maps API Key

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/degree_180_app.git
cd degree_180_app
```

2. **Install dependencies**
```bash
flutter pub get
```

3. **Firebase Setup**
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase
firebase init
```

4. **Configure Firebase**
- Add `google-services.json` (Android)
- Add `GoogleService-Info.plist` (iOS)
- Update Firebase configuration

5. **Google Maps Setup**
- Get Google Maps API key
- Add to `android/app/src/main/AndroidManifest.xml`
- Add to `ios/Runner/AppDelegate.swift`

6. **Run the app**
```bash
flutter run
```

## 📁 Project Structure

```
lib/
├── core/
│   ├── constants/
│   ├── models/
│   ├── services/
│   └── widgets/
├── features/
│   ├── auth/
│   ├── home/
│   ├── booking/
│   ├── payment/
│   ├── chat/
│   ├── reviews/
│   ├── maps/
│   └── profile/
└── main.dart
```

## 🔧 Configuration

### Environment Variables
Create `.env` file in the root directory:
```env
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
FIREBASE_PROJECT_ID=your_firebase_project_id
PAYMENT_GATEWAY_KEY=your_payment_gateway_key
```

## 🧪 Testing

### Unit Tests
```bash
flutter test
```

### Integration Tests
```bash
flutter test integration_test/
```

## 📦 Build & Deployment

### Android
```bash
flutter build apk --release
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

### Web
```bash
flutter build web --release
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Website: https://180degree.com
- Documentation: https://docs.180degree.com

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Firebase for backend services
- Google Maps for location services
- All open-source contributors

---

<div align="center">
  <p>Made with ❤️ by the 180 Degree Team</p>
  <p>© 2024 180 Degree. All rights reserved.</p>
</div>
