import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/service_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/enums/booking_status.dart';
import '../../domain/repositories/booking_repository.dart' as repo;

abstract class BookingRemoteDataSource {
  Future<BookingModel> createBooking(repo.CreateBookingParams params);
  Future<List<BookingModel>> getUserBookings(String userId, {BookingStatus? status, int? limit, String? lastDocumentId});
  Future<List<BookingModel>> getProviderBookings(String providerId, {BookingStatus? status, DateTime? startDate, DateTime? endDate, int? limit, String? lastDocumentId});
  Future<BookingModel> getBookingById(String bookingId);
  Future<BookingModel> updateBookingStatus(String bookingId, BookingStatus status, {String? reason, String? updatedBy});
  Future<BookingModel> cancelBooking(String bookingId, String reason, String cancelledBy);
  Future<BookingModel> rescheduleBooking(String bookingId, DateTime newDate, String newTimeSlot);
  Future<List<TimeSlotModel>> getAvailableTimeSlots(String providerId, DateTime date, int serviceDurationMinutes);
  Future<Map<String, dynamic>> getBookingStatistics(String providerId, {DateTime? startDate, DateTime? endDate});
  Future<List<BookingModel>> getUpcomingBookings(DateTime reminderTime);
  Future<void> markReminderSent(String bookingId);
  Future<List<BookingModel>> getBookingHistory(String userId, {int? limit, String? lastDocumentId});
}

class BookingRemoteDataSourceImpl implements BookingRemoteDataSource {
  final FirebaseFirestore firestore;

  BookingRemoteDataSourceImpl({required this.firestore});

  @override
  Future<BookingModel> createBooking(repo.CreateBookingParams params) async {
    try {
      // Get service details
      final serviceDoc = await firestore
          .collection('service_providers')
          .doc(params.providerId)
          .collection('services')
          .doc(params.serviceId)
          .get();

      if (!serviceDoc.exists) {
        throw ServerException(message: "Server error");
      }

      final service = ServiceModel.fromFirestore(serviceDoc);

      // Create booking
      final bookingData = {
        'customerId': params.customerId,
        'customerName': params.customerName,
        'customerPhone': params.customerPhone,
        'customerEmail': params.customerEmail,
        'providerId': params.providerId,
        'providerName': params.providerName,
        'serviceId': params.serviceId,
        'service': service.toJson(),
        'bookingDate': Timestamp.fromDate(params.bookingDate),
        'timeSlot': params.timeSlot,
        'status': BookingStatus.pending.toString().split('.').last,
        'totalAmount': params.totalAmount,
        'currency': params.currency,
        'discountAmount': params.discountAmount,
        'couponCode': params.couponCode,
        'notes': params.notes,
        'paymentInfo': params.paymentInfo,
        'isReminderSent': false,
        'isReviewSubmitted': false,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      final docRef = await firestore.collection('bookings').add(bookingData);
      
      // Update the document with its ID
      await docRef.update({'id': docRef.id});
      
      // Get the created booking
      final createdDoc = await docRef.get();
      return BookingModel.fromFirestore(createdDoc);
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<BookingModel>> getUserBookings(
    String userId, {
    BookingStatus? status,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = firestore
          .collection('bookings')
          .where('customerId', isEqualTo: userId);

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString().split('.').last);
      }

      query = query.orderBy('bookingDate', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      if (lastDocumentId != null) {
        final lastDoc = await firestore.collection('bookings').doc(lastDocumentId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => BookingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<BookingModel>> getProviderBookings(
    String providerId, {
    BookingStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = firestore
          .collection('bookings')
          .where('providerId', isEqualTo: providerId);

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString().split('.').last);
      }

      if (startDate != null) {
        query = query.where('bookingDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('bookingDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      query = query.orderBy('bookingDate', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      if (lastDocumentId != null) {
        final lastDoc = await firestore.collection('bookings').doc(lastDocumentId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => BookingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<BookingModel> getBookingById(String bookingId) async {
    try {
      final doc = await firestore.collection('bookings').doc(bookingId).get();
      if (!doc.exists) {
        throw ServerException(message: "Server error");
      }
      return BookingModel.fromFirestore(doc);
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<BookingModel> updateBookingStatus(
    String bookingId,
    BookingStatus status, {
    String? reason,
    String? updatedBy,
  }) async {
    try {
      final updateData = {
        'status': status.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (status == BookingStatus.cancelled && reason != null) {
        updateData['cancellationReason'] = reason;
        updateData['cancellationDate'] = FieldValue.serverTimestamp();
        updateData['cancellationBy'] = updatedBy ?? 'system';
      }

      if (status == BookingStatus.completed) {
        updateData['completedAt'] = FieldValue.serverTimestamp();
      }

      await firestore.collection('bookings').doc(bookingId).update(updateData);

      final updatedDoc = await firestore.collection('bookings').doc(bookingId).get();
      return BookingModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<BookingModel> cancelBooking(
    String bookingId,
    String reason,
    String cancelledBy,
  ) async {
    try {
      await firestore.collection('bookings').doc(bookingId).update({
        'status': BookingStatus.cancelled.toString().split('.').last,
        'cancellationReason': reason,
        'cancellationDate': FieldValue.serverTimestamp(),
        'cancellationBy': cancelledBy,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final updatedDoc = await firestore.collection('bookings').doc(bookingId).get();
      return BookingModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<BookingModel> rescheduleBooking(
    String bookingId,
    DateTime newDate,
    String newTimeSlot,
  ) async {
    try {
      await firestore.collection('bookings').doc(bookingId).update({
        'bookingDate': Timestamp.fromDate(newDate),
        'timeSlot': newTimeSlot,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      final updatedDoc = await firestore.collection('bookings').doc(bookingId).get();
      return BookingModel.fromFirestore(updatedDoc);
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<TimeSlotModel>> getAvailableTimeSlots(
    String providerId,
    DateTime date,
    int serviceDurationMinutes,
  ) async {
    try {
      final dateString = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      
      // Get provider's working hours
      final providerDoc = await firestore.collection('service_providers').doc(providerId).get();
      if (!providerDoc.exists) {
        throw ServerException(message: "Server error");
      }

      // Get existing bookings for the date
      final bookingsQuery = await firestore
          .collection('bookings')
          .where('providerId', isEqualTo: providerId)
          .where('bookingDate', isEqualTo: Timestamp.fromDate(date))
          .where('status', whereIn: ['pending', 'confirmed'])
          .get();

      final bookedSlots = bookingsQuery.docs
          .map((doc) => doc.data()['timeSlot'] as String)
          .toSet();

      // Generate time slots (9 AM to 6 PM)
      final timeSlots = <TimeSlotModel>[];
      for (int hour = 9; hour < 18; hour++) {
        final startTime = DateTime(date.year, date.month, date.day, hour, 0);
        final endTime = startTime.add(Duration(minutes: serviceDurationMinutes));
        
        // Don't allow slots that go beyond 6 PM
        if (endTime.hour >= 18) continue;

        final timeSlot = '${hour.toString().padLeft(2, '0')}:00 - ${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}';
        final isAvailable = !bookedSlots.contains(timeSlot);

        timeSlots.add(TimeSlotModel(
          id: '${dateString}_$hour',
          startTime: startTime,
          endTime: endTime,
          isAvailable: isAvailable,
          isBooked: !isAvailable,
          durationMinutes: serviceDurationMinutes,
        ));
      }

      return timeSlots;
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<Map<String, dynamic>> getBookingStatistics(
    String providerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query query = firestore
          .collection('bookings')
          .where('providerId', isEqualTo: providerId);

      if (startDate != null) {
        query = query.where('bookingDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('bookingDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final querySnapshot = await query.get();
      final bookings = querySnapshot.docs;

      final totalBookings = bookings.length;
      final completedBookings = bookings.where((doc) => (doc.data() as Map<String, dynamic>)['status'] == 'completed').length;
      final cancelledBookings = bookings.where((doc) => (doc.data() as Map<String, dynamic>)['status'] == 'cancelled').length;
      final pendingBookings = bookings.where((doc) => (doc.data() as Map<String, dynamic>)['status'] == 'pending').length;

      final totalRevenue = bookings
          .where((doc) => (doc.data() as Map<String, dynamic>)['status'] == 'completed')
          .fold<double>(0.0, (sum, doc) => sum + ((doc.data() as Map<String, dynamic>)['totalAmount'] as num).toDouble());

      return {
        'totalBookings': totalBookings,
        'completedBookings': completedBookings,
        'cancelledBookings': cancelledBookings,
        'pendingBookings': pendingBookings,
        'totalRevenue': totalRevenue,
        'completionRate': totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0.0,
        'cancellationRate': totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0.0,
      };
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<BookingModel>> getUpcomingBookings(DateTime reminderTime) async {
    try {
      final query = await firestore
          .collection('bookings')
          .where('bookingDate', isGreaterThanOrEqualTo: Timestamp.fromDate(DateTime.now()))
          .where('bookingDate', isLessThanOrEqualTo: Timestamp.fromDate(reminderTime))
          .where('status', whereIn: ['pending', 'confirmed'])
          .where('isReminderSent', isEqualTo: false)
          .get();

      return query.docs
          .map((doc) => BookingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<void> markReminderSent(String bookingId) async {
    try {
      await firestore.collection('bookings').doc(bookingId).update({
        'isReminderSent': true,
        'reminderSentAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<BookingModel>> getBookingHistory(
    String userId, {
    int? limit,
    String? lastDocumentId,
  }) async {
    try {
      Query query = firestore
          .collection('bookings')
          .where('customerId', isEqualTo: userId)
          .where('status', whereIn: ['completed', 'cancelled'])
          .orderBy('bookingDate', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      if (lastDocumentId != null) {
        final lastDoc = await firestore.collection('bookings').doc(lastDocumentId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => BookingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }
}
