import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:degree_180_app/core/security/rate_limiter.dart';

void main() {
  group('RateLimiter Tests', () {
    late RateLimiter rateLimiter;

    setUp(() async {
      // تهيئة SharedPreferences للاختبار
      SharedPreferences.setMockInitialValues({});
      rateLimiter = RateLimiter.instance;
      
      // تنظيف البيانات قبل كل اختبار
      await rateLimiter.clearAllData();
    });

    tearDown(() async {
      // تنظيف البيانات بعد كل اختبار
      await rateLimiter.clearAllData();
    });

    group('canAttempt', () {
      test('should allow first attempt for new user', () async {
        // arrange
        const String email = '<EMAIL>';

        // act
        final result = await rateLimiter.canAttempt(email);

        // assert
        expect(result.canAttempt, true);
        expect(result.remainingAttempts, RateLimiter.maxLoginAttempts);
        expect(result.lockoutUntil, isNull);
        expect(result.reason, isNull);
      });

      test('should track failed attempts correctly', () async {
        // arrange
        const String email = '<EMAIL>';

        // act & assert
        // المحاولة الأولى
        var result = await rateLimiter.canAttempt(email);
        expect(result.canAttempt, true);
        expect(result.remainingAttempts, RateLimiter.maxLoginAttempts);

        // تسجيل محاولة فاشلة
        await rateLimiter.recordFailedAttempt(email);

        // المحاولة الثانية
        result = await rateLimiter.canAttempt(email);
        expect(result.canAttempt, true);
        expect(result.remainingAttempts, RateLimiter.maxLoginAttempts - 1);
      });

      test('should block user after max attempts', () async {
        // arrange
        const String email = '<EMAIL>';

        // act
        // تسجيل المحاولات الفاشلة حتى الوصول للحد الأقصى
        for (int i = 0; i < RateLimiter.maxLoginAttempts; i++) {
          await rateLimiter.recordFailedAttempt(email);
        }

        final result = await rateLimiter.canAttempt(email);

        // assert
        expect(result.canAttempt, false);
        expect(result.remainingAttempts, 0);
        expect(result.lockoutUntil, isNotNull);
        expect(result.reason, isNotNull);
        expect(result.reason, contains('تم تجاوز الحد الأقصى'));
      });

      test('should provide correct remaining time', () async {
        // arrange
        const String email = '<EMAIL>';

        // act
        // تسجيل المحاولات الفاشلة حتى القفل
        for (int i = 0; i < RateLimiter.maxLoginAttempts; i++) {
          await rateLimiter.recordFailedAttempt(email);
        }

        final result = await rateLimiter.canAttempt(email);

        // assert
        expect(result.remainingLockoutTime, isNotNull);
        expect(result.remainingLockoutTime!.inMinutes, 
               lessThanOrEqualTo(RateLimiter.lockoutDuration.inMinutes));
        expect(result.formattedRemainingTime, isNotNull);
      });

      test('should handle different users independently', () async {
        // arrange
        const String email1 = '<EMAIL>';
        const String email2 = '<EMAIL>';

        // act
        // قفل المستخدم الأول
        for (int i = 0; i < RateLimiter.maxLoginAttempts; i++) {
          await rateLimiter.recordFailedAttempt(email1);
        }

        final result1 = await rateLimiter.canAttempt(email1);
        final result2 = await rateLimiter.canAttempt(email2);

        // assert
        expect(result1.canAttempt, false);
        expect(result2.canAttempt, true);
        expect(result2.remainingAttempts, RateLimiter.maxLoginAttempts);
      });
    });

    group('resetUserAttempts', () {
      test('should reset user attempts after successful login', () async {
        // arrange
        const String email = '<EMAIL>';

        // act
        // تسجيل بعض المحاولات الفاشلة
        await rateLimiter.recordFailedAttempt(email);
        await rateLimiter.recordFailedAttempt(email);

        var result = await rateLimiter.canAttempt(email);
        expect(result.remainingAttempts, RateLimiter.maxLoginAttempts - 2);

        // إعادة تعيين المحاولات
        await rateLimiter.resetUserAttempts(email);

        result = await rateLimiter.canAttempt(email);

        // assert
        expect(result.canAttempt, true);
        expect(result.remainingAttempts, RateLimiter.maxLoginAttempts);
      });
    });

    group('Global rate limiting', () {
      test('should track global attempts', () async {
        // arrange
        const List<String> emails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        // act
        // تسجيل محاولات فاشلة من مستخدمين مختلفين
        for (int i = 0; i < RateLimiter.maxGlobalAttempts; i++) {
          final email = emails[i % emails.length];
          await rateLimiter.recordFailedAttempt(email);
        }

        // فحص المستخدمين
        final results = <RateLimitResult>[];
        for (String email in emails) {
          results.add(await rateLimiter.canAttempt(email));
        }

        // assert
        // يجب أن يكون هناك قفل عام
        expect(results.any((r) => !r.canAttempt), true);
      });
    });

    group('Data cleanup', () {
      test('should clear all data', () async {
        // arrange
        const String email = '<EMAIL>';
        await rateLimiter.recordFailedAttempt(email);

        var result = await rateLimiter.canAttempt(email);
        expect(result.remainingAttempts, lessThan(RateLimiter.maxLoginAttempts));

        // act
        await rateLimiter.clearAllData();

        result = await rateLimiter.canAttempt(email);

        // assert
        expect(result.canAttempt, true);
        expect(result.remainingAttempts, RateLimiter.maxLoginAttempts);
      });
    });

    group('Edge cases', () {
      test('should handle empty email', () async {
        // arrange
        const String email = '';

        // act
        final result = await rateLimiter.canAttempt(email);

        // assert
        expect(result.canAttempt, true);
      });

      test('should handle very long email', () async {
        // arrange
        final String email = 'a' * 1000 + '@example.com';

        // act
        final result = await rateLimiter.canAttempt(email);

        // assert
        expect(result.canAttempt, true);
      });

      test('should handle special characters in email', () async {
        // arrange
        const String email = '<EMAIL>';

        // act
        await rateLimiter.recordFailedAttempt(email);
        final result = await rateLimiter.canAttempt(email);

        // assert
        expect(result.canAttempt, true);
        expect(result.remainingAttempts, RateLimiter.maxLoginAttempts - 1);
      });

      test('should handle case sensitivity', () async {
        // arrange
        const String email1 = '<EMAIL>';
        const String email2 = '<EMAIL>';

        // act
        await rateLimiter.recordFailedAttempt(email1);
        final result = await rateLimiter.canAttempt(email2);

        // assert
        // يجب أن يعامل الإيميلات كنفس المستخدم (case insensitive)
        expect(result.remainingAttempts, RateLimiter.maxLoginAttempts - 1);
      });
    });

    group('RateLimitResult', () {
      test('should format remaining time correctly', () {
        // arrange
        final now = DateTime.now();
        final lockoutUntil = now.add(const Duration(hours: 1, minutes: 30));
        
        final result = RateLimitResult(
          canAttempt: false,
          remainingAttempts: 0,
          lockoutUntil: lockoutUntil,
          reason: 'Test lockout',
        );

        // act
        final formattedTime = result.formattedRemainingTime;

        // assert
        expect(formattedTime, isNotNull);
        expect(formattedTime, contains('ساعة'));
        expect(formattedTime, contains('دقيقة'));
      });

      test('should handle expired lockout', () {
        // arrange
        final now = DateTime.now();
        final lockoutUntil = now.subtract(const Duration(minutes: 1));
        
        final result = RateLimitResult(
          canAttempt: false,
          remainingAttempts: 0,
          lockoutUntil: lockoutUntil,
          reason: 'Test lockout',
        );

        // act
        final remainingTime = result.remainingLockoutTime;
        final formattedTime = result.formattedRemainingTime;

        // assert
        expect(remainingTime, isNull);
        expect(formattedTime, isNull);
      });
    });
  });
}
