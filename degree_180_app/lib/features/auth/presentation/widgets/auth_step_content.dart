import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/enums/service_type.dart';
import '../../../../core/theme/theme_manager.dart';
import '../pages/auth_flow_page.dart';


/// محتوى خطوات المصادقة
class AuthStepContent extends StatelessWidget {
  final AuthStepData step;
  final Gender? selectedGender;
  final String? selectedUserType;
  final String? selectedServiceType;
  final bool isLoginMode;
  final Function(Gender) onGenderSelected;
  final Function(String) onUserTypeSelected;
  final Function(String) onServiceTypeSelected;
  final Function(bool) onAuthModeChanged;
  final VoidCallback onNext;
  final VoidCallback onPrevious;

  const AuthStepContent({
    super.key,
    required this.step,
    this.selectedGender,
    this.selectedUserType,
    this.selectedServiceType,
    required this.isLoginMode,
    required this.onGenderSelected,
    required this.onUserTypeSelected,
    required this.onServiceTypeSelected,
    required this.onAuthModeChanged,
    required this.onNext,
    required this.onPrevious,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // عنوان الخطوة
          _buildStepHeader(),
          
          const SizedBox(height: AppConstants.extraLargePadding),
          
          // محتوى الخطوة
          Expanded(
            child: _buildStepContent(context),
          ),
        ],
      ),
    );
  }

  Widget _buildStepHeader() {
    return Column(
      children: [
        Text(
          step.title,
          style: GoogleFonts.cairo(
            fontSize: AppConstants.fontSizeExtraLarge,
            fontWeight: FontWeight.bold,
            color: ThemeManager.currentColors.primary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          step.subtitle,
          style: GoogleFonts.cairo(
            fontSize: AppConstants.fontSizeMedium,
            color: AppColors.textGray,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStepContent(BuildContext context) {
    switch (step.stepType) {
      case AuthStepType.genderSelection:
        return ModernGenderSelector(
          selectedGender: selectedGender,
          onGenderSelected: onGenderSelected,
        );
        
      case AuthStepType.userTypeSelection:
        return _buildUserTypeSelector();

      case AuthStepType.serviceTypeSelection:
        return _buildServiceTypeSelector();

      case AuthStepType.authentication:
        return _buildAuthForm();
    }
  }

  Widget _buildUserTypeSelector() {
    return Column(
      children: [
        Text(
          'اختر نوع حسابك',
          style: GoogleFonts.cairo(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
            color: ThemeManager.currentColors.primary,
          ),
        ),
        const SizedBox(height: AppConstants.extraLargePadding),
        // سيتم إضافة المحتوى لاحقاً
        const Placeholder(fallbackHeight: 200),
      ],
    );
  }

  Widget _buildServiceTypeSelector() {
    return Column(
      children: [
        Text(
          'اختر نوع الخدمة',
          style: GoogleFonts.cairo(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
            color: ThemeManager.currentColors.primary,
          ),
        ),
        const SizedBox(height: AppConstants.extraLargePadding),
        // سيتم إضافة المحتوى لاحقاً
        const Placeholder(fallbackHeight: 200),
      ],
    );
  }

  Widget _buildAuthForm() {
    return Column(
      children: [
        Text(
          isLoginMode ? 'تسجيل الدخول' : 'إنشاء حساب',
          style: GoogleFonts.cairo(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
            color: ThemeManager.currentColors.primary,
          ),
        ),
        const SizedBox(height: AppConstants.extraLargePadding),
        // سيتم إضافة المحتوى لاحقاً
        const Placeholder(fallbackHeight: 300),
      ],
    );
  }
}

/// اختيار الجنس العصري
class ModernGenderSelector extends StatefulWidget {
  final Gender? selectedGender;
  final Function(Gender) onGenderSelected;

  const ModernGenderSelector({
    super.key,
    this.selectedGender,
    required this.onGenderSelected,
  });

  @override
  State<ModernGenderSelector> createState() => _ModernGenderSelectorState();
}

class _ModernGenderSelectorState extends State<ModernGenderSelector>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _cardAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _cardAnimations = List.generate(2, (index) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.2,
          0.6 + (index * 0.2),
          curve: Curves.elasticOut,
        ),
      ));
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // وصف الثيم
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: ThemeManager.currentColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            border: Border.all(
              color: ThemeManager.currentColors.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.palette,
                color: ThemeManager.currentColors.primary,
                size: 24,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Text(
                  'اختر الثيم الذي يعكس شخصيتك ويجعل تجربتك أكثر متعة',
                  style: GoogleFonts.cairo(
                    fontSize: AppConstants.fontSizeSmall,
                    color: ThemeManager.currentColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: AppConstants.extraLargePadding),
        
        // خيارات الجنس
        Expanded(
          child: Row(
            children: [
              // ثيم نسائي
              Expanded(
                child: AnimatedBuilder(
                  animation: _cardAnimations[0],
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _cardAnimations[0].value,
                      child: _buildGenderCard(
                        gender: Gender.female,
                        title: 'ثيم نسائي',
                        subtitle: 'ألوان وردية ناعمة',
                        icon: Icons.favorite,
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFF6B9D), Color(0xFFFF8E9B)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(width: AppConstants.defaultPadding),
              
              // ثيم رجالي
              Expanded(
                child: AnimatedBuilder(
                  animation: _cardAnimations[1],
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _cardAnimations[1].value,
                      child: _buildGenderCard(
                        gender: Gender.male,
                        title: 'ثيم رجالي',
                        subtitle: 'ألوان زرقاء قوية',
                        icon: Icons.star,
                        gradient: const LinearGradient(
                          colors: [Color(0xFF4A90E2), Color(0xFF357ABD)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGenderCard({
    required Gender gender,
    required String title,
    required String subtitle,
    required IconData icon,
    required Gradient gradient,
  }) {
    final isSelected = widget.selectedGender == gender;
    
    return GestureDetector(
      onTap: () => widget.onGenderSelected(gender),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        height: 200,
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withOpacity(0.3),
              blurRadius: isSelected ? 20 : 10,
              spreadRadius: isSelected ? 2 : 0,
              offset: const Offset(0, 8),
            ),
          ],
          border: isSelected
              ? Border.all(color: AppColors.textWhite, width: 3)
              : null,
        ),
        child: Stack(
          children: [
            // أيقونة الخلفية
            Positioned(
              top: -20,
              right: -20,
              child: Icon(
                icon,
                size: 100,
                color: AppColors.textWhite.withOpacity(0.2),
              ),
            ),
            
            // المحتوى
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    icon,
                    color: AppColors.textWhite,
                    size: 32,
                  ),
                  
                  const Spacer(),
                  
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textWhite,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.smallPadding),
                  
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppColors.textWhite.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
            
            // علامة الاختيار
            if (isSelected)
              Positioned(
                top: AppConstants.smallPadding,
                left: AppConstants.smallPadding,
                child: Container(
                  width: 30,
                  height: 30,
                  decoration: const BoxDecoration(
                    color: AppColors.textWhite,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check,
                    color: gradient.colors.first,
                    size: 20,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
