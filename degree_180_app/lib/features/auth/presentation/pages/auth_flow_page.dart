import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/theme/theme_manager.dart';
import '../widgets/auth_step_indicator.dart';
import '../widgets/auth_step_content.dart';
import '../bloc/auth_bloc.dart';

/// صفحة تدفق المصادقة الموحدة
/// تدير جميع خطوات المصادقة في مكان واحد
class AuthFlowPage extends StatefulWidget {
  const AuthFlowPage({super.key});

  @override
  State<AuthFlowPage> createState() => _AuthFlowPageState();
}

class _AuthFlowPageState extends State<AuthFlowPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentStep = 0;
  final int _totalSteps = 4;

  // بيانات التدفق
  Gender? _selectedGender;
  String? _selectedUserType;
  String? _selectedServiceType;
  bool _isLoginMode = true;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
  }

  void _initializeControllers() {
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
  }

  void _initializeAnimations() {
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _onGenderSelected(Gender gender) {
    setState(() {
      _selectedGender = gender;
    });
    ThemeManager.setGender(gender);
    _nextStep();
  }

  void _onUserTypeSelected(String userType) {
    setState(() {
      _selectedUserType = userType;
    });
    if (userType == 'customer') {
      _nextStep();
    } else {
      // للعامل، نحتاج اختيار نوع الخدمة
      _nextStep();
    }
  }

  void _onServiceTypeSelected(String serviceType) {
    setState(() {
      _selectedServiceType = serviceType;
    });
    _nextStep();
  }

  void _onAuthModeChanged(bool isLogin) {
    setState(() {
      _isLoginMode = isLogin;
    });
  }

  List<AuthStepData> get _steps {
    return [
      AuthStepData(
        title: 'اختر الثيم المفضل',
        subtitle: 'اختر الثيم الذي يناسبك لتجربة أفضل',
        stepType: AuthStepType.genderSelection,
        isCompleted: _selectedGender != null,
      ),
      AuthStepData(
        title: 'نوع الحساب',
        subtitle: 'هل أنت عميل أم مقدم خدمة؟',
        stepType: AuthStepType.userTypeSelection,
        isCompleted: _selectedUserType != null,
      ),
      if (_selectedUserType == 'worker')
        AuthStepData(
          title: 'نوع الخدمة',
          subtitle: 'ما نوع الخدمة التي تقدمها؟',
          stepType: AuthStepType.serviceTypeSelection,
          isCompleted: _selectedServiceType != null,
        ),
      AuthStepData(
        title: _isLoginMode ? 'تسجيل الدخول' : 'إنشاء حساب',
        subtitle: _isLoginMode 
            ? 'أدخل بياناتك لتسجيل الدخول'
            : 'أدخل بياناتك لإنشاء حساب جديد',
        stepType: AuthStepType.authentication,
        isCompleted: false,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeManager.currentTheme,
      child: Scaffold(
        backgroundColor: AppColors.backgroundWhite,
        body: SafeArea(
          child: Column(
            children: [
              // Header مع مؤشر التقدم
              _buildHeader(),
              
              // محتوى الخطوات
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _steps.length,
                  itemBuilder: (context, index) {
                    return SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: AuthStepContent(
                          step: _steps[index],
                          selectedGender: _selectedGender,
                          selectedUserType: _selectedUserType,
                          selectedServiceType: _selectedServiceType,
                          isLoginMode: _isLoginMode,
                          onGenderSelected: _onGenderSelected,
                          onUserTypeSelected: _onUserTypeSelected,
                          onServiceTypeSelected: _onServiceTypeSelected,
                          onAuthModeChanged: _onAuthModeChanged,
                          onNext: _nextStep,
                          onPrevious: _previousStep,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: ThemeManager.currentColors.gradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppConstants.borderRadiusLarge),
          bottomRight: Radius.circular(AppConstants.borderRadiusLarge),
        ),
      ),
      child: Column(
        children: [
          // زر الرجوع والعنوان
          Row(
            children: [
              if (_currentStep > 0)
                IconButton(
                  onPressed: _previousStep,
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: AppColors.textWhite,
                  ),
                ),
              Expanded(
                child: Text(
                  AppStrings.appName,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.cairo(
                    fontSize: AppConstants.fontSizeExtraLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textWhite,
                  ),
                ),
              ),
              const SizedBox(width: 48), // للتوازن
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // مؤشر التقدم
          AuthStepIndicator(
            currentStep: _currentStep,
            totalSteps: _steps.length,
            completedSteps: _steps.take(_currentStep).where((s) => s.isCompleted).length,
          ),
        ],
      ),
    );
  }
}

/// بيانات خطوة المصادقة
class AuthStepData {
  final String title;
  final String subtitle;
  final AuthStepType stepType;
  final bool isCompleted;

  const AuthStepData({
    required this.title,
    required this.subtitle,
    required this.stepType,
    required this.isCompleted,
  });
}

/// أنواع خطوات المصادقة
enum AuthStepType {
  genderSelection,
  userTypeSelection,
  serviceTypeSelection,
  authentication,
}
