import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/booking_model.dart';
import '../models/service_model.dart';
import '../models/payment_model.dart';

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  late FirebaseAnalytics _analytics;
  late FirebaseCrashlytics _crashlytics;

  void initialize() {
    _analytics = FirebaseAnalytics.instance;
    _crashlytics = FirebaseCrashlytics.instance;
    
    // Set up crash reporting
    FlutterError.onError = _crashlytics.recordFlutterFatalError;
    PlatformDispatcher.instance.onError = (error, stack) {
      _crashlytics.recordError(error, stack, fatal: true);
      return true;
    };
  }

  // User Analytics
  Future<void> setUserId(String userId) async {
    await _analytics.setUserId(id: userId);
    await _crashlytics.setUserIdentifier(userId);
  }

  Future<void> setUserProperties(UserModel user) async {
    await _analytics.setUserProperty(
      name: 'user_type',
      value: user.userType.name,
    );
    await _analytics.setUserProperty(
      name: 'user_verified',
      value: 'true', // user.isVerified.toString(),
    );
    await _analytics.setUserProperty(
      name: 'user_location',
      value: user.location?.city ?? 'unknown',
    );
    
    // Set crashlytics user info
    await _crashlytics.setCustomKey('user_type', user.userType.name);
    await _crashlytics.setCustomKey('user_verified', true); // user.isVerified
  }

  // Authentication Analytics
  Future<void> logSignUp(String method) async {
    await _analytics.logSignUp(signUpMethod: method);
  }

  Future<void> logLogin(String method) async {
    await _analytics.logLogin(loginMethod: method);
  }

  Future<void> logLogout() async {
    await _analytics.logEvent(name: 'logout');
  }

  // Service Discovery Analytics
  Future<void> logSearch(String searchTerm, String? category) async {
    await _analytics.logSearch(
      searchTerm: searchTerm,
      parameters: {
        'category': category ?? 'all',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  Future<void> logViewService(ServiceModel service) async {
    await _analytics.logEvent(
      name: 'view_item',
      parameters: {
        'item_name': service.name,
        'item_category': service.category,
        'service_price': service.price,
        'service_duration': service.durationMinutes,
        'service_popular': service.isPopular,
      },
    );
  }

  Future<void> logViewProvider(String providerId, String providerName) async {
    await _analytics.logEvent(
      name: 'view_provider',
      parameters: {
        'provider_id': providerId,
        'provider_name': providerName,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  // Booking Analytics
  Future<void> logBookingStarted(ServiceModel service, String providerId) async {
    await logCustomEvent('booking_started', {
      'service_id': service.id,
      'service_name': service.name,
      'service_price': service.price,
      'provider_id': providerId,
    });
  }

  Future<void> logBookingCompleted(BookingModel booking) async {
    await logCustomEvent('booking_completed', {
      'booking_id': booking.id,
      'service_id': booking.serviceId,
      'provider_id': booking.providerId,
      'total_amount': booking.totalAmount,
      'status': booking.status.name,
    });
  }

  Future<void> logBookingCancelled(String bookingId, String reason) async {
    await _analytics.logEvent(
      name: 'booking_cancelled',
      parameters: {
        'booking_id': bookingId,
        'cancellation_reason': reason,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  // Payment Analytics
  Future<void> logPaymentStarted(PaymentModel payment) async {
    await _analytics.logEvent(
      name: 'payment_started',
      parameters: {
        'payment_method': payment.paymentMethod.name,
        'amount': payment.amount,
        'currency': payment.currency,
        'booking_id': payment.bookingId,
      },
    );
  }

  Future<void> logPaymentCompleted(PaymentModel payment) async {
    await _analytics.logEvent(
      name: 'payment_completed',
      parameters: {
        'payment_id': payment.id,
        'payment_method': payment.paymentMethod.name,
        'amount': payment.amount,
        'currency': payment.currency,
        'transaction_id': payment.transactionId ?? '',
        // 'processing_time': payment.processedAt?.difference(payment.createdAt).inSeconds,
      },
    );
  }

  Future<void> logPaymentFailed(PaymentModel payment, String error) async {
    await _analytics.logEvent(
      name: 'payment_failed',
      parameters: {
        'payment_method': payment.paymentMethod.name,
        'amount': payment.amount,
        'currency': payment.currency,
        'error_message': error,
        'booking_id': payment.bookingId,
      },
    );
  }

  // User Engagement Analytics
  Future<void> logScreenView(String screenName, String screenClass) async {
    await _analytics.logScreenView(
      screenName: screenName,
      screenClass: screenClass,
    );
  }

  Future<void> logAppOpen() async {
    await _analytics.logAppOpen();
  }

  Future<void> logShare(String contentType, String itemId) async {
    await logCustomEvent('share', {
      'content_type': contentType,
      'item_id': itemId,
    });
  }

  Future<void> logRating(String itemId, double rating, String itemType) async {
    await _analytics.logEvent(
      name: 'rate_item',
      parameters: {
        'item_id': itemId,
        'item_type': itemType,
        'rating': rating,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  // Custom Events
  Future<void> logCustomEvent(String eventName, Map<String, dynamic> parameters) async {
    await _analytics.logEvent(
      name: eventName,
      parameters: parameters.cast<String, Object>(),
    );
  }

  // Error Tracking
  Future<void> recordError(dynamic error, StackTrace? stackTrace, {
    bool fatal = false,
    Map<String, dynamic>? customKeys,
  }) async {
    if (customKeys != null) {
      for (final entry in customKeys.entries) {
        await _crashlytics.setCustomKey(entry.key, entry.value);
      }
    }
    
    await _crashlytics.recordError(
      error,
      stackTrace,
      fatal: fatal,
    );
  }

  Future<void> log(String message) async {
    await _crashlytics.log(message);
  }

  // Performance Monitoring
  Future<T> trackPerformance<T>(String traceName, Future<T> Function() operation) async {
    final startTime = DateTime.now();
    try {
      final result = await operation();
      final duration = DateTime.now().difference(startTime);
      await logCustomEvent('performance_trace', {
        'trace_name': traceName,
        'duration_ms': duration.inMilliseconds,
        'success': true,
      });
      return result;
    } catch (error, stackTrace) {
      final duration = DateTime.now().difference(startTime);
      await logCustomEvent('performance_trace', {
        'trace_name': traceName,
        'duration_ms': duration.inMilliseconds,
        'success': false,
        'error': error.toString(),
      });
      await recordError(error, stackTrace);
      rethrow;
    }
  }

  // User Journey Analytics
  Future<void> logUserJourney(String step, Map<String, dynamic> data) async {
    await _analytics.logEvent(
      name: 'user_journey',
      parameters: {
        'journey_step': step,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        ...data,
      },
    );
  }

  // Business Metrics
  Future<void> logRevenue(double amount, String currency, String source) async {
    await _analytics.logEvent(
      name: 'revenue_generated',
      parameters: {
        'amount': amount,
        'currency': currency,
        'source': source,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  Future<void> logRetention(int daysSinceInstall) async {
    await _analytics.logEvent(
      name: 'user_retention',
      parameters: {
        'days_since_install': daysSinceInstall,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  // A/B Testing Support
  Future<void> logExperiment(String experimentId, String variant) async {
    await _analytics.logEvent(
      name: 'experiment_exposure',
      parameters: {
        'experiment_id': experimentId,
        'variant': variant,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  // Conversion Funnel
  Future<void> logFunnelStep(String funnelName, String step, Map<String, dynamic>? data) async {
    await _analytics.logEvent(
      name: 'funnel_step',
      parameters: {
        'funnel_name': funnelName,
        'step': step,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (data != null) ...data,
      },
    );
  }

  // Session Analytics
  Future<void> logSessionStart() async {
    await _analytics.logEvent(
      name: 'session_start',
      parameters: {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  Future<void> logSessionEnd(Duration sessionDuration) async {
    await _analytics.logEvent(
      name: 'session_end',
      parameters: {
        'session_duration_seconds': sessionDuration.inSeconds,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }
}

// Analytics Event Item for structured data
class AnalyticsEventItem {
  final String itemId;
  final String itemName;
  final String itemCategory;
  final double price;
  final int quantity;

  AnalyticsEventItem({
    required this.itemId,
    required this.itemName,
    required this.itemCategory,
    required this.price,
    this.quantity = 1,
  });

  Map<String, dynamic> toMap() {
    return {
      'item_id': itemId,
      'item_name': itemName,
      'item_category': itemCategory,
      'price': price,
      'quantity': quantity,
    };
  }
}
