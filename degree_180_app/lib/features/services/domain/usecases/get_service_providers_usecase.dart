import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/models/location_model.dart';
import '../../../../core/enums/service_type.dart';
import '../repositories/service_provider_repository.dart';

class GetServiceProvidersUseCase implements UseCase<List<ServiceProviderModel>, GetServiceProvidersParams> {
  final ServiceProviderRepository repository;

  GetServiceProvidersUseCase(this.repository);

  @override
  Future<Either<Failure, List<ServiceProviderModel>>> call(GetServiceProvidersParams params) async {
    return await repository.getServiceProviders(
      serviceType: params.serviceType,
      userLocation: params.userLocation,
      radiusKm: params.radiusKm,
      limit: params.limit,
      lastDocumentId: params.lastDocumentId,
      isVerified: params.isVerified,
      isOnline: params.isOnline,
      minRating: params.minRating,
    );
  }
}

class GetServiceProvidersParams {
  final ServiceType? serviceType;
  final LocationModel? userLocation;
  final double? radiusKm;
  final int? limit;
  final String? lastDocumentId;
  final bool? isVerified;
  final bool? isOnline;
  final double? minRating;

  const GetServiceProvidersParams({
    this.serviceType,
    this.userLocation,
    this.radiusKm,
    this.limit,
    this.lastDocumentId,
    this.isVerified,
    this.isOnline,
    this.minRating,
  });
}
