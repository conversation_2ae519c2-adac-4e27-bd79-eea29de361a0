import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/models/booking_statistics.dart';
import '../../../../core/enums/booking_status.dart';

abstract class BookingRepository {
  /// Create a new booking
  Future<Either<Failure, BookingModel>> createBooking(
    CreateBookingParams params,
  );

  /// Get user's bookings
  Future<Either<Failure, List<BookingModel>>> getUserBookings(
    String userId, {
    BookingStatus? status,
    int? limit,
    String? lastDocumentId,
  });

  /// Get provider's bookings
  Future<Either<Failure, List<BookingModel>>> getProviderBookings(
    String providerId, {
    BookingStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    String? lastDocumentId,
  });

  /// Get booking by ID
  Future<Either<Failure, BookingModel>> getBookingById(
    String bookingId,
  );

  /// Update booking status
  Future<Either<Failure, BookingModel>> updateBookingStatus(
    String bookingId,
    BookingStatus status, {
    String? reason,
    String? updatedBy,
  });

  /// Cancel booking
  Future<Either<Failure, BookingModel>> cancelBooking(
    String bookingId,
    String reason,
    String cancelledBy,
  );

  /// Reschedule booking
  Future<Either<Failure, BookingModel>> rescheduleBooking(
    String bookingId,
    DateTime newDate,
    String newTimeSlot,
    String? reason,
  );

  /// Get available time slots for a provider on a specific date
  Future<Either<Failure, List<TimeSlotModel>>> getAvailableTimeSlots(
    String providerId,
    DateTime date,
    int serviceDurationMinutes,
  );

  /// Get booking statistics for provider
  Future<Either<Failure, BookingStatistics>> getBookingStatistics(
    String providerId, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get upcoming bookings for reminders
  Future<Either<Failure, List<BookingModel>>> getUpcomingBookings(
    DateTime reminderTime,
  );

  /// Mark reminder as sent
  Future<Either<Failure, void>> markReminderSent(
    String bookingId,
  );

  /// Get booking history for analytics
  Future<Either<Failure, List<BookingModel>>> getBookingHistory(
    String userId, {
    int? limit,
    String? lastDocumentId,
  });
}

class CreateBookingParams {
  final String customerId;
  final String customerName;
  final String? customerPhone;
  final String? customerEmail;
  final String providerId;
  final String providerName;
  final String serviceId;
  final DateTime bookingDate;
  final String timeSlot;
  final double totalAmount;
  final String currency;
  final double? discountAmount;
  final String? couponCode;
  final String? notes;
  final Map<String, dynamic>? paymentInfo;

  const CreateBookingParams({
    required this.customerId,
    required this.customerName,
    this.customerPhone,
    this.customerEmail,
    required this.providerId,
    required this.providerName,
    required this.serviceId,
    required this.bookingDate,
    required this.timeSlot,
    required this.totalAmount,
    this.currency = 'KD',
    this.discountAmount,
    this.couponCode,
    this.notes,
    this.paymentInfo,
  });
}


