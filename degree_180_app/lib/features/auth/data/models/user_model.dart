import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/user_entity.dart';

class UserModel extends UserEntity {
  const UserModel({
    required super.id,
    required super.uid,
    required super.email,
    required super.name,
    super.phoneNumber,
    super.profileImageUrl,
    super.photoURL,
    required super.userType,
    super.serviceType,
    super.rating,
    super.reviewCount,
    super.isActive,
    super.isVerified,
    required super.createdAt,
    required super.updatedAt,
    super.additionalData,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      uid: json['uid'] ?? json['id'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      phoneNumber: json['phoneNumber'],
      profileImageUrl: json['profileImageUrl'],
      photoURL: json['photoURL'] ?? json['profileImageUrl'],
      userType: _parseUserType(json['userType']),
      serviceType: _parseServiceType(json['serviceType']),
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      isActive: json['isActive'] ?? true,
      isVerified: json['isVerified'] ?? false,
      createdAt: _parseDateTime(json['createdAt']),
      updatedAt: _parseDateTime(json['updatedAt']),
      additionalData: json['additionalData'],
    );
  }

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel.fromJson({
      'id': doc.id,
      ...data,
    });
  }

  factory UserModel.fromEntity(UserEntity entity) {
    return UserModel(
      id: entity.id,
      uid: entity.uid,
      email: entity.email,
      name: entity.name,
      phoneNumber: entity.phoneNumber,
      profileImageUrl: entity.profileImageUrl,
      photoURL: entity.photoURL,
      userType: entity.userType,
      serviceType: entity.serviceType,
      rating: entity.rating,
      reviewCount: entity.reviewCount,
      isActive: entity.isActive,
      isVerified: entity.isVerified,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      additionalData: entity.additionalData,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uid': uid,
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'profileImageUrl': profileImageUrl,
      'photoURL': photoURL,
      'userType': userTypeString,
      'serviceType': serviceTypeString,
      'rating': rating,
      'reviewCount': reviewCount,
      'isActive': isActive,
      'isVerified': isVerified,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalData': additionalData,
    };
  }

  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id'); // Remove id as it's handled by Firestore document ID
    return json;
  }

  static UserType _parseUserType(String? userType) {
    switch (userType?.toLowerCase()) {
      case 'worker':
        return UserType.worker;
      case 'freelancer':
        return UserType.freelancer;
      case 'customer':
      default:
        return UserType.customer;
    }
  }

  static ServiceType? _parseServiceType(String? serviceType) {
    switch (serviceType?.toLowerCase()) {
      case 'salon':
        return ServiceType.salon;
      case 'barbershop':
        return ServiceType.barbershop;
      case 'spa':
        return ServiceType.spa;
      default:
        return null;
    }
  }

  static DateTime _parseDateTime(dynamic dateTime) {
    if (dateTime == null) return DateTime.now();
    if (dateTime is Timestamp) return dateTime.toDate();
    if (dateTime is String) return DateTime.parse(dateTime);
    if (dateTime is DateTime) return dateTime;
    return DateTime.now();
  }

  @override
  UserModel copyWith({
    String? id,
    String? uid,
    String? email,
    String? name,
    String? phoneNumber,
    String? profileImageUrl,
    String? photoURL,
    UserType? userType,
    ServiceType? serviceType,
    double? rating,
    int? reviewCount,
    bool? isActive,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return UserModel(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      photoURL: photoURL ?? this.photoURL,
      userType: userType ?? this.userType,
      serviceType: serviceType ?? this.serviceType,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}
