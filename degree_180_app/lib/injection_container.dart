import 'package:get_it/get_it.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';

// Core
import 'core/network/network_info.dart';
import 'core/security/security_manager.dart';
import 'core/performance/performance_manager.dart';
import 'core/network/enhanced_network_manager.dart';
import 'core/services/theme_service.dart';
import 'core/services/analytics_service.dart';
import 'core/services/cache_service.dart';
import 'core/services/connectivity_service.dart';
import 'core/services/error_service.dart';

// Auth
import 'features/auth/domain/repositories/auth_repository.dart';
import 'features/auth/domain/usecases/sign_in_with_email_usecase.dart';
import 'features/auth/domain/usecases/sign_up_with_email_usecase.dart';
import 'features/auth/domain/usecases/sign_in_with_google_usecase.dart';
import 'features/auth/domain/usecases/sign_out_usecase.dart';
import 'features/auth/domain/usecases/get_current_user_usecase.dart';
import 'features/auth/data/repositories/auth_repository_impl.dart';
import 'features/auth/data/datasources/auth_remote_data_source.dart';

// Services
import 'features/services/domain/repositories/service_provider_repository.dart';
import 'features/services/domain/usecases/get_service_providers_usecase.dart';
import 'features/services/domain/usecases/get_service_provider_details_usecase.dart';
import 'features/services/data/repositories/service_provider_repository_impl.dart';
import 'features/services/data/datasources/service_provider_remote_data_source.dart';

// Booking
import 'features/booking/domain/repositories/booking_repository.dart';
import 'features/booking/domain/usecases/get_available_time_slots_usecase.dart';
import 'features/booking/domain/usecases/create_advanced_booking_usecase.dart';
import 'features/booking/domain/usecases/reschedule_booking_usecase.dart';
import 'features/booking/domain/usecases/get_booking_statistics_usecase.dart';
import 'features/booking/data/repositories/booking_repository_impl.dart';
import 'features/booking/data/datasources/booking_remote_data_source.dart';

final sl = GetIt.instance;

Future<void> init() async {
  //! Features - Auth
  // Use cases
  sl.registerLazySingleton(() => SignInWithEmailUseCase(sl()));
  sl.registerLazySingleton(() => SignUpWithEmailUseCase(sl()));
  sl.registerLazySingleton(() => SignInWithGoogleUseCase(sl()));
  sl.registerLazySingleton(() => SignOutUseCase(sl()));
  sl.registerLazySingleton(() => GetCurrentUserUseCase(sl()));

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      firebaseAuth: sl(),
      firestore: sl(),
      googleSignIn: sl(),
    ),
  );

  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  sl.registerLazySingleton(() => FirebaseAuth.instance);
  sl.registerLazySingleton(() => FirebaseFirestore.instance);
  sl.registerLazySingleton(() => FirebaseStorage.instance);
  sl.registerLazySingleton(() => GoogleSignIn());
  sl.registerLazySingleton(() => Connectivity());
  sl.registerLazySingleton(() => Dio());

  //! Core
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(sl()));
  sl.registerLazySingleton(() => SecurityManager.instance);
  sl.registerLazySingleton(() => PerformanceManager.instance);
  sl.registerLazySingleton(() => EnhancedNetworkManager.instance);

  //! Core Services
  sl.registerLazySingleton(() => ThemeService());
  sl.registerLazySingleton(() => AnalyticsService());

  // Initialize CacheService
  final cacheService = CacheService();
  await cacheService.initialize();
  sl.registerLazySingleton(() => cacheService);

  sl.registerLazySingleton(() => ConnectivityService());
  sl.registerLazySingleton(() => ErrorService());

  //! Services
  // Data sources
  sl.registerLazySingleton<ServiceProviderRemoteDataSource>(
    () => ServiceProviderRemoteDataSourceImpl(
      firestore: sl(),
      storage: sl(),
    ),
  );

  // Repository
  sl.registerLazySingleton<ServiceProviderRepository>(
    () => ServiceProviderRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetServiceProvidersUseCase(sl()));
  sl.registerLazySingleton(() => GetServiceProviderDetailsUseCase(sl()));

  //! Booking
  // Data sources
  sl.registerLazySingleton<BookingRemoteDataSource>(
    () => BookingRemoteDataSourceImpl(
      firestore: sl(),
    ),
  );

  // Repository
  sl.registerLazySingleton<BookingRepository>(
    () => BookingRepositoryImpl(
      remoteDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => GetAvailableTimeSlotsUseCase(sl()));
  sl.registerLazySingleton(() => CreateAdvancedBookingUseCase(sl()));
  sl.registerLazySingleton(() => RescheduleBookingUseCase(sl()));
  sl.registerLazySingleton(() => GetBookingStatisticsUseCase(sl()));
}
