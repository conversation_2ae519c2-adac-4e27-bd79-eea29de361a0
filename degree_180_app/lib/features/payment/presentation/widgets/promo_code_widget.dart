import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';

class PromoCodeWidget extends StatefulWidget {
  final Function(String?, double) onPromoApplied;
  final double orderAmount;

  const PromoCodeWidget({
    super.key,
    required this.onPromoApplied,
    this.orderAmount = 0.0,
  });

  @override
  State<PromoCodeWidget> createState() => _PromoCodeWidgetState();
}

class _PromoCodeWidgetState extends State<PromoCodeWidget>
    with SingleTickerProviderStateMixin {
  final _promoController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  
  bool _isExpanded = false;
  bool _isLoading = false;
  String? _appliedPromo;
  double _discount = 0.0;
  String? _errorMessage;

  // Mock promo codes for demonstration
  final Map<String, PromoCode> _mockPromoCodes = {
    'WELCOME10': PromoCode(
      code: 'WELCOME10',
      discountType: DiscountType.percentage,
      discountValue: 10.0,
      minimumAmount: 20.0,
      description: '10% off for new customers',
      isActive: true,
    ),
    'SAVE5': PromoCode(
      code: 'SAVE5',
      discountType: DiscountType.fixed,
      discountValue: 5.0,
      minimumAmount: 15.0,
      description: '5 KWD off your order',
      isActive: true,
    ),
    'EXPIRED': PromoCode(
      code: 'EXPIRED',
      discountType: DiscountType.percentage,
      discountValue: 20.0,
      minimumAmount: 0.0,
      description: 'Expired promo code',
      isActive: false,
    ),
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _promoController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            GestureDetector(
              onTap: _toggleExpanded,
              child: Row(
                children: [
                  Icon(
                    Icons.local_offer,
                    color: AppColors.primaryPurple,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _appliedPromo != null 
                              ? 'Promo Applied: $_appliedPromo'
                              : 'Have a promo code?',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _appliedPromo != null 
                                ? AppColors.success 
                                : AppColors.textDark,
                          ),
                        ),
                        if (_appliedPromo != null)
                          Text(
                            'You saved ${_discount.toStringAsFixed(3)} KWD',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.success,
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (_appliedPromo != null)
                    GestureDetector(
                      onTap: _removePromo,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppColors.error.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Icon(
                          Icons.close,
                          color: AppColors.error,
                          size: 16,
                        ),
                      ),
                    )
                  else
                    Icon(
                      _isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: AppColors.primaryPurple,
                    ),
                ],
              ),
            ),
            
            // Expandable content
            AnimatedBuilder(
              animation: _slideAnimation,
              builder: (context, child) {
                return ClipRect(
                  child: Align(
                    alignment: Alignment.topCenter,
                    heightFactor: _slideAnimation.value,
                    child: child,
                  ),
                );
              },
              child: _appliedPromo == null ? _buildPromoInput() : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromoInput() {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Input field
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _promoController,
                  decoration: InputDecoration(
                    hintText: 'Enter promo code',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: AppColors.borderGray),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppColors.primaryPurple, width: 2),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppColors.error, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    errorText: _errorMessage,
                  ),
                  textCapitalization: TextCapitalization.characters,
                  onChanged: (value) {
                    if (_errorMessage != null) {
                      setState(() {
                        _errorMessage = null;
                      });
                    }
                  },
                  onSubmitted: (_) => _applyPromo(),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Apply button
              ElevatedButton(
                onPressed: _isLoading ? null : _applyPromo,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryPurple,
                  foregroundColor: AppColors.textWhite,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.textWhite),
                        ),
                      )
                    : const Text('Apply'),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Available promo codes hint
          _buildAvailablePromosHint(),
        ],
      ),
    );
  }

  Widget _buildAvailablePromosHint() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.primaryPurple.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.primaryPurple.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: AppColors.primaryPurple,
              ),
              const SizedBox(width: 8),
              Text(
                'Available Offers',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryPurple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...(_getValidPromoCodes().take(2).map((promo) => 
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Text(
                    '• ${promo.code}:',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryPurple,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      promo.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textLight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )),
        ],
      ),
    );
  }

  void _toggleExpanded() {
    if (_appliedPromo != null) return;
    
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  Future<void> _applyPromo() async {
    final code = _promoController.text.trim().toUpperCase();
    
    if (code.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a promo code';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    final promoCode = _mockPromoCodes[code];
    
    if (promoCode == null) {
      setState(() {
        _errorMessage = 'Invalid promo code';
        _isLoading = false;
      });
      return;
    }

    if (!promoCode.isActive) {
      setState(() {
        _errorMessage = 'This promo code has expired';
        _isLoading = false;
      });
      return;
    }

    if (widget.orderAmount < promoCode.minimumAmount) {
      setState(() {
        _errorMessage = 'Minimum order amount is ${promoCode.minimumAmount.toStringAsFixed(3)} KWD';
        _isLoading = false;
      });
      return;
    }

    // Calculate discount
    double discount = 0.0;
    if (promoCode.discountType == DiscountType.percentage) {
      discount = widget.orderAmount * (promoCode.discountValue / 100);
    } else {
      discount = promoCode.discountValue;
    }

    setState(() {
      _appliedPromo = code;
      _discount = discount;
      _isLoading = false;
      _isExpanded = false;
    });

    _animationController.reverse();
    widget.onPromoApplied(code, discount);

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Promo code applied! You saved ${discount.toStringAsFixed(3)} KWD'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _removePromo() {
    setState(() {
      _appliedPromo = null;
      _discount = 0.0;
      _promoController.clear();
    });
    
    widget.onPromoApplied(null, 0.0);
  }

  List<PromoCode> _getValidPromoCodes() {
    return _mockPromoCodes.values
        .where((promo) => promo.isActive && widget.orderAmount >= promo.minimumAmount)
        .toList();
  }
}

// Promo code model
class PromoCode {
  final String code;
  final DiscountType discountType;
  final double discountValue;
  final double minimumAmount;
  final String description;
  final bool isActive;
  final DateTime? expiryDate;

  const PromoCode({
    required this.code,
    required this.discountType,
    required this.discountValue,
    required this.minimumAmount,
    required this.description,
    required this.isActive,
    this.expiryDate,
  });
}

enum DiscountType {
  percentage,
  fixed,
}

// Simple promo code input widget
class SimplePromoCodeWidget extends StatefulWidget {
  final Function(String?) onPromoChanged;
  final String? initialPromo;

  const SimplePromoCodeWidget({
    super.key,
    required this.onPromoChanged,
    this.initialPromo,
  });

  @override
  State<SimplePromoCodeWidget> createState() => _SimplePromoCodeWidgetState();
}

class _SimplePromoCodeWidgetState extends State<SimplePromoCodeWidget> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialPromo);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      decoration: InputDecoration(
        labelText: 'Promo Code (Optional)',
        hintText: 'Enter promo code',
        prefixIcon: const Icon(Icons.local_offer),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primaryPurple, width: 2),
        ),
      ),
      textCapitalization: TextCapitalization.characters,
      onChanged: widget.onPromoChanged,
    );
  }
}
