import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class MapSearchBar extends StatelessWidget {
  final Function(String) onSearch;
  final VoidCallback onFilterPressed;

  const MapSearchBar({
    super.key,
    required this.onSearch,
    required this.onFilterPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        onChanged: onSearch,
        decoration: InputDecoration(
          hintText: 'Search for services, locations...',
          hintStyle: TextStyle(
            color: AppColors.textGray.withValues(alpha: 0.7),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.textGray.withValues(alpha: 0.7),
          ),
          suffixIcon: IconButton(
            onPressed: onFilterPressed,
            icon: Icon(
              Icons.tune,
              color: AppColors.primaryPurple,
            ),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.defaultPadding,
          ),
        ),
      ),
    );
  }
}
