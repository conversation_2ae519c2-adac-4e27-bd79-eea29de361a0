enum ServiceType {
  salon,
  barbershop,
  spa,
  nails,
  skincare,
  massage,
  makeup,
  haircare,
  beauty,
  wellness,
  fitness,
}

extension ServiceTypeExtension on ServiceType {
  String get displayName {
    switch (this) {
      case ServiceType.salon:
        return 'صالون';
      case ServiceType.barbershop:
        return 'حلاق';
      case ServiceType.spa:
        return 'سبا';
      case ServiceType.nails:
        return 'أظافر';
      case ServiceType.skincare:
        return 'العناية بالبشرة';
      case ServiceType.massage:
        return 'تدليك';
      case ServiceType.makeup:
        return 'مكياج';
      case ServiceType.haircare:
        return 'العناية بالشعر';
      case ServiceType.beauty:
        return 'تجميل';
      case ServiceType.wellness:
        return 'عافية';
      case ServiceType.fitness:
        return 'لياقة بدنية';
    }
  }

  String get displayNameEn {
    switch (this) {
      case ServiceType.salon:
        return 'Salon';
      case ServiceType.barbershop:
        return 'Barbershop';
      case ServiceType.spa:
        return 'Spa';
      case ServiceType.nails:
        return 'Nails';
      case ServiceType.skincare:
        return 'Skincare';
      case ServiceType.massage:
        return 'Massage';
      case ServiceType.makeup:
        return 'Makeup';
      case ServiceType.haircare:
        return 'Hair Care';
      case ServiceType.beauty:
        return 'Beauty';
      case ServiceType.wellness:
        return 'Wellness';
      case ServiceType.fitness:
        return 'Fitness';
    }
  }

  String get description {
    switch (this) {
      case ServiceType.salon:
        return 'خدمات الشعر والتجميل النسائية';
      case ServiceType.barbershop:
        return 'خدمات الحلاقة الرجالية';
      case ServiceType.spa:
        return 'خدمات الاسترخاء والعلاج';
      case ServiceType.nails:
        return 'العناية بالأظافر والتجميل';
      case ServiceType.skincare:
        return 'العناية بالبشرة والوجه';
      case ServiceType.massage:
        return 'خدمات التدليك والاسترخاء';
      case ServiceType.makeup:
        return 'خدمات المكياج والتجميل';
      case ServiceType.haircare:
        return 'العناية بالشعر والتصفيف';
      case ServiceType.beauty:
        return 'خدمات التجميل العامة';
      case ServiceType.wellness:
        return 'خدمات الصحة والعافية';
      case ServiceType.fitness:
        return 'خدمات اللياقة البدنية والرياضة';
    }
  }

  String get icon {
    switch (this) {
      case ServiceType.salon:
        return '💇‍♀️';
      case ServiceType.barbershop:
        return '💇‍♂️';
      case ServiceType.spa:
        return '🧖‍♀️';
      case ServiceType.nails:
        return '💅';
      case ServiceType.skincare:
        return '🧴';
      case ServiceType.massage:
        return '💆‍♀️';
      case ServiceType.makeup:
        return '💄';
      case ServiceType.haircare:
        return '✂️';
      case ServiceType.beauty:
        return '✨';
      case ServiceType.wellness:
        return '🌿';
      case ServiceType.fitness:
        return '💪';
    }
  }

  static ServiceType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'salon':
      case 'صالون':
        return ServiceType.salon;
      case 'barbershop':
      case 'حلاق':
        return ServiceType.barbershop;
      case 'spa':
      case 'سبا':
        return ServiceType.spa;
      case 'nails':
      case 'أظافر':
        return ServiceType.nails;
      case 'skincare':
      case 'العناية بالبشرة':
        return ServiceType.skincare;
      case 'massage':
      case 'تدليك':
        return ServiceType.massage;
      case 'makeup':
      case 'مكياج':
        return ServiceType.makeup;
      case 'haircare':
      case 'العناية بالشعر':
        return ServiceType.haircare;
      case 'beauty':
      case 'تجميل':
        return ServiceType.beauty;
      case 'wellness':
      case 'عافية':
        return ServiceType.wellness;
      case 'fitness':
      case 'لياقة':
        return ServiceType.fitness;
      default:
        return ServiceType.salon;
    }
  }
}
