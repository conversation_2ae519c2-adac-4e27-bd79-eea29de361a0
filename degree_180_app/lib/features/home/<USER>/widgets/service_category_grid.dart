import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/service_type.dart';

class ServiceCategoryGrid extends StatelessWidget {
  const ServiceCategoryGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Browse Categories',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 0.8,
          ),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            return _CategoryItem(
              category: category,
              onTap: () => _navigateToCategory(context, category.serviceType),
            );
          },
        ),
      ],
    );
  }

  void _navigateToCategory(BuildContext context, ServiceType serviceType) {
    Navigator.pushNamed(
      context,
      '/providers',
      arguments: {
        'serviceType': serviceType,
      },
    );
  }
}

class _CategoryItem extends StatelessWidget {
  final CategoryData category;
  final VoidCallback onTap;

  const _CategoryItem({
    required this.category,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.backgroundWhite,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          border: Border.all(
            color: AppColors.borderGray,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: category.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                category.icon,
                color: category.color,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              category.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

class CategoryData {
  final String name;
  final IconData icon;
  final Color color;
  final ServiceType serviceType;

  const CategoryData({
    required this.name,
    required this.icon,
    required this.color,
    required this.serviceType,
  });
}

final List<CategoryData> _categories = [
  CategoryData(
    name: 'Hair Salon',
    icon: Icons.content_cut,
    color: AppColors.primaryPurple,
    serviceType: ServiceType.salon,
  ),
  CategoryData(
    name: 'Barbershop',
    icon: Icons.face_retouching_natural,
    color: AppColors.success,
    serviceType: ServiceType.barbershop,
  ),
  CategoryData(
    name: 'Spa',
    icon: Icons.spa,
    color: AppColors.warning,
    serviceType: ServiceType.spa,
  ),
  CategoryData(
    name: 'Nails',
    icon: Icons.back_hand,
    color: AppColors.error,
    serviceType: ServiceType.nails,
  ),
  CategoryData(
    name: 'Makeup',
    icon: Icons.face,
    color: Colors.pink,
    serviceType: ServiceType.makeup,
  ),
  CategoryData(
    name: 'Massage',
    icon: Icons.healing,
    color: Colors.teal,
    serviceType: ServiceType.massage,
  ),
  CategoryData(
    name: 'Skincare',
    icon: Icons.face_retouching_natural,
    color: Colors.orange,
    serviceType: ServiceType.skincare,
  ),
  CategoryData(
    name: 'Fitness',
    icon: Icons.fitness_center,
    color: Colors.indigo,
    serviceType: ServiceType.fitness,
  ),
];
