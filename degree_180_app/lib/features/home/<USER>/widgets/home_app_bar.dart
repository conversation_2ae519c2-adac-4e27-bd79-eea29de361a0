import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/location_model.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';

class HomeAppBar extends StatelessWidget {
  final LocationModel? userLocation;
  final bool isLoadingLocation;
  final VoidCallback onLocationTap;

  const HomeAppBar({
    super.key,
    this.userLocation,
    required this.isLoadingLocation,
    required this.onLocationTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryPurple,
            AppColors.primaryPurpleLight,
          ],
        ),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Greeting and Location
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            String greeting = 'Hello!';
                            if (state is AuthAuthenticated) {
                              final name = state.user.displayName?.split(' ').first ?? 'User';
                              greeting = 'Hello, $name!';
                            }
                            return Text(
                              greeting,
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: AppColors.textWhite,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 4),
                        GestureDetector(
                          onTap: onLocationTap,
                          child: Row(
                            children: [
                              Icon(
                                Icons.location_on,
                                color: AppColors.textWhite.withValues(alpha: 0.8),
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  _getLocationText(),
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppColors.textWhite.withValues(alpha: 0.8),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isLoadingLocation)
                                Padding(
                                  padding: const EdgeInsets.only(left: 8),
                                  child: SizedBox(
                                    width: 12,
                                    height: 12,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        AppColors.textWhite.withValues(alpha: 0.8),
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Profile and Notifications
                  Row(
                    children: [
                      // Notifications
                      IconButton(
                        onPressed: () => _navigateToNotifications(context),
                        icon: Stack(
                          children: [
                            Icon(
                              Icons.notifications_outlined,
                              color: AppColors.textWhite,
                              size: 24,
                            ),
                            // Notification badge
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  color: AppColors.error,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(width: 8),
                      
                      // Profile
                      BlocBuilder<AuthBloc, AuthState>(
                        builder: (context, state) {
                          return GestureDetector(
                            onTap: () => _navigateToProfile(context),
                            child: CircleAvatar(
                              radius: 20,
                              backgroundColor: AppColors.textWhite.withValues(alpha: 0.2),
                              backgroundImage: state is AuthAuthenticated && state.user.photoURL != null
                                  ? NetworkImage(state.user.photoURL!)
                                  : null,
                              child: state is AuthAuthenticated && state.user.photoURL == null
                                  ? Text(
                                      state.user.displayName?.substring(0, 1).toUpperCase() ?? 'U',
                                      style: TextStyle(
                                        color: AppColors.textWhite,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )
                                  : Icon(
                                      Icons.person,
                                      color: AppColors.textWhite,
                                    ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Welcome message
              Text(
                'Find the perfect beauty service near you',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textWhite.withValues(alpha: 0.9),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getLocationText() {
    if (isLoadingLocation) {
      return 'Getting location...';
    }
    
    if (userLocation != null) {
      return userLocation!.address ?? 'Current Location';
    }
    
    return 'Tap to get location';
  }

  void _navigateToNotifications(BuildContext context) {
    Navigator.pushNamed(context, '/notifications');
  }

  void _navigateToProfile(BuildContext context) {
    Navigator.pushNamed(context, '/profile');
  }
}
