import 'package:equatable/equatable.dart';

enum UserType { customer, worker, freelancer }

enum ServiceType { salon, barbershop, spa, fitness }

class UserEntity extends Equatable {
  final String id;
  final String uid; // Firebase UID
  final String email;
  final String name;
  final String? phoneNumber;
  final String? profileImageUrl;
  final String? photoURL; // Alternative name for profile image
  final UserType userType;
  final ServiceType? serviceType;
  final double rating;
  final int reviewCount;
  final bool isActive;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalData;

  const UserEntity({
    required this.id,
    required this.uid,
    required this.email,
    required this.name,
    this.phoneNumber,
    this.profileImageUrl,
    this.photoURL,
    required this.userType,
    this.serviceType,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isActive = true,
    this.isVerified = false,
    required this.createdAt,
    required this.updatedAt,
    this.additionalData,
  });

  @override
  List<Object?> get props => [
        id,
        uid,
        email,
        name,
        phoneNumber,
        profileImageUrl,
        photoURL,
        userType,
        serviceType,
        rating,
        reviewCount,
        isActive,
        isVerified,
        createdAt,
        updatedAt,
        additionalData,
      ];

  UserEntity copyWith({
    String? id,
    String? uid,
    String? email,
    String? name,
    String? phoneNumber,
    String? profileImageUrl,
    String? photoURL,
    UserType? userType,
    ServiceType? serviceType,
    double? rating,
    int? reviewCount,
    bool? isActive,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return UserEntity(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      photoURL: photoURL ?? this.photoURL,
      userType: userType ?? this.userType,
      serviceType: serviceType ?? this.serviceType,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  bool get isServiceProvider => userType == UserType.worker || userType == UserType.freelancer;
  
  bool get isCustomer => userType == UserType.customer;
  
  String get displayName => name.isNotEmpty ? name : email.split('@').first;
  
  String get userTypeString {
    switch (userType) {
      case UserType.customer:
        return 'customer';
      case UserType.worker:
        return 'worker';
      case UserType.freelancer:
        return 'freelancer';
    }
  }
  
  String? get serviceTypeString {
    if (serviceType == null) return null;
    switch (serviceType!) {
      case ServiceType.salon:
        return 'salon';
      case ServiceType.barbershop:
        return 'barbershop';
      case ServiceType.spa:
        return 'spa';
      case ServiceType.fitness:
        return 'fitness';
    }
  }
}
