import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import '../lib/core/enums/gender.dart';
import '../lib/core/services/theme_service.dart';
import '../lib/core/theme/app_theme.dart';

void main() {
  group('Theme System Tests', () {
    late ThemeService themeService;

    setUp(() {
      themeService = ThemeService();
    });

    test('should initialize with default values', () {
      expect(themeService.currentGender, Gender.notSpecified);
      expect(themeService.isDarkMode, false);
      expect(themeService.currentTheme, isA<ThemeData>());
    });

    test('should change gender and update theme', () async {
      // Test female theme
      await themeService.setGender(Gender.female);
      expect(themeService.currentGender, Gender.female);
      expect(themeService.isFemaleTheme, true);
      expect(themeService.isMaleTheme, false);
      expect(themeService.primaryColor, const Color(0xFFE91E63));

      // Test male theme
      await themeService.setGender(Gender.male);
      expect(themeService.currentGender, Gender.male);
      expect(themeService.isFemaleTheme, false);
      expect(themeService.isMaleTheme, true);
      expect(themeService.primaryColor, const Color(0xFF6C63FF));
    });

    test('should toggle dark mode', () async {
      expect(themeService.isDarkMode, false);
      
      await themeService.toggleDarkMode();
      expect(themeService.isDarkMode, true);
      
      await themeService.toggleDarkMode();
      expect(themeService.isDarkMode, false);
    });

    test('should provide correct gradient colors for each gender', () {
      // Test male gradient
      themeService.setGender(Gender.male);
      final maleGradient = themeService.gradientColors;
      expect(maleGradient.length, 2);
      expect(maleGradient.first, const Color(0xFF6C63FF));
      expect(maleGradient.last, const Color(0xFF9C88FF));

      // Test female gradient
      themeService.setGender(Gender.female);
      final femaleGradient = themeService.gradientColors;
      expect(femaleGradient.length, 2);
      expect(femaleGradient.first, const Color(0xFFE91E63));
      expect(femaleGradient.last, const Color(0xFFF8BBD9));
    });

    test('should provide correct theme for gender', () {
      final maleTheme = AppTheme.getThemeForGender(Gender.male);
      final femaleTheme = AppTheme.getThemeForGender(Gender.female);
      final maleDarkTheme = AppTheme.getThemeForGender(Gender.male, isDark: true);
      final femaleDarkTheme = AppTheme.getThemeForGender(Gender.female, isDark: true);

      expect(maleTheme.primaryColor, const Color(0xFF6C63FF));
      expect(femaleTheme.primaryColor, const Color(0xFFE91E63));
      expect(maleDarkTheme.brightness, Brightness.dark);
      expect(femaleDarkTheme.brightness, Brightness.dark);
    });

    test('should provide correct gender icon', () {
      themeService.setGender(Gender.male);
      expect(themeService.getGenderIcon(), Icons.male);

      themeService.setGender(Gender.female);
      expect(themeService.getGenderIcon(), Icons.female);

      themeService.setGender(Gender.notSpecified);
      expect(themeService.getGenderIcon(), Icons.person);
    });

    test('should provide themed decorations', () {
      themeService.setGender(Gender.female);
      
      final decoration = themeService.getPrimaryGradientDecoration();
      expect(decoration.gradient, isA<LinearGradient>());
      
      final buttonStyle = themeService.getPrimaryButtonStyle();
      expect(buttonStyle, isA<ButtonStyle>());
      
      final cardDecoration = themeService.getCardDecoration();
      expect(cardDecoration, isA<BoxDecoration>());
      
      final inputDecoration = themeService.getInputDecoration(labelText: 'Test');
      expect(inputDecoration.labelText, 'Test');
    });

    test('should reset to default theme', () async {
      // Change theme first
      await themeService.setGender(Gender.female);
      await themeService.setDarkMode(true);
      
      expect(themeService.currentGender, Gender.female);
      expect(themeService.isDarkMode, true);
      
      // Reset to default
      await themeService.resetToDefault();
      
      expect(themeService.currentGender, Gender.notSpecified);
      expect(themeService.isDarkMode, false);
    });

    test('should auto-detect theme based on gender', () async {
      await themeService.autoDetectTheme(Gender.female);
      expect(themeService.currentGender, Gender.female);
      expect(themeService.isFemaleTheme, true);
    });

    test('should provide correct theme name', () {
      themeService.setGender(Gender.male);
      expect(themeService.getThemeName(), contains('ذكوري'));
      
      themeService.setGender(Gender.female);
      expect(themeService.getThemeName(), contains('أنثوي'));
      
      themeService.setGender(Gender.notSpecified);
      expect(themeService.getThemeName(), contains('افتراضي'));
    });

    test('should check theme suitability for gender', () {
      themeService.setGender(Gender.female);
      expect(themeService.isThemeSuitableForGender(Gender.female), true);
      expect(themeService.isThemeSuitableForGender(Gender.male), false);
      
      themeService.setGender(Gender.notSpecified);
      expect(themeService.isThemeSuitableForGender(Gender.female), true);
      expect(themeService.isThemeSuitableForGender(Gender.male), true);
    });
  });

  group('Gender Enum Tests', () {
    test('should provide correct display names', () {
      expect(Gender.male.displayName, 'ذكر');
      expect(Gender.female.displayName, 'أنثى');
      expect(Gender.other.displayName, 'آخر');
      expect(Gender.notSpecified.displayName, 'غير محدد');
    });

    test('should provide correct English display names', () {
      expect(Gender.male.displayNameEn, 'Male');
      expect(Gender.female.displayNameEn, 'Female');
      expect(Gender.other.displayNameEn, 'Other');
      expect(Gender.notSpecified.displayNameEn, 'Not Specified');
    });

    test('should provide correct codes', () {
      expect(Gender.male.code, 'M');
      expect(Gender.female.code, 'F');
      expect(Gender.other.code, 'O');
      expect(Gender.notSpecified.code, 'N');
    });

    test('should create gender from code', () {
      expect(GenderExtension.fromCode('M'), Gender.male);
      expect(GenderExtension.fromCode('F'), Gender.female);
      expect(GenderExtension.fromCode('O'), Gender.other);
      expect(GenderExtension.fromCode('X'), Gender.notSpecified);
    });

    test('should create gender from string', () {
      expect(GenderExtension.fromString('male'), Gender.male);
      expect(GenderExtension.fromString('ذكر'), Gender.male);
      expect(GenderExtension.fromString('female'), Gender.female);
      expect(GenderExtension.fromString('أنثى'), Gender.female);
      expect(GenderExtension.fromString('unknown'), Gender.notSpecified);
    });
  });

  group('Theme Integration Tests', () {
    testWidgets('should apply theme correctly in widget tree', (WidgetTester tester) async {
      final themeService = ThemeService();
      await themeService.setGender(Gender.female);

      await tester.pumpWidget(
        MaterialApp(
          theme: themeService.currentTheme,
          home: Scaffold(
            appBar: AppBar(title: const Text('Test')),
            body: const Center(child: Text('Theme Test')),
          ),
        ),
      );

      // Verify theme is applied
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.backgroundColor, const Color(0xFFE91E63));
    });

    testWidgets('should update UI when theme changes', (WidgetTester tester) async {
      final themeService = ThemeService();
      
      await tester.pumpWidget(
        MaterialApp(
          theme: themeService.currentTheme,
          home: ListenableBuilder(
            listenable: themeService,
            builder: (context, child) {
              return Scaffold(
                backgroundColor: themeService.primaryColor,
                body: const Center(child: Text('Dynamic Theme')),
              );
            },
          ),
        ),
      );

      // Initial theme (male)
      await themeService.setGender(Gender.male);
      await tester.pump();
      
      Scaffold scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, const Color(0xFF6C63FF));

      // Change to female theme
      await themeService.setGender(Gender.female);
      await tester.pump();
      
      scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, const Color(0xFFE91E63));
    });
  });
}
