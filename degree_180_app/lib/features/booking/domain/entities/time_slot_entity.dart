import 'package:equatable/equatable.dart';

enum SlotStatus {
  available,
  booked,
  blocked,
  break_time,
}

class TimeSlotEntity extends Equatable {
  final String id;
  final String providerId;
  final DateTime date;
  final DateTime startTime;
  final DateTime endTime;
  final SlotStatus status;
  final String? bookingId;
  final double? price;
  final bool isRecurring;
  final List<int>? recurringDays; // 1-7 for Monday-Sunday
  final DateTime? recurringEndDate;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TimeSlotEntity({
    required this.id,
    required this.providerId,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.status,
    this.bookingId,
    this.price,
    this.isRecurring = false,
    this.recurringDays,
    this.recurringEndDate,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        providerId,
        date,
        startTime,
        endTime,
        status,
        bookingId,
        price,
        isRecurring,
        recurringDays,
        recurringEndDate,
        notes,
        createdAt,
        updatedAt,
      ];

  TimeSlotEntity copyWith({
    String? id,
    String? providerId,
    DateTime? date,
    DateTime? startTime,
    DateTime? endTime,
    SlotStatus? status,
    String? bookingId,
    double? price,
    bool? isRecurring,
    List<int>? recurringDays,
    DateTime? recurringEndDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TimeSlotEntity(
      id: id ?? this.id,
      providerId: providerId ?? this.providerId,
      date: date ?? this.date,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      bookingId: bookingId ?? this.bookingId,
      price: price ?? this.price,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringDays: recurringDays ?? this.recurringDays,
      recurringEndDate: recurringEndDate ?? this.recurringEndDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Duration get duration => endTime.difference(startTime);

  String get statusString {
    switch (status) {
      case SlotStatus.available:
        return 'available';
      case SlotStatus.booked:
        return 'booked';
      case SlotStatus.blocked:
        return 'blocked';
      case SlotStatus.break_time:
        return 'break_time';
    }
  }

  bool get isAvailable => status == SlotStatus.available;

  bool get isBooked => status == SlotStatus.booked;

  bool get isBlocked => status == SlotStatus.blocked;

  bool get isBreakTime => status == SlotStatus.break_time;

  bool get isPast => endTime.isBefore(DateTime.now());

  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  bool get isFuture => startTime.isAfter(DateTime.now());

  String get timeRange {
    final startHour = startTime.hour.toString().padLeft(2, '0');
    final startMinute = startTime.minute.toString().padLeft(2, '0');
    final endHour = endTime.hour.toString().padLeft(2, '0');
    final endMinute = endTime.minute.toString().padLeft(2, '0');
    
    return '$startHour:$startMinute - $endHour:$endMinute';
  }

  String get formattedPrice {
    if (price == null) return '';
    return '${price!.toStringAsFixed(0)} KD';
  }
}
