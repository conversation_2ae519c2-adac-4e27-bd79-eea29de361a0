import 'package:flutter_test/flutter_test.dart';
import 'package:degree_180_app/core/validators/password_validator.dart';

void main() {
  group('PasswordValidator Tests', () {
    group('validatePassword', () {
      test('should return invalid for null password', () {
        // arrange
        const String? password = null;

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.score, 0);
        expect(result.message, 'كلمة المرور مطلوبة');
      });

      test('should return invalid for empty password', () {
        // arrange
        const String password = '';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.score, 0);
        expect(result.message, 'كلمة المرور مطلوبة');
      });

      test('should return invalid for short password', () {
        // arrange
        const String password = '123';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, contains('كلمة المرور قصيرة جداً'));
        expect(result.suggestions, contains('استخدم على الأقل 8 أحرف'));
      });

      test('should return invalid for password without uppercase', () {
        // arrange
        const String password = 'password123!';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, contains('لا تحتوي على أحرف كبيرة'));
        expect(result.suggestions, contains('أضف أحرف كبيرة (A-Z)'));
      });

      test('should return invalid for password without digits', () {
        // arrange
        const String password = 'Password!';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, contains('لا تحتوي على أرقام'));
        expect(result.suggestions, contains('أضف أرقام (0-9)'));
      });

      test('should return invalid for password without special characters', () {
        // arrange
        const String password = 'Password123';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, contains('لا تحتوي على رموز خاصة'));
        expect(result.suggestions, contains('أضف رموز خاصة (!@#\$%^&*)'));
      });

      test('should return invalid for common password', () {
        // arrange
        const String password = 'password123';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, contains('كلمة مرور شائعة جداً'));
        expect(result.suggestions, contains('استخدم كلمة مرور أكثر تعقيداً'));
      });

      test('should return invalid for password with repeating patterns', () {
        // arrange
        const String password = 'Password111!';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, contains('تحتوي على أنماط متكررة'));
        expect(result.suggestions, contains('تجنب تكرار الأحرف أو الأرقام'));
      });

      test('should return invalid for password with sequential characters', () {
        // arrange
        const String password = 'Password123!';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, contains('تحتوي على تسلسل منطقي'));
        expect(result.suggestions, contains('تجنب التسلسل مثل 123 أو abc'));
      });

      test('should return valid for strong password', () {
        // arrange
        const String password = 'MyStr0ng!P@ssw0rd';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, true);
        expect(result.score, greaterThanOrEqualTo(3));
        expect(result.message, 'كلمة مرور قوية');
        expect(result.issues, isEmpty);
      });

      test('should return valid for Arabic password', () {
        // arrange
        const String password = 'كلمةمرور123!قوية';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        // كلمة المرور العربية قد تحتاج تحسينات إضافية
        expect(result.score, greaterThanOrEqualTo(2));
      });
    });

    group('getPasswordStrength', () {
      test('should return very weak for empty password', () {
        // arrange
        const String password = '';

        // act
        final strength = PasswordValidator.getPasswordStrength(password);

        // assert
        expect(strength, PasswordStrength.veryWeak);
      });

      test('should return weak for simple password', () {
        // arrange
        const String password = '123456';

        // act
        final strength = PasswordValidator.getPasswordStrength(password);

        // assert
        expect(strength, PasswordStrength.veryWeak);
      });

      test('should return strong for complex password', () {
        // arrange
        const String password = 'MyStr0ng!P@ssw0rd';

        // act
        final strength = PasswordValidator.getPasswordStrength(password);

        // assert
        expect(strength, PasswordStrength.strong);
      });
    });

    group('getPasswordStrengthPercentage', () {
      test('should return 0 for empty password', () {
        // arrange
        const String password = '';

        // act
        final percentage = PasswordValidator.getPasswordStrengthPercentage(password);

        // assert
        expect(percentage, 0);
      });

      test('should return 100 for perfect password', () {
        // arrange
        const String password = 'MyStr0ng!P@ssw0rd2024';

        // act
        final percentage = PasswordValidator.getPasswordStrengthPercentage(password);

        // assert
        expect(percentage, greaterThanOrEqualTo(80));
      });
    });

    group('generateStrongPassword', () {
      test('should generate password with default length', () {
        // act
        final password = PasswordValidator.generateStrongPassword();

        // assert
        expect(password.length, 12);
        expect(password, isNotEmpty);
      });

      test('should generate password with custom length', () {
        // arrange
        const int customLength = 16;

        // act
        final password = PasswordValidator.generateStrongPassword(length: customLength);

        // assert
        expect(password.length, customLength);
        expect(password, isNotEmpty);
      });

      test('should generate different passwords on multiple calls', () async {
        // act
        final password1 = PasswordValidator.generateStrongPassword();
        // إضافة تأخير صغير لضمان اختلاف البذرة
        await Future.delayed(const Duration(milliseconds: 1));
        final password2 = PasswordValidator.generateStrongPassword();

        // assert
        expect(password1, isNot(equals(password2)));
      });
    });

    group('PasswordStrength extension', () {
      test('should return correct labels', () {
        expect(PasswordStrength.veryWeak.label, 'ضعيفة جداً');
        expect(PasswordStrength.weak.label, 'ضعيفة');
        expect(PasswordStrength.fair.label, 'متوسطة');
        expect(PasswordStrength.good.label, 'جيدة');
        expect(PasswordStrength.strong.label, 'قوية');
      });

      test('should return correct color values', () {
        expect(PasswordStrength.veryWeak.colorValue, 0xFFE53E3E);
        expect(PasswordStrength.weak.colorValue, 0xFFED8936);
        expect(PasswordStrength.fair.colorValue, 0xFFECC94B);
        expect(PasswordStrength.good.colorValue, 0xFF48BB78);
        expect(PasswordStrength.strong.colorValue, 0xFF38A169);
      });
    });

    group('Edge cases', () {
      test('should handle very long password', () {
        // arrange
        final password = 'A' * 200;

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, contains('كلمة المرور طويلة جداً'));
      });

      test('should handle password with only special characters', () {
        // arrange
        const String password = '!@#\$%^&*()';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        expect(result.isValid, false);
        expect(result.issues, isNotEmpty);
      });

      test('should handle password with mixed languages', () {
        // arrange
        const String password = 'MyPassword123!كلمةمرور';

        // act
        final result = PasswordValidator.validatePassword(password);

        // assert
        // كلمة المرور المختلطة قد تحتاج تحسينات
        expect(result.score, greaterThanOrEqualTo(2));
      });
    });
  });
}
