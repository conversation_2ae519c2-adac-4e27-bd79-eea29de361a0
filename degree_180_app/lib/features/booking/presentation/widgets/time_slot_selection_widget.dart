import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/time_slot_model.dart';

class TimeSlotSelectionWidget extends StatelessWidget {
  final List<TimeSlotModel> timeSlots;
  final TimeSlotModel? selectedTimeSlot;
  final Function(TimeSlotModel) onTimeSlotSelected;

  const TimeSlotSelectionWidget({
    super.key,
    required this.timeSlots,
    this.selectedTimeSlot,
    required this.onTimeSlotSelected,
  });

  @override
  Widget build(BuildContext context) {
    if (timeSlots.isEmpty) {
      return _buildEmptyState(context);
    }

    // Group time slots by time period
    final morningSlots = timeSlots.where((slot) => 
        slot.startTime.hour < 12).toList();
    final afternoonSlots = timeSlots.where((slot) => 
        slot.startTime.hour >= 12 && slot.startTime.hour < 17).toList();
    final eveningSlots = timeSlots.where((slot) => 
        slot.startTime.hour >= 17).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Available slots count
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.success.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.success.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.schedule,
                color: AppColors.success,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${timeSlots.where((slot) => slot.isAvailable).length} available slots',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Morning Slots
        if (morningSlots.isNotEmpty) ...[
          _buildTimePeriodSection(
            context,
            'Morning',
            Icons.wb_sunny,
            morningSlots,
          ),
          const SizedBox(height: 16),
        ],
        
        // Afternoon Slots
        if (afternoonSlots.isNotEmpty) ...[
          _buildTimePeriodSection(
            context,
            'Afternoon',
            Icons.wb_sunny_outlined,
            afternoonSlots,
          ),
          const SizedBox(height: 16),
        ],
        
        // Evening Slots
        if (eveningSlots.isNotEmpty) ...[
          _buildTimePeriodSection(
            context,
            'Evening',
            Icons.nights_stay,
            eveningSlots,
          ),
        ],
      ],
    );
  }

  Widget _buildTimePeriodSection(
    BuildContext context,
    String title,
    IconData icon,
    List<TimeSlotModel> slots,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            Icon(
              icon,
              color: AppColors.primaryPurple,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryPurple,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.primaryPurple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${slots.where((slot) => slot.isAvailable).length} available',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primaryPurple,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Time Slots Grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 2.5,
          ),
          itemCount: slots.length,
          itemBuilder: (context, index) {
            final slot = slots[index];
            return _TimeSlotCard(
              timeSlot: slot,
              isSelected: selectedTimeSlot?.id == slot.id,
              onTap: slot.isAvailable 
                  ? () => onTimeSlotSelected(slot)
                  : null,
            );
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppColors.backgroundGray,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(color: AppColors.borderGray),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule_outlined,
              size: 48,
              color: AppColors.textLight.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 8),
            Text(
              'No Available Time Slots',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textLight,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Please select a different date',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textLight,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TimeSlotCard extends StatelessWidget {
  final TimeSlotModel timeSlot;
  final bool isSelected;
  final VoidCallback? onTap;

  const _TimeSlotCard({
    required this.timeSlot,
    required this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isAvailable = timeSlot.isAvailable;
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: _getBackgroundColor(),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getBorderColor(),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            if (isSelected)
              BoxShadow(
                color: AppColors.primaryPurple.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Time
              Text(
                timeSlot.formattedTime,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: _getTextColor(),
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 4),
              
              // Status
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getStatusText(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _getStatusColor(),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              
              // Unavailable reason
              if (!isAvailable) ...[
                const SizedBox(height: 4),
                Text(
                  'غير متاح',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.error,
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    if (isSelected) {
      return AppColors.primaryPurple.withValues(alpha: 0.1);
    }
    if (!timeSlot.isAvailable) {
      return AppColors.error.withValues(alpha: 0.05);
    }
    return AppColors.backgroundWhite;
  }

  Color _getBorderColor() {
    if (isSelected) {
      return AppColors.primaryPurple;
    }
    if (!timeSlot.isAvailable) {
      return AppColors.error.withValues(alpha: 0.3);
    }
    return AppColors.borderGray;
  }

  Color _getTextColor() {
    if (isSelected) {
      return AppColors.primaryPurple;
    }
    if (!timeSlot.isAvailable) {
      return AppColors.textLight;
    }
    return AppColors.textDark;
  }

  Color _getStatusColor() {
    if (isSelected) {
      return AppColors.primaryPurple;
    }
    if (!timeSlot.isAvailable) {
      return AppColors.error;
    }
    return AppColors.success;
  }

  String _getStatusText() {
    if (isSelected) {
      return 'Selected';
    }
    if (!timeSlot.isAvailable) {
      return 'Booked';
    }
    return 'Available';
  }
}
