import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

class ServiceModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final double price;
  final String currency;
  final int durationMinutes;
  final String category;
  final List<String> images;
  final List<String> tags;
  final bool isActive;
  final bool isPopular;
  final int bookingCount;
  final double? discountPercentage;
  final double? discountedPrice;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ServiceModel({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.currency = 'KD',
    required this.durationMinutes,
    required this.category,
    this.images = const [],
    this.tags = const [],
    this.isActive = true,
    this.isPopular = false,
    this.bookingCount = 0,
    this.discountPercentage,
    this.discountedPrice,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ServiceModel.fromJson(Map<String, dynamic> json) {
    return ServiceModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'KD',
      durationMinutes: json['durationMinutes'] ?? 30,
      category: json['category'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
      isActive: json['isActive'] ?? true,
      isPopular: json['isPopular'] ?? false,
      bookingCount: json['bookingCount'] ?? 0,
      discountPercentage: json['discountPercentage']?.toDouble(),
      discountedPrice: json['discountedPrice']?.toDouble(),
      metadata: json['metadata'],
      createdAt: _parseDateTime(json['createdAt']),
      updatedAt: _parseDateTime(json['updatedAt']),
    );
  }

  factory ServiceModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    data['id'] = doc.id;
    return ServiceModel.fromJson(data);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'durationMinutes': durationMinutes,
      'category': category,
      'images': images,
      'tags': tags,
      'isActive': isActive,
      'isPopular': isPopular,
      'bookingCount': bookingCount,
      'discountPercentage': discountPercentage,
      'discountedPrice': discountedPrice,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id');
    return json;
  }

  ServiceModel copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? currency,
    int? durationMinutes,
    String? category,
    List<String>? images,
    List<String>? tags,
    bool? isActive,
    bool? isPopular,
    int? bookingCount,
    double? discountPercentage,
    double? discountedPrice,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      category: category ?? this.category,
      images: images ?? this.images,
      tags: tags ?? this.tags,
      isActive: isActive ?? this.isActive,
      isPopular: isPopular ?? this.isPopular,
      bookingCount: bookingCount ?? this.bookingCount,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountedPrice: discountedPrice ?? this.discountedPrice,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  double get finalPrice {
    if (discountedPrice != null) return discountedPrice!;
    if (discountPercentage != null) {
      return price * (1 - discountPercentage! / 100);
    }
    return price;
  }

  String get formattedPrice {
    return '${finalPrice.toStringAsFixed(1)} $currency';
  }

  String get formattedDuration {
    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;
    
    if (hours > 0 && minutes > 0) {
      return '${hours}h ${minutes}m';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${minutes}m';
    }
  }

  bool get hasDiscount {
    return discountPercentage != null || discountedPrice != null;
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is String) return DateTime.parse(value);
    if (value is DateTime) return value;
    return DateTime.now();
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        currency,
        durationMinutes,
        category,
        images,
        tags,
        isActive,
        isPopular,
        bookingCount,
        discountPercentage,
        discountedPrice,
        metadata,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'ServiceModel(id: $id, name: $name, price: $formattedPrice, duration: $formattedDuration)';
  }
}
