import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/models/booking_statistics.dart';
import '../../../../core/enums/booking_status.dart';
import '../../domain/repositories/booking_repository.dart' as repo;
import '../datasources/booking_remote_data_source.dart';

class BookingRepositoryImpl implements repo.BookingRepository {
  final BookingRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  BookingRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, BookingModel>> createBooking(repo.CreateBookingParams params) async {
    if (await networkInfo.isConnected) {
      try {
        final booking = await remoteDataSource.createBooking(params);
        return Right(booking);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<BookingModel>>> getUserBookings(
    String userId, {
    BookingStatus? status,
    int? limit,
    String? lastDocumentId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final bookings = await remoteDataSource.getUserBookings(
          userId,
          status: status,
          limit: limit,
          lastDocumentId: lastDocumentId,
        );
        return Right(bookings);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<BookingModel>>> getProviderBookings(
    String providerId, {
    BookingStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    String? lastDocumentId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final bookings = await remoteDataSource.getProviderBookings(
          providerId,
          status: status,
          startDate: startDate,
          endDate: endDate,
          limit: limit,
          lastDocumentId: lastDocumentId,
        );
        return Right(bookings);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, BookingModel>> getBookingById(String bookingId) async {
    if (await networkInfo.isConnected) {
      try {
        final booking = await remoteDataSource.getBookingById(bookingId);
        return Right(booking);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, BookingModel>> updateBookingStatus(
    String bookingId,
    BookingStatus status, {
    String? reason,
    String? updatedBy,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final booking = await remoteDataSource.updateBookingStatus(
          bookingId,
          status,
          reason: reason,
          updatedBy: updatedBy,
        );
        return Right(booking);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, BookingModel>> cancelBooking(
    String bookingId,
    String reason,
    String cancelledBy,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final booking = await remoteDataSource.cancelBooking(bookingId, reason, cancelledBy);
        return Right(booking);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, BookingModel>> rescheduleBooking(
    String bookingId,
    DateTime newDate,
    String newTimeSlot,
    String? reason,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final booking = await remoteDataSource.rescheduleBooking(bookingId, newDate, newTimeSlot);
        return Right(booking);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<TimeSlotModel>>> getAvailableTimeSlots(
    String providerId,
    DateTime date,
    int serviceDurationMinutes,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final timeSlots = await remoteDataSource.getAvailableTimeSlots(
          providerId,
          date,
          serviceDurationMinutes,
        );
        return Right(timeSlots);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, BookingStatistics>> getBookingStatistics(
    String providerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final statisticsMap = await remoteDataSource.getBookingStatistics(
          providerId,
          startDate: startDate,
          endDate: endDate,
        );
        final statistics = BookingStatistics.fromMap(statisticsMap);
        return Right(statistics);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<BookingModel>>> getUpcomingBookings(DateTime reminderTime) async {
    if (await networkInfo.isConnected) {
      try {
        final bookings = await remoteDataSource.getUpcomingBookings(reminderTime);
        return Right(bookings);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> markReminderSent(String bookingId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.markReminderSent(bookingId);
        return const Right(null);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<BookingModel>>> getBookingHistory(
    String userId, {
    int? limit,
    String? lastDocumentId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final bookings = await remoteDataSource.getBookingHistory(
          userId,
          limit: limit,
          lastDocumentId: lastDocumentId,
        );
        return Right(bookings);
      } on ServerException {
        return const Left(ServerFailure(message: 'Server error occurred'));
      } catch (e) {
        return Left(ServerFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
