import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/models/booking_model.dart';

class PaymentSummaryWidget extends StatelessWidget {
  final BookingModel? booking;
  final double discount;
  final String? promoCode;
  final bool showDetails;

  const PaymentSummaryWidget({
    super.key,
    this.booking,
    this.discount = 0.0,
    this.promoCode,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    // Mock data if booking is null
    final subtotal = booking?.totalAmount ?? 25.0;
    final serviceFee = subtotal * 0.025; // 2.5%
    final tax = subtotal * 0.05; // 5%
    final total = subtotal + serviceFee + tax - discount;

    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: AppColors.primaryPurple,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Payment Summary',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Service Details
            if (showDetails) ...[
              _buildServiceDetails(),
              const SizedBox(height: 16),
              Divider(color: AppColors.borderGray),
              const SizedBox(height: 16),
            ],
            
            // Cost Breakdown
            _buildSummaryRow('Subtotal', subtotal, isSubtotal: true),
            _buildSummaryRow('Service Fee (2.5%)', serviceFee),
            _buildSummaryRow('Tax (5%)', tax),
            
            if (discount > 0) ...[
              _buildSummaryRow('Discount', -discount, isDiscount: true),
              if (promoCode != null)
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4),
                  child: Text(
                    'Promo: $promoCode',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.success,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ],
            
            const SizedBox(height: 12),
            Divider(color: AppColors.borderGray, thickness: 2),
            const SizedBox(height: 12),
            
            // Total
            _buildSummaryRow('Total', total, isTotal: true),
            
            const SizedBox(height: 16),
            
            // Payment Info
            _buildPaymentInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Details',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.backgroundGray,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.content_cut,
                color: AppColors.textLight,
              ),
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    booking?.serviceName ?? 'Hair Cut & Styling',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    booking?.providerName ?? 'Glamour Salon',
                    style: TextStyle(
                      color: AppColors.textLight,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatDateTime(booking?.appointmentDateTime ?? DateTime.now()),
                    style: TextStyle(
                      color: AppColors.primaryPurple,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryRow(
    String label, 
    double amount, {
    bool isSubtotal = false,
    bool isDiscount = false,
    bool isTotal = false,
  }) {
    Color textColor = AppColors.textDark;
    FontWeight fontWeight = FontWeight.normal;
    double fontSize = 14;

    if (isDiscount) {
      textColor = AppColors.success;
      fontWeight = FontWeight.w600;
    } else if (isTotal) {
      textColor = AppColors.primaryPurple;
      fontWeight = FontWeight.bold;
      fontSize = 16;
    } else if (isSubtotal) {
      fontWeight = FontWeight.w600;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontWeight: fontWeight,
              fontSize: fontSize,
            ),
          ),
          Text(
            '${amount.abs().toStringAsFixed(3)} KWD',
            style: TextStyle(
              color: textColor,
              fontWeight: fontWeight,
              fontSize: fontSize,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.primaryPurple.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.primaryPurple.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                size: 16,
                color: AppColors.primaryPurple,
              ),
              const SizedBox(width: 8),
              Text(
                'Secure Payment',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryPurple,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• Your payment is processed securely\n'
            '• 256-bit SSL encryption\n'
            '• PCI DSS compliant\n'
            '• Refund available as per policy',
            style: TextStyle(
              color: AppColors.textLight,
              fontSize: 12,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    final month = months[dateTime.month - 1];
    final day = dateTime.day;
    final hour = dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '$month $day at $displayHour:$minute $period';
  }
}

// Compact payment summary for smaller spaces
class CompactPaymentSummaryWidget extends StatelessWidget {
  final double subtotal;
  final double discount;
  final String? promoCode;

  const CompactPaymentSummaryWidget({
    super.key,
    required this.subtotal,
    this.discount = 0.0,
    this.promoCode,
  });

  @override
  Widget build(BuildContext context) {
    final serviceFee = subtotal * 0.025;
    final tax = subtotal * 0.05;
    final total = subtotal + serviceFee + tax - discount;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.backgroundGray,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderGray),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Subtotal',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                '${subtotal.toStringAsFixed(3)} KWD',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          if (discount > 0) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Discount${promoCode != null ? ' ($promoCode)' : ''}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.success,
                  ),
                ),
                Text(
                  '-${discount.toStringAsFixed(3)} KWD',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
          
          const SizedBox(height: 8),
          Divider(color: AppColors.borderGray),
          const SizedBox(height: 8),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryPurple,
                ),
              ),
              Text(
                '${total.toStringAsFixed(3)} KWD',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryPurple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Payment breakdown widget for detailed view
class PaymentBreakdownWidget extends StatelessWidget {
  final double subtotal;
  final double serviceFee;
  final double tax;
  final double discount;
  final String? promoCode;
  final Map<String, double>? additionalFees;

  const PaymentBreakdownWidget({
    super.key,
    required this.subtotal,
    required this.serviceFee,
    required this.tax,
    this.discount = 0.0,
    this.promoCode,
    this.additionalFees,
  });

  @override
  Widget build(BuildContext context) {
    final total = subtotal + serviceFee + tax - discount +
                  (additionalFees?.values.fold(0.0, (a, b) => a + b) ?? 0.0);

    return Column(
      children: [
        _buildBreakdownItem('Service Amount', subtotal),
        _buildBreakdownItem('Service Fee', serviceFee, isPercentage: true, percentage: 2.5),
        _buildBreakdownItem('Tax', tax, isPercentage: true, percentage: 5.0),
        
        if (additionalFees != null)
          ...additionalFees!.entries.map((entry) => 
            _buildBreakdownItem(entry.key, entry.value)),
        
        if (discount > 0)
          _buildBreakdownItem(
            'Discount${promoCode != null ? ' ($promoCode)' : ''}', 
            -discount, 
            isDiscount: true,
          ),
        
        const Divider(thickness: 2),
        
        _buildBreakdownItem('Total Amount', total, isTotal: true),
      ],
    );
  }

  Widget _buildBreakdownItem(
    String label, 
    double amount, {
    bool isPercentage = false,
    double? percentage,
    bool isDiscount = false,
    bool isTotal = false,
  }) {
    Color textColor = AppColors.textDark;
    FontWeight fontWeight = FontWeight.normal;

    if (isDiscount) {
      textColor = AppColors.success;
      fontWeight = FontWeight.w600;
    } else if (isTotal) {
      textColor = AppColors.primaryPurple;
      fontWeight = FontWeight.bold;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: textColor,
                    fontWeight: fontWeight,
                  ),
                ),
                if (isPercentage && percentage != null)
                  Text(
                    ' (${percentage.toStringAsFixed(1)}%)',
                    style: TextStyle(
                      color: AppColors.textLight,
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),
          Text(
            '${amount.abs().toStringAsFixed(3)} KWD',
            style: TextStyle(
              color: textColor,
              fontWeight: fontWeight,
            ),
          ),
        ],
      ),
    );
  }
}
