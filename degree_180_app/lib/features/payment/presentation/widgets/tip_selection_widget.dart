import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class TipSelectionWidget extends StatefulWidget {
  final double baseAmount;
  final Function(double) onTipChanged;

  const TipSelectionWidget({
    super.key,
    required this.baseAmount,
    required this.onTipChanged,
  });

  @override
  State<TipSelectionWidget> createState() => _TipSelectionWidgetState();
}

class _TipSelectionWidgetState extends State<TipSelectionWidget> {
  double _selectedTipPercentage = 0;
  double _customTipAmount = 0;
  bool _isCustomTip = false;
  final TextEditingController _customTipController = TextEditingController();

  final List<double> _tipPercentages = [0, 10, 15, 20, 25];

  @override
  void dispose() {
    _customTipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Percentage tip options
        Wrap(
          spacing: AppConstants.smallPadding,
          runSpacing: AppConstants.smallPadding,
          children: _tipPercentages.map((percentage) {
            final isSelected = !_isCustomTip && _selectedTipPercentage == percentage;
            return _buildTipChip(
              label: percentage == 0 ? 'No Tip' : '${percentage.toInt()}%',
              isSelected: isSelected,
              onTap: () => _selectPercentageTip(percentage),
            );
          }).toList(),
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        // Custom tip option
        _buildTipChip(
          label: 'Custom',
          isSelected: _isCustomTip,
          onTap: () => _selectCustomTip(),
        ),
        
        // Custom tip input
        if (_isCustomTip) ...[
          const SizedBox(height: AppConstants.defaultPadding),
          TextField(
            controller: _customTipController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'Custom tip amount (KD)',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                borderSide: const BorderSide(color: AppColors.primaryPurple),
              ),
            ),
            onChanged: (value) {
              final amount = double.tryParse(value) ?? 0;
              setState(() {
                _customTipAmount = amount;
              });
              widget.onTipChanged(amount);
            },
          ),
        ],
        
        // Tip amount display
        if (_getTipAmount() > 0) ...[
          const SizedBox(height: AppConstants.defaultPadding),
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppColors.primaryPurple.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tip Amount:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textDark,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${_getTipAmount().toStringAsFixed(2)} KD',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.primaryPurple,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTipChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primaryPurple : AppColors.backgroundGray,
            borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
            border: Border.all(
              color: isSelected ? AppColors.primaryPurple : AppColors.borderGray,
            ),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: isSelected ? AppColors.textWhite : AppColors.textDark,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  void _selectPercentageTip(double percentage) {
    setState(() {
      _selectedTipPercentage = percentage;
      _isCustomTip = false;
      _customTipController.clear();
      _customTipAmount = 0;
    });
    
    final tipAmount = widget.baseAmount * (percentage / 100);
    widget.onTipChanged(tipAmount);
  }

  void _selectCustomTip() {
    setState(() {
      _isCustomTip = true;
      _selectedTipPercentage = 0;
    });
    
    if (_customTipController.text.isNotEmpty) {
      widget.onTipChanged(_customTipAmount);
    } else {
      widget.onTipChanged(0);
    }
  }

  double _getTipAmount() {
    if (_isCustomTip) {
      return _customTipAmount;
    } else {
      return widget.baseAmount * (_selectedTipPercentage / 100);
    }
  }
}
