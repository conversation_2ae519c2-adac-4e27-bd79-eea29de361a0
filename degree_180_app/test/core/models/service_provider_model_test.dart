import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:degree_180_app/core/models/service_provider_model.dart';
import 'package:degree_180_app/core/models/location_model.dart';
import 'package:degree_180_app/core/models/working_hours_model.dart';
import 'package:degree_180_app/core/models/service_model.dart';
import 'package:degree_180_app/core/enums/service_type.dart';

void main() {
  group('ServiceProviderModel', () {
    late ServiceProviderModel testProvider;
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 1);
      testProvider = ServiceProviderModel(
        id: 'test-id',
        name: 'Test Provider',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        profileImageUrl: 'https://example.com/image.jpg',
        serviceType: ServiceType.salon,
        description: 'Test description',
        galleryImages: ['image1.jpg', 'image2.jpg'],
        location: const LocationModel(
          latitude: 29.3759,
          longitude: 47.9774,
          address: 'Test Address',
          city: 'Kuwait City',
          country: 'Kuwait',
        ),
        rating: 4.5,
        reviewCount: 100,
        isActive: true,
        isVerified: true,
        isOnline: true,
        workingHours: WorkingHoursModel(
          schedule: {
            'monday': const DaySchedule(
              isOpen: true,
              openTime: '09:00',
              closeTime: '18:00',
            ),
          },
        ),
        services: [
          ServiceModel(
            id: 'service-1',
            name: 'Test Service',
            description: 'Test service description',
            price: 25.0,
            durationMinutes: 60,
            category: 'Hair',
            createdAt: testDate,
            updatedAt: testDate,
          ),
        ],
        socialMedia: {'instagram': '@testprovider'},
        businessInfo: {'license': '12345'},
        createdAt: testDate,
        updatedAt: testDate,
      );
    });

    group('Constructor', () {
      test('should create ServiceProviderModel with required fields', () {
        final provider = ServiceProviderModel(
          id: 'test-id',
          name: 'Test Provider',
          email: '<EMAIL>',
          serviceType: ServiceType.salon,
          createdAt: testDate,
          updatedAt: testDate,
        );

        expect(provider.id, 'test-id');
        expect(provider.name, 'Test Provider');
        expect(provider.email, '<EMAIL>');
        expect(provider.serviceType, ServiceType.salon);
        expect(provider.rating, 0.0);
        expect(provider.reviewCount, 0);
        expect(provider.isActive, true);
        expect(provider.isVerified, false);
        expect(provider.isOnline, false);
        expect(provider.galleryImages, isEmpty);
        expect(provider.services, isEmpty);
      });

      test('should create ServiceProviderModel with all fields', () {
        expect(testProvider.id, 'test-id');
        expect(testProvider.name, 'Test Provider');
        expect(testProvider.email, '<EMAIL>');
        expect(testProvider.phoneNumber, '+**********');
        expect(testProvider.profileImageUrl, 'https://example.com/image.jpg');
        expect(testProvider.serviceType, ServiceType.salon);
        expect(testProvider.description, 'Test description');
        expect(testProvider.galleryImages, ['image1.jpg', 'image2.jpg']);
        expect(testProvider.location, isNotNull);
        expect(testProvider.rating, 4.5);
        expect(testProvider.reviewCount, 100);
        expect(testProvider.isActive, true);
        expect(testProvider.isVerified, true);
        expect(testProvider.isOnline, true);
        expect(testProvider.workingHours, isNotNull);
        expect(testProvider.services, hasLength(1));
        expect(testProvider.socialMedia, {'instagram': '@testprovider'});
        expect(testProvider.businessInfo, {'license': '12345'});
        expect(testProvider.createdAt, testDate);
        expect(testProvider.updatedAt, testDate);
      });
    });

    group('JSON Serialization', () {
      test('should convert to JSON correctly', () {
        final json = testProvider.toJson();

        expect(json['id'], 'test-id');
        expect(json['name'], 'Test Provider');
        expect(json['email'], '<EMAIL>');
        expect(json['phoneNumber'], '+**********');
        expect(json['profileImageUrl'], 'https://example.com/image.jpg');
        expect(json['serviceType'], 'salon');
        expect(json['description'], 'Test description');
        expect(json['galleryImages'], ['image1.jpg', 'image2.jpg']);
        expect(json['location'], isNotNull);
        expect(json['rating'], 4.5);
        expect(json['reviewCount'], 100);
        expect(json['isActive'], true);
        expect(json['isVerified'], true);
        expect(json['isOnline'], true);
        expect(json['workingHours'], isNotNull);
        expect(json['services'], hasLength(1));
        expect(json['socialMedia'], {'instagram': '@testprovider'});
        expect(json['businessInfo'], {'license': '12345'});
        expect(json['createdAt'], isA<Timestamp>());
        expect(json['updatedAt'], isA<Timestamp>());
      });

      test('should create from JSON correctly', () {
        final json = testProvider.toJson();
        final fromJson = ServiceProviderModel.fromJson(json);

        expect(fromJson.id, testProvider.id);
        expect(fromJson.name, testProvider.name);
        expect(fromJson.email, testProvider.email);
        expect(fromJson.phoneNumber, testProvider.phoneNumber);
        expect(fromJson.profileImageUrl, testProvider.profileImageUrl);
        expect(fromJson.serviceType, testProvider.serviceType);
        expect(fromJson.description, testProvider.description);
        expect(fromJson.galleryImages, testProvider.galleryImages);
        expect(fromJson.rating, testProvider.rating);
        expect(fromJson.reviewCount, testProvider.reviewCount);
        expect(fromJson.isActive, testProvider.isActive);
        expect(fromJson.isVerified, testProvider.isVerified);
        expect(fromJson.isOnline, testProvider.isOnline);
        expect(fromJson.socialMedia, testProvider.socialMedia);
        expect(fromJson.businessInfo, testProvider.businessInfo);
      });

      test('should handle missing optional fields in JSON', () {
        final minimalJson = {
          'id': 'test-id',
          'name': 'Test Provider',
          'email': '<EMAIL>',
          'serviceType': 'salon',
          'createdAt': Timestamp.fromDate(testDate),
          'updatedAt': Timestamp.fromDate(testDate),
        };

        final provider = ServiceProviderModel.fromJson(minimalJson);

        expect(provider.id, 'test-id');
        expect(provider.name, 'Test Provider');
        expect(provider.email, '<EMAIL>');
        expect(provider.serviceType, ServiceType.salon);
        expect(provider.phoneNumber, isNull);
        expect(provider.profileImageUrl, isNull);
        expect(provider.description, isNull);
        expect(provider.galleryImages, isEmpty);
        expect(provider.location, isNull);
        expect(provider.rating, 0.0);
        expect(provider.reviewCount, 0);
        expect(provider.isActive, true);
        expect(provider.isVerified, false);
        expect(provider.isOnline, false);
        expect(provider.workingHours, isNull);
        expect(provider.services, isEmpty);
        expect(provider.socialMedia, isNull);
        expect(provider.businessInfo, isNull);
      });
    });

    group('Firestore Serialization', () {
      test('should convert to Firestore format correctly', () {
        final firestoreData = testProvider.toFirestore();

        expect(firestoreData.containsKey('id'), false); // ID should be removed
        expect(firestoreData['name'], 'Test Provider');
        expect(firestoreData['email'], '<EMAIL>');
        expect(firestoreData['serviceType'], 'salon');
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        final updated = testProvider.copyWith(
          name: 'Updated Provider',
          rating: 5.0,
          isOnline: false,
        );

        expect(updated.name, 'Updated Provider');
        expect(updated.rating, 5.0);
        expect(updated.isOnline, false);
        
        // Other fields should remain the same
        expect(updated.id, testProvider.id);
        expect(updated.email, testProvider.email);
        expect(updated.serviceType, testProvider.serviceType);
        expect(updated.reviewCount, testProvider.reviewCount);
        expect(updated.isActive, testProvider.isActive);
        expect(updated.isVerified, testProvider.isVerified);
      });

      test('should create identical copy when no fields are updated', () {
        final copy = testProvider.copyWith();

        expect(copy.id, testProvider.id);
        expect(copy.name, testProvider.name);
        expect(copy.email, testProvider.email);
        expect(copy.serviceType, testProvider.serviceType);
        expect(copy.rating, testProvider.rating);
        expect(copy.reviewCount, testProvider.reviewCount);
        expect(copy.isActive, testProvider.isActive);
        expect(copy.isVerified, testProvider.isVerified);
        expect(copy.isOnline, testProvider.isOnline);
      });
    });

    group('Equality', () {
      test('should be equal when all properties are the same', () {
        final provider1 = ServiceProviderModel(
          id: 'test-id',
          name: 'Test Provider',
          email: '<EMAIL>',
          serviceType: ServiceType.salon,
          createdAt: testDate,
          updatedAt: testDate,
        );

        final provider2 = ServiceProviderModel(
          id: 'test-id',
          name: 'Test Provider',
          email: '<EMAIL>',
          serviceType: ServiceType.salon,
          createdAt: testDate,
          updatedAt: testDate,
        );

        expect(provider1, equals(provider2));
        expect(provider1.hashCode, equals(provider2.hashCode));
      });

      test('should not be equal when properties differ', () {
        final provider1 = ServiceProviderModel(
          id: 'test-id-1',
          name: 'Test Provider',
          email: '<EMAIL>',
          serviceType: ServiceType.salon,
          createdAt: testDate,
          updatedAt: testDate,
        );

        final provider2 = ServiceProviderModel(
          id: 'test-id-2',
          name: 'Test Provider',
          email: '<EMAIL>',
          serviceType: ServiceType.salon,
          createdAt: testDate,
          updatedAt: testDate,
        );

        expect(provider1, isNot(equals(provider2)));
      });
    });

    group('toString', () {
      test('should return formatted string representation', () {
        final string = testProvider.toString();
        
        expect(string, contains('ServiceProviderModel'));
        expect(string, contains('test-id'));
        expect(string, contains('Test Provider'));
        expect(string, contains('salon'));
        expect(string, contains('4.5'));
      });
    });
  });
}
