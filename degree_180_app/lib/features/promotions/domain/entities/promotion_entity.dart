import 'package:equatable/equatable.dart';

enum PromotionType {
  percentage,
  fixedAmount,
  buyOneGetOne,
  freeService,
  bundleDiscount,
}

enum PromotionStatus {
  active,
  inactive,
  expired,
  upcoming,
  paused,
}

enum PromotionTarget {
  all,
  newCustomers,
  returningCustomers,
  vipCustomers,
  specificServices,
}

class PromotionEntity extends Equatable {
  final String id;
  final String title;
  final String description;
  final String? imageUrl;
  final PromotionType type;
  final PromotionStatus status;
  final PromotionTarget target;
  final double value; // percentage or fixed amount
  final double? minOrderAmount;
  final double? maxDiscountAmount;
  final List<String>? applicableServices;
  final List<String>? applicableProviders;
  final String? couponCode;
  final int? usageLimit;
  final int usageCount;
  final int? usageLimitPerCustomer;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? termsAndConditions;
  final Map<String, dynamic>? metadata;

  const PromotionEntity({
    required this.id,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.type,
    required this.status,
    required this.target,
    required this.value,
    this.minOrderAmount,
    this.maxDiscountAmount,
    this.applicableServices,
    this.applicableProviders,
    this.couponCode,
    this.usageLimit,
    this.usageCount = 0,
    this.usageLimitPerCustomer,
    required this.startDate,
    required this.endDate,
    required this.createdAt,
    required this.updatedAt,
    this.termsAndConditions,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        imageUrl,
        type,
        status,
        target,
        value,
        minOrderAmount,
        maxDiscountAmount,
        applicableServices,
        applicableProviders,
        couponCode,
        usageLimit,
        usageCount,
        usageLimitPerCustomer,
        startDate,
        endDate,
        createdAt,
        updatedAt,
        termsAndConditions,
        metadata,
      ];

  PromotionEntity copyWith({
    String? id,
    String? title,
    String? description,
    String? imageUrl,
    PromotionType? type,
    PromotionStatus? status,
    PromotionTarget? target,
    double? value,
    double? minOrderAmount,
    double? maxDiscountAmount,
    List<String>? applicableServices,
    List<String>? applicableProviders,
    String? couponCode,
    int? usageLimit,
    int? usageCount,
    int? usageLimitPerCustomer,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? termsAndConditions,
    Map<String, dynamic>? metadata,
  }) {
    return PromotionEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      type: type ?? this.type,
      status: status ?? this.status,
      target: target ?? this.target,
      value: value ?? this.value,
      minOrderAmount: minOrderAmount ?? this.minOrderAmount,
      maxDiscountAmount: maxDiscountAmount ?? this.maxDiscountAmount,
      applicableServices: applicableServices ?? this.applicableServices,
      applicableProviders: applicableProviders ?? this.applicableProviders,
      couponCode: couponCode ?? this.couponCode,
      usageLimit: usageLimit ?? this.usageLimit,
      usageCount: usageCount ?? this.usageCount,
      usageLimitPerCustomer: usageLimitPerCustomer ?? this.usageLimitPerCustomer,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      termsAndConditions: termsAndConditions ?? this.termsAndConditions,
      metadata: metadata ?? this.metadata,
    );
  }

  String get typeString {
    switch (type) {
      case PromotionType.percentage:
        return 'percentage';
      case PromotionType.fixedAmount:
        return 'fixed_amount';
      case PromotionType.buyOneGetOne:
        return 'buy_one_get_one';
      case PromotionType.freeService:
        return 'free_service';
      case PromotionType.bundleDiscount:
        return 'bundle_discount';
    }
  }

  String get statusString {
    switch (status) {
      case PromotionStatus.active:
        return 'active';
      case PromotionStatus.inactive:
        return 'inactive';
      case PromotionStatus.expired:
        return 'expired';
      case PromotionStatus.upcoming:
        return 'upcoming';
      case PromotionStatus.paused:
        return 'paused';
    }
  }

  String get targetString {
    switch (target) {
      case PromotionTarget.all:
        return 'all';
      case PromotionTarget.newCustomers:
        return 'new_customers';
      case PromotionTarget.returningCustomers:
        return 'returning_customers';
      case PromotionTarget.vipCustomers:
        return 'vip_customers';
      case PromotionTarget.specificServices:
        return 'specific_services';
    }
  }

  bool get isActive => status == PromotionStatus.active;
  bool get isExpired => status == PromotionStatus.expired || DateTime.now().isAfter(endDate);
  bool get isUpcoming => status == PromotionStatus.upcoming || DateTime.now().isBefore(startDate);
  bool get isPaused => status == PromotionStatus.paused;

  bool get hasUsageLimit => usageLimit != null;
  bool get isUsageLimitReached => hasUsageLimit && usageCount >= usageLimit!;

  bool get hasCouponCode => couponCode != null && couponCode!.isNotEmpty;

  String get discountText {
    switch (type) {
      case PromotionType.percentage:
        return '${value.toInt()}% OFF';
      case PromotionType.fixedAmount:
        return '${value.toStringAsFixed(0)} KD OFF';
      case PromotionType.buyOneGetOne:
        return 'Buy 1 Get 1 FREE';
      case PromotionType.freeService:
        return 'FREE SERVICE';
      case PromotionType.bundleDiscount:
        return 'Bundle Deal';
    }
  }

  String get validityText {
    final now = DateTime.now();
    if (isExpired) {
      return 'Expired';
    } else if (isUpcoming) {
      final daysUntilStart = startDate.difference(now).inDays;
      return 'Starts in $daysUntilStart days';
    } else {
      final daysUntilEnd = endDate.difference(now).inDays;
      if (daysUntilEnd == 0) {
        return 'Expires today';
      } else if (daysUntilEnd == 1) {
        return 'Expires tomorrow';
      } else {
        return 'Expires in $daysUntilEnd days';
      }
    }
  }

  double calculateDiscount(double orderAmount) {
    if (!isActive || isExpired || orderAmount < (minOrderAmount ?? 0)) {
      return 0.0;
    }

    double discount = 0.0;
    switch (type) {
      case PromotionType.percentage:
        discount = orderAmount * (value / 100);
        break;
      case PromotionType.fixedAmount:
        discount = value;
        break;
      case PromotionType.buyOneGetOne:
      case PromotionType.freeService:
      case PromotionType.bundleDiscount:
        // These require special logic based on services
        discount = 0.0;
        break;
    }

    if (maxDiscountAmount != null && discount > maxDiscountAmount!) {
      discount = maxDiscountAmount!;
    }

    return discount;
  }

  bool isApplicableToService(String serviceId) {
    if (applicableServices == null || applicableServices!.isEmpty) {
      return true; // Applicable to all services
    }
    return applicableServices!.contains(serviceId);
  }

  bool isApplicableToProvider(String providerId) {
    if (applicableProviders == null || applicableProviders!.isEmpty) {
      return true; // Applicable to all providers
    }
    return applicableProviders!.contains(providerId);
  }
}
