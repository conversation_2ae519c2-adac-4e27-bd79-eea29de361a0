import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';

import '../../../../core/models/location_model.dart';
import '../../../../core/enums/service_type.dart';
import '../bloc/service_provider_bloc.dart';
import '../widgets/service_provider_card.dart';
import '../widgets/providers_filter_sheet.dart';

class ServiceProvidersPage extends StatefulWidget {
  final ServiceType? serviceType;
  final LocationModel? userLocation;
  final bool featured;
  final bool nearby;

  const ServiceProvidersPage({
    super.key,
    this.serviceType,
    this.userLocation,
    this.featured = false,
    this.nearby = false,
  });

  @override
  State<ServiceProvidersPage> createState() => _ServiceProvidersPageState();
}

class _ServiceProvidersPageState extends State<ServiceProvidersPage> {
  final ScrollController _scrollController = ScrollController();
  ServiceType? _selectedServiceType;
  double _selectedRadius = 10.0;
  double _minRating = 0.0;
  bool _isVerified = false;
  bool _isOnline = false;

  @override
  void initState() {
    super.initState();
    _selectedServiceType = widget.serviceType;
    _selectedRadius = widget.nearby ? 10.0 : 10.0;
    _loadProviders();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadProviders() {
    if (widget.featured) {
      context.read<ServiceProviderBloc>().add(
        LoadFeaturedProvidersEvent(
          serviceType: _selectedServiceType,
          limit: 20,
        ),
      );
    } else if (widget.nearby && widget.userLocation != null) {
      context.read<ServiceProviderBloc>().add(
        LoadNearbyProvidersEvent(
          userLocation: widget.userLocation!,
          radiusKm: _selectedRadius ?? 10.0,
          serviceType: _selectedServiceType,
          limit: 20,
        ),
      );
    } else {
      context.read<ServiceProviderBloc>().add(
        LoadServiceProvidersEvent(
          serviceType: _selectedServiceType,
          userLocation: widget.userLocation,
          radiusKm: _selectedRadius,
          limit: 20,
          isVerified: _isVerified,
          isOnline: _isOnline,
          minRating: _minRating,
        ),
      );
    }
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ProvidersFilterSheet(
        selectedServiceType: _selectedServiceType,
        selectedRadius: _selectedRadius,
        minRating: _minRating,
        isVerified: _isVerified,
        isOnline: _isOnline,
        onApplyFilters: _applyFilters,
      ),
    );
  }

  void _applyFilters({
    ServiceType? serviceType,
    double? radius,
    double? minRating,
    bool? isVerified,
    bool? isOnline,
  }) {
    setState(() {
      _selectedServiceType = serviceType;
      _selectedRadius = radius ?? _selectedRadius;
      _minRating = minRating ?? _minRating;
      _isVerified = isVerified ?? _isVerified;
      _isOnline = isOnline ?? _isOnline;
    });
    _loadProviders();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getPageTitle()),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            onPressed: _showFilterSheet,
            icon: const Icon(Icons.filter_list),
          ),
          IconButton(
            onPressed: () => Navigator.pushNamed(context, '/search'),
            icon: const Icon(Icons.search),
          ),
        ],
      ),
      body: Column(
        children: [
          // Active Filters
          if (_hasActiveFilters()) _buildActiveFilters(),
          
          // Providers List
          Expanded(
            child: BlocBuilder<ServiceProviderBloc, ServiceProviderState>(
              builder: (context, state) {
                if (state is ServiceProviderLoading) {
                  return const EnhancedLoadingWidget(
                    message: 'Loading service providers...',
                  );
                }
                
                if (state is ServiceProviderError) {
                  return _buildErrorState(state.message);
                }
                
                if (state is ServiceProviderEmpty) {
                  return _buildEmptyState(state.message);
                }
                
                if (state is ServiceProviderLoaded) {
                  return _buildProvidersList(state.providers);
                }
                
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getPageTitle() {
    if (widget.featured) return 'Featured Providers';
    if (widget.nearby) return 'Nearby Providers';
    if (_selectedServiceType != null) {
      return '${_selectedServiceType!.displayName} Providers';
    }
    return 'Service Providers';
  }

  bool _hasActiveFilters() {
    return _selectedServiceType != null ||
           _selectedRadius != null ||
           _minRating != null ||
           _isVerified != null ||
           _isOnline != null;
  }

  Widget _buildActiveFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: AppColors.backgroundGray,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Active Filters',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: _clearFilters,
                child: const Text('Clear All'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _buildFilterChips(),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildFilterChips() {
    final chips = <Widget>[];
    
    if (_selectedServiceType != null) {
      chips.add(_buildFilterChip(
        _selectedServiceType!.displayName,
        () => setState(() {
          _selectedServiceType = null;
          _loadProviders();
        }),
      ));
    }
    
    if (_selectedRadius != null) {
      chips.add(_buildFilterChip(
        'Within ${_selectedRadius!.toInt()} km',
        () => setState(() {
          _selectedRadius = null;
          _loadProviders();
        }),
      ));
    }
    
    if (_minRating != null) {
      chips.add(_buildFilterChip(
        '${_minRating!.toStringAsFixed(1)}+ Rating',
        () => setState(() {
          _minRating = null;
          _loadProviders();
        }),
      ));
    }
    
    if (_isVerified == true) {
      chips.add(_buildFilterChip(
        'Verified Only',
        () => setState(() {
          _isVerified = null;
          _loadProviders();
        }),
      ));
    }
    
    if (_isOnline == true) {
      chips.add(_buildFilterChip(
        'Online Only',
        () => setState(() {
          _isOnline = null;
          _loadProviders();
        }),
      ));
    }
    
    return chips;
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label),
      onDeleted: onRemove,
      deleteIcon: const Icon(Icons.close, size: 16),
      backgroundColor: AppColors.primaryPurple.withValues(alpha: 0.1),
      labelStyle: TextStyle(
        color: AppColors.primaryPurple,
        fontSize: 12,
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedServiceType = widget.serviceType;
      _selectedRadius = widget.nearby ? 10.0 : null;
      _minRating = null;
      _isVerified = null;
      _isOnline = null;
    });
    _loadProviders();
  }

  Widget _buildProvidersList(List providers) {
    return RefreshIndicator(
      onRefresh: () async => _loadProviders(),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: providers.length,
        itemBuilder: (context, index) {
          final provider = providers[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: ServiceProviderCard(
              provider: provider,
              userLocation: widget.userLocation,
              onTap: () => _navigateToProviderDetails(provider.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadProviders,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.textLight.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No providers found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _clearFilters,
              child: const Text('Clear Filters'),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToProviderDetails(String providerId) {
    Navigator.pushNamed(
      context,
      '/provider-details',
      arguments: {'providerId': providerId},
    );
  }
}
