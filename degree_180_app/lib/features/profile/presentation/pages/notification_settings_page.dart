import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/services/cache_service.dart';
import '../../../../core/enums/notification_type.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_switch_tile.dart';
import '../widgets/settings_tile.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  final NotificationService _notificationService = NotificationService();
  final AnalyticsService _analytics = AnalyticsService();
  final CacheService _cache = CacheService();

  // Notification settings
  bool _notificationsEnabled = true;
  bool _pushNotificationsEnabled = true;
  bool _emailNotificationsEnabled = false;
  bool _smsNotificationsEnabled = true;
  
  // Booking notifications
  bool _bookingConfirmationEnabled = true;
  bool _bookingReminderEnabled = true;
  bool _bookingCancellationEnabled = true;
  bool _bookingReschedulingEnabled = true;
  bool _providerMessagesEnabled = true;
  
  // Marketing notifications
  bool _promotionalOffersEnabled = false;
  bool _newServicesEnabled = true;
  bool _weeklyDigestEnabled = false;
  bool _specialEventsEnabled = true;
  
  // System notifications
  bool _securityAlertsEnabled = true;
  bool _accountUpdatesEnabled = true;
  bool _appUpdatesEnabled = true;
  bool _maintenanceAlertsEnabled = true;
  
  // Notification timing
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 8, minute: 0);
  bool _quietHoursEnabled = true;
  
  // Advanced settings
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _ledEnabled = false;
  String _selectedSound = 'Default';
  int _reminderAdvanceTime = 60; // minutes

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _analytics.logScreenView('Notification Settings', 'NotificationSettingsPage');
  }

  Future<void> _loadSettings() async {
    // Load settings from cache
    _notificationsEnabled = await _cache.getSetting<bool>('notifications_enabled') ?? true;
    _pushNotificationsEnabled = await _cache.getSetting<bool>('push_notifications_enabled') ?? true;
    _emailNotificationsEnabled = await _cache.getSetting<bool>('email_notifications_enabled') ?? false;
    _smsNotificationsEnabled = await _cache.getSetting<bool>('sms_notifications_enabled') ?? true;
    
    _bookingConfirmationEnabled = await _cache.getSetting<bool>('booking_confirmation_enabled') ?? true;
    _bookingReminderEnabled = await _cache.getSetting<bool>('booking_reminder_enabled') ?? true;
    _bookingCancellationEnabled = await _cache.getSetting<bool>('booking_cancellation_enabled') ?? true;
    _bookingReschedulingEnabled = await _cache.getSetting<bool>('booking_rescheduling_enabled') ?? true;
    _providerMessagesEnabled = await _cache.getSetting<bool>('provider_messages_enabled') ?? true;
    
    _promotionalOffersEnabled = await _cache.getSetting<bool>('promotional_offers_enabled') ?? false;
    _newServicesEnabled = await _cache.getSetting<bool>('new_services_enabled') ?? true;
    _weeklyDigestEnabled = await _cache.getSetting<bool>('weekly_digest_enabled') ?? false;
    _specialEventsEnabled = await _cache.getSetting<bool>('special_events_enabled') ?? true;
    
    _securityAlertsEnabled = await _cache.getSetting<bool>('security_alerts_enabled') ?? true;
    _accountUpdatesEnabled = await _cache.getSetting<bool>('account_updates_enabled') ?? true;
    _appUpdatesEnabled = await _cache.getSetting<bool>('app_updates_enabled') ?? true;
    _maintenanceAlertsEnabled = await _cache.getSetting<bool>('maintenance_alerts_enabled') ?? true;
    
    _quietHoursEnabled = await _cache.getSetting<bool>('quiet_hours_enabled') ?? true;
    _soundEnabled = await _cache.getSetting<bool>('sound_enabled') ?? true;
    _vibrationEnabled = await _cache.getSetting<bool>('vibration_enabled') ?? true;
    _ledEnabled = await _cache.getSetting<bool>('led_enabled') ?? false;
    _selectedSound = await _cache.getSetting<String>('selected_sound') ?? 'Default';
    _reminderAdvanceTime = await _cache.getSetting<int>('reminder_advance_time') ?? 60;

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showNotificationHelp,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // Master notification toggle
            _buildMasterToggle(),
            
            const SizedBox(height: 20),
            
            // Notification channels
            if (_notificationsEnabled) ...[
              _buildNotificationChannels(),
              
              const SizedBox(height: 20),
              
              // Booking notifications
              _buildBookingNotifications(),
              
              const SizedBox(height: 20),
              
              // Marketing notifications
              _buildMarketingNotifications(),
              
              const SizedBox(height: 20),
              
              // System notifications
              _buildSystemNotifications(),
              
              const SizedBox(height: 20),
              
              // Quiet hours
              _buildQuietHours(),
              
              const SizedBox(height: 20),
              
              // Advanced settings
              _buildAdvancedSettings(),
              
              const SizedBox(height: 20),
              
              // Test notification
              _buildTestNotification(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMasterToggle() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  _notificationsEnabled ? Icons.notifications : Icons.notifications_off,
                  color: _notificationsEnabled ? AppColors.primaryPurple : AppColors.textLight,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notifications',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _notificationsEnabled 
                            ? 'You will receive notifications'
                            : 'All notifications are disabled',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textLight,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: _notificationsEnabled,
                  onChanged: (value) => _updateSetting('notifications_enabled', value, () {
                    setState(() => _notificationsEnabled = value);
                  }),
                  activeColor: AppColors.primaryPurple,
                ),
              ],
            ),
            
            if (!_notificationsEnabled) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning_amber, color: AppColors.warning, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'You won\'t receive important updates about your bookings',
                        style: TextStyle(color: AppColors.warning, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationChannels() {
    return SettingsSection(
      title: 'Notification Channels',
      icon: Icons.send,
      children: [
        SettingsSwitchTile(
          title: 'Push Notifications',
          subtitle: 'Receive notifications on your device',
          icon: Icons.phone_android,
          value: _pushNotificationsEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('push_notifications_enabled', value, () {
            setState(() => _pushNotificationsEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'Email Notifications',
          subtitle: 'Receive notifications via email',
          icon: Icons.email,
          value: _emailNotificationsEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('email_notifications_enabled', value, () {
            setState(() => _emailNotificationsEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'SMS Notifications',
          subtitle: 'Receive notifications via SMS',
          icon: Icons.sms,
          value: _smsNotificationsEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('sms_notifications_enabled', value, () {
            setState(() => _smsNotificationsEnabled = value);
          }),
        ),
      ],
    );
  }

  Widget _buildBookingNotifications() {
    return SettingsSection(
      title: 'Booking Notifications',
      icon: Icons.event,
      children: [
        SettingsSwitchTile(
          title: 'Booking Confirmations',
          subtitle: 'When your booking is confirmed',
          value: _bookingConfirmationEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('booking_confirmation_enabled', value, () {
            setState(() => _bookingConfirmationEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'Booking Reminders',
          subtitle: 'Reminders before your appointment',
          value: _bookingReminderEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('booking_reminder_enabled', value, () {
            setState(() => _bookingReminderEnabled = value);
          }),
        ),
        if (_bookingReminderEnabled && _notificationsEnabled)
          SettingsTile(
            title: 'Reminder Time',
            subtitle: '$_reminderAdvanceTime minutes before appointment',
            icon: Icons.schedule,
            onTap: _showReminderTimeSelector,
          ),
        SettingsSwitchTile(
          title: 'Cancellations',
          subtitle: 'When bookings are cancelled',
          value: _bookingCancellationEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('booking_cancellation_enabled', value, () {
            setState(() => _bookingCancellationEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'Rescheduling',
          subtitle: 'When bookings are rescheduled',
          value: _bookingReschedulingEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('booking_rescheduling_enabled', value, () {
            setState(() => _bookingReschedulingEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'Provider Messages',
          subtitle: 'Messages from service providers',
          value: _providerMessagesEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('provider_messages_enabled', value, () {
            setState(() => _providerMessagesEnabled = value);
          }),
        ),
      ],
    );
  }

  Widget _buildMarketingNotifications() {
    return SettingsSection(
      title: 'Marketing & Promotions',
      icon: Icons.local_offer,
      children: [
        SettingsSwitchTile(
          title: 'Promotional Offers',
          subtitle: 'Special deals and discounts',
          value: _promotionalOffersEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('promotional_offers_enabled', value, () {
            setState(() => _promotionalOffersEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'New Services',
          subtitle: 'When new services are available',
          value: _newServicesEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('new_services_enabled', value, () {
            setState(() => _newServicesEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'Weekly Digest',
          subtitle: 'Weekly summary of activities',
          value: _weeklyDigestEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('weekly_digest_enabled', value, () {
            setState(() => _weeklyDigestEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'Special Events',
          subtitle: 'Holiday specials and events',
          value: _specialEventsEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('special_events_enabled', value, () {
            setState(() => _specialEventsEnabled = value);
          }),
        ),
      ],
    );
  }

  Widget _buildSystemNotifications() {
    return SettingsSection(
      title: 'System Notifications',
      icon: Icons.settings,
      children: [
        SettingsSwitchTile(
          title: 'Security Alerts',
          subtitle: 'Important security notifications',
          value: _securityAlertsEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('security_alerts_enabled', value, () {
            setState(() => _securityAlertsEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'Account Updates',
          subtitle: 'Changes to your account',
          value: _accountUpdatesEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('account_updates_enabled', value, () {
            setState(() => _accountUpdatesEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'App Updates',
          subtitle: 'When app updates are available',
          value: _appUpdatesEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('app_updates_enabled', value, () {
            setState(() => _appUpdatesEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'Maintenance Alerts',
          subtitle: 'Scheduled maintenance notifications',
          value: _maintenanceAlertsEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('maintenance_alerts_enabled', value, () {
            setState(() => _maintenanceAlertsEnabled = value);
          }),
        ),
      ],
    );
  }

  Widget _buildQuietHours() {
    return SettingsSection(
      title: 'Quiet Hours',
      icon: Icons.bedtime,
      children: [
        SettingsSwitchTile(
          title: 'Enable Quiet Hours',
          subtitle: 'Silence notifications during specified hours',
          value: _quietHoursEnabled,
          enabled: _notificationsEnabled,
          onChanged: (value) => _updateSetting('quiet_hours_enabled', value, () {
            setState(() => _quietHoursEnabled = value);
          }),
        ),
        if (_quietHoursEnabled && _notificationsEnabled) ...[
          SettingsTile(
            title: 'Start Time',
            subtitle: _formatTimeOfDay(_quietHoursStart),
            icon: Icons.bedtime,
            onTap: () => _selectQuietHoursTime(true),
          ),
          SettingsTile(
            title: 'End Time',
            subtitle: _formatTimeOfDay(_quietHoursEnd),
            icon: Icons.wb_sunny,
            onTap: () => _selectQuietHoursTime(false),
          ),
        ],
      ],
    );
  }

  Widget _buildAdvancedSettings() {
    return SettingsSection(
      title: 'Advanced Settings',
      icon: Icons.tune,
      children: [
        SettingsSwitchTile(
          title: 'Sound',
          subtitle: 'Play notification sounds',
          icon: Icons.volume_up,
          value: _soundEnabled,
          enabled: _notificationsEnabled && _pushNotificationsEnabled,
          onChanged: (value) => _updateSetting('sound_enabled', value, () {
            setState(() => _soundEnabled = value);
          }),
        ),
        if (_soundEnabled && _notificationsEnabled && _pushNotificationsEnabled)
          SettingsTile(
            title: 'Notification Sound',
            subtitle: _selectedSound,
            icon: Icons.music_note,
            onTap: _showSoundSelector,
          ),
        SettingsSwitchTile(
          title: 'Vibration',
          subtitle: 'Vibrate on notifications',
          icon: Icons.vibration,
          value: _vibrationEnabled,
          enabled: _notificationsEnabled && _pushNotificationsEnabled,
          onChanged: (value) => _updateSetting('vibration_enabled', value, () {
            setState(() => _vibrationEnabled = value);
          }),
        ),
        SettingsSwitchTile(
          title: 'LED Light',
          subtitle: 'Flash LED for notifications',
          icon: Icons.lightbulb,
          value: _ledEnabled,
          enabled: _notificationsEnabled && _pushNotificationsEnabled,
          onChanged: (value) => _updateSetting('led_enabled', value, () {
            setState(() => _ledEnabled = value);
          }),
        ),
      ],
    );
  }

  Widget _buildTestNotification() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.notifications_active,
              color: AppColors.primaryPurple,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Test Notifications',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Send a test notification to verify your settings',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _notificationsEnabled ? _sendTestNotification : null,
                icon: const Icon(Icons.send),
                label: const Text('Send Test Notification'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryPurple,
                  foregroundColor: AppColors.textWhite,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateSetting(String key, dynamic value, VoidCallback updateUI) async {
    await _cache.saveSetting(key, value);
    updateUI();
    
    // Track setting change
    _analytics.logCustomEvent('notification_setting_changed', {
      'setting_key': key,
      'setting_value': value.toString(),
    });
  }

  void _showReminderTimeSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Reminder Time', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            ...([15, 30, 60, 120, 240]).map((minutes) => ListTile(
              title: Text('$minutes minutes before'),
              trailing: _reminderAdvanceTime == minutes ? const Icon(Icons.check, color: AppColors.primaryPurple) : null,
              onTap: () {
                _updateSetting('reminder_advance_time', minutes, () {
                  setState(() => _reminderAdvanceTime = minutes);
                });
                Navigator.pop(context);
              },
            )),
          ],
        ),
      ),
    );
  }

  void _showSoundSelector() {
    final sounds = ['Default', 'Chime', 'Bell', 'Ding', 'Notification'];
    
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Notification Sound', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            ...sounds.map((sound) => ListTile(
              title: Text(sound),
              trailing: _selectedSound == sound ? const Icon(Icons.check, color: AppColors.primaryPurple) : null,
              onTap: () {
                _updateSetting('selected_sound', sound, () {
                  setState(() => _selectedSound = sound);
                });
                Navigator.pop(context);
              },
            )),
          ],
        ),
      ),
    );
  }

  Future<void> _selectQuietHoursTime(bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _quietHoursStart : _quietHoursEnd,
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _quietHoursStart = picked;
        } else {
          _quietHoursEnd = picked;
        }
      });
      
      await _cache.saveSetting(
        isStartTime ? 'quiet_hours_start' : 'quiet_hours_end',
        '${picked.hour}:${picked.minute}',
      );
    }
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  Future<void> _sendTestNotification() async {
    try {
      await _notificationService.showNotification(
        title: 'Test Notification',
        body: 'This is a test notification from 180 Degree app',
        type: NotificationType.general.englishName,
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Test notification sent'),
          backgroundColor: AppColors.success,
        ),
      );
      
      _analytics.logCustomEvent('test_notification_sent', {});
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to send test notification'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _showNotificationHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('• Push notifications appear on your device'),
              SizedBox(height: 8),
              Text('• Email notifications are sent to your registered email'),
              SizedBox(height: 8),
              Text('• SMS notifications are sent to your phone number'),
              SizedBox(height: 8),
              Text('• Quiet hours prevent notifications during sleep'),
              SizedBox(height: 8),
              Text('• You can customize sounds and vibration patterns'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
