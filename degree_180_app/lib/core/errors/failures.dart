import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final String? code;

  const Failure({
    required this.message,
    this.code,
  });

  @override
  List<Object?> get props => [message, code];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
  });
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
  });
}

class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
  });
}

// Authentication failures
class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.code,
  });
}

class InvalidCredentialsFailure extends AuthFailure {
  const InvalidCredentialsFailure({
    super.message = 'Invalid email or password',
    super.code = 'invalid-credentials',
  });
}

class UserNotFoundFailure extends AuthFailure {
  const UserNotFoundFailure({
    super.message = 'User not found',
    super.code = 'user-not-found',
  });
}

class EmailAlreadyInUseFailure extends AuthFailure {
  const EmailAlreadyInUseFailure({
    super.message = 'Email is already in use',
    super.code = 'email-already-in-use',
  });
}

class WeakPasswordFailure extends AuthFailure {
  const WeakPasswordFailure({
    super.message = 'Password is too weak',
    super.code = 'weak-password',
  });
}

class InvalidEmailFailure extends AuthFailure {
  const InvalidEmailFailure({
    super.message = 'Invalid email address',
    super.code = 'invalid-email',
  });
}

class UserDisabledFailure extends AuthFailure {
  const UserDisabledFailure({
    super.message = 'User account has been disabled',
    super.code = 'user-disabled',
  });
}

class TooManyRequestsFailure extends AuthFailure {
  const TooManyRequestsFailure({
    super.message = 'Too many requests. Please try again later',
    super.code = 'too-many-requests',
  });
}

class OperationNotAllowedFailure extends AuthFailure {
  const OperationNotAllowedFailure({
    super.message = 'Operation not allowed',
    super.code = 'operation-not-allowed',
  });
}

// Validation failures
class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code = 'validation-error',
  });
}

// Permission failures
class PermissionFailure extends Failure {
  const PermissionFailure({
    required super.message,
    super.code = 'permission-denied',
  });
}

// Storage failures
class StorageFailure extends Failure {
  const StorageFailure({
    required super.message,
    super.code,
  });
}

class FileNotFoundFailure extends StorageFailure {
  const FileNotFoundFailure({
    super.message = 'File not found',
    super.code = 'file-not-found',
  });
}

class FileTooLargeFailure extends StorageFailure {
  const FileTooLargeFailure({
    super.message = 'File is too large',
    super.code = 'file-too-large',
  });
}

// Unknown failure
class UnknownFailure extends Failure {
  const UnknownFailure({
    super.message = 'An unknown error occurred',
    super.code = 'unknown-error',
  });
}
