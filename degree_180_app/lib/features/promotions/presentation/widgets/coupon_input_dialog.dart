import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class CouponInputDialog extends StatefulWidget {
  final Function(String) onCouponApplied;

  const CouponInputDialog({
    super.key,
    required this.onCouponApplied,
  });

  @override
  State<CouponInputDialog> createState() => _CouponInputDialogState();
}

class _CouponInputDialogState extends State<CouponInputDialog> {
  final TextEditingController _couponController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.confirmation_number,
            color: AppColors.primaryPurple,
            size: 24,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          const Text('Enter Coupon Code'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Enter your coupon code to apply the discount:',
            style: TextStyle(color: AppColors.textGray),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          TextField(
            controller: _couponController,
            textCapitalization: TextCapitalization.characters,
            decoration: InputDecoration(
              labelText: 'Coupon Code',
              hintText: 'e.g., WELCOME30',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                borderSide: const BorderSide(color: AppColors.primaryPurple),
              ),
              prefixIcon: Icon(
                Icons.confirmation_number,
                color: AppColors.primaryPurple,
              ),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _applyCoupon,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryPurple,
            foregroundColor: AppColors.textWhite,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.textWhite),
                  ),
                )
              : const Text('Apply'),
        ),
      ],
    );
  }

  Future<void> _applyCoupon() async {
    final couponCode = _couponController.text.trim().toUpperCase();
    
    if (couponCode.isEmpty) {
      _showError('Please enter a coupon code');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock validation
      final validCoupons = ['WELCOME30', 'WEEKEND', 'SPADAY20', 'SUMMER10', 'VIP25'];
      
      if (validCoupons.contains(couponCode)) {
        Navigator.of(context).pop();
        widget.onCouponApplied(couponCode);
      } else {
        _showError('Invalid coupon code. Please check and try again.');
      }
    } catch (e) {
      _showError('Failed to apply coupon. Please try again.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }
}
