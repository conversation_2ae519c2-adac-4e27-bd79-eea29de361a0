import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';
import '../analytics/performance_analytics.dart';
import '../services/memory_manager.dart';
import '../services/network_optimizer.dart';
import '../state/app_state_manager.dart';

/// صفحة تصحيح الأداء للمطورين
class PerformanceDebugPage extends StatefulWidget {
  const PerformanceDebugPage({super.key});

  @override
  State<PerformanceDebugPage> createState() => _PerformanceDebugPageState();
}

class _PerformanceDebugPageState extends State<PerformanceDebugPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  PerformanceReport? _currentReport;
  MemoryStats? _memoryStats;
  StateManagerStats? _stateStats;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final report = PerformanceAnalytics.instance.getCurrentReport();
    final memoryStats = await MemoryManager.instance.getMemoryStats();
    final stateStats = AppStateManager.instance.getStats();

    setState(() {
      _currentReport = report;
      _memoryStats = memoryStats;
      _stateStats = stateStats;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تصحيح الأداء',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
          IconButton(
            icon: const Icon(Icons.cleaning_services),
            onPressed: _performCleanup,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.textWhite,
          unselectedLabelColor: AppColors.textWhite.withValues(alpha: 0.7),
          indicatorColor: AppColors.primaryPurpleLight,
          tabs: const [
            Tab(text: 'الأداء'),
            Tab(text: 'الذاكرة'),
            Tab(text: 'الشبكة'),
            Tab(text: 'الحالة'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPerformanceTab(),
          _buildMemoryTab(),
          _buildNetworkTab(),
          _buildStateTab(),
        ],
      ),
    );
  }

  /// تبويب الأداء العام
  Widget _buildPerformanceTab() {
    if (_currentReport == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildInfoCard(
          'معلومات الأداء العامة',
          [
            _buildInfoRow('إجمالي المقاييس', '${_currentReport!.totalMetrics}'),
            _buildInfoRow('متوسط FPS', '${_currentReport!.averageFPS.toStringAsFixed(1)}'),
            _buildInfoRow('إسقاط الإطارات', '${_currentReport!.frameDropCount}'),
            _buildInfoRow('وقت التقرير', _formatDateTime(_currentReport!.timestamp)),
          ],
        ),
        
        const SizedBox(height: 16),
        
        _buildInfoCard(
          'إحصائيات العمليات',
          _currentReport!.operationStats.entries.map((entry) {
            final stats = entry.value;
            return ExpansionTile(
              title: Text(
                stats.operationName,
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              children: [
                _buildInfoRow('إجمالي الاستدعاءات', '${stats.totalCalls}'),
                _buildInfoRow('متوسط الوقت', '${stats.averageTimeMs.toStringAsFixed(1)}ms'),
                _buildInfoRow('أقل وقت', '${stats.minTimeMs.toStringAsFixed(1)}ms'),
                _buildInfoRow('أكبر وقت', '${stats.maxTimeMs.toStringAsFixed(1)}ms'),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }

  /// تبويب الذاكرة
  Widget _buildMemoryTab() {
    if (_memoryStats == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildInfoCard(
          'استخدام الذاكرة',
          [
            _buildInfoRow('الاستخدام الحالي', '${_memoryStats!.currentUsageMB}MB'),
            _buildInfoRow('أقصى استخدام', '${_memoryStats!.peakUsageMB}MB'),
            _buildInfoRow('الذاكرة المتاحة', '${_memoryStats!.availableMemoryMB}MB'),
            _buildInfoRow('إجمالي الذاكرة', '${_memoryStats!.totalMemoryMB}MB'),
            _buildInfoRow('نسبة الاستخدام', '${_memoryStats!.usagePercentage.toStringAsFixed(1)}%'),
          ],
        ),
        
        const SizedBox(height: 16),
        
        _buildInfoCard(
          'ذاكرة الصور',
          [
            _buildInfoRow('عدد الصور', '${_memoryStats!.imageCacheSize}'),
            _buildInfoRow('حجم الذاكرة', '${_memoryStats!.imageCacheSizeMB}MB'),
          ],
        ),
        
        const SizedBox(height: 16),
        
        _buildActionCard(
          'إدارة الذاكرة',
          [
            ElevatedButton(
              onPressed: () async {
                await MemoryManager.instance.manualCleanup();
                _loadData();
                _showSnackBar('تم تنظيف الذاكرة بنجاح');
              },
              child: const Text('تنظيف خفيف'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () async {
                await MemoryManager.instance.manualCleanup(aggressive: true);
                _loadData();
                _showSnackBar('تم التنظيف الشامل للذاكرة');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
              ),
              child: const Text('تنظيف شامل'),
            ),
          ],
        ),
      ],
    );
  }

  /// تبويب الشبكة
  Widget _buildNetworkTab() {
    if (_currentReport == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final networkStats = _currentReport!.networkStats;
    final optimizer = NetworkOptimizer.instance;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildInfoCard(
          'حالة الشبكة',
          [
            _buildInfoRow('نوع الاتصال', networkStats.connectionType),
            _buildInfoRow('جودة الشبكة', networkStats.networkQuality),
            _buildInfoRow('حالة الاتصال', networkStats.isConnected ? 'متصل' : 'غير متصل'),
          ],
        ),
        
        const SizedBox(height: 16),
        
        _buildInfoCard(
          'إعدادات الطلبات',
          () {
            final config = optimizer.getOptimizedRequestConfig('default');
            return [
              _buildInfoRow('مهلة الاتصال', '${config.timeout.inSeconds}s'),
              _buildInfoRow('عدد المحاولات', '${config.retryCount}'),
              _buildInfoRow('تأخير المحاولة', '${config.retryDelay.inSeconds}s'),
              _buildInfoRow('ضغط البيانات', config.compressionEnabled ? 'مفعل' : 'معطل'),
              _buildInfoRow('التخزين المؤقت', config.cacheEnabled ? 'مفعل' : 'معطل'),
            ];
          }(),
        ),
      ],
    );
  }

  /// تبويب إدارة الحالة
  Widget _buildStateTab() {
    if (_stateStats == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildInfoCard(
          'إحصائيات الحالة',
          [
            _buildInfoRow('إجمالي الحالات', '${_stateStats!.totalStates}'),
            _buildInfoRow('Streams النشطة', '${_stateStats!.activeStreams}'),
            _buildInfoRow('الحفظ المعلق', '${_stateStats!.pendingPersistence}'),
            _buildInfoRow('استخدام الذاكرة', '${_stateStats!.memoryUsageKB}KB'),
          ],
        ),
        
        const SizedBox(height: 16),
        
        _buildActionCard(
          'إدارة الحالة',
          [
            ElevatedButton(
              onPressed: () async {
                await AppStateManager.instance.persistAllPendingStates();
                _loadData();
                _showSnackBar('تم حفظ جميع الحالات المعلقة');
              },
              child: const Text('حفظ الحالات'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                final exportedState = AppStateManager.instance.exportStateForDebug();
                _showStateDialog(exportedState);
              },
              child: const Text('عرض الحالة'),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة معلومات
  Widget _buildInfoCard(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryPurple,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إجراءات
  Widget _buildActionCard(String title, List<Widget> actions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryPurple,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: actions,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w600,
              color: AppColors.textDark,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.cairo(
              color: AppColors.textGray,
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// تنظيف شامل
  Future<void> _performCleanup() async {
    await MemoryManager.instance.manualCleanup(aggressive: true);
    PerformanceAnalytics.instance.clearOldMetrics();
    await AppStateManager.instance.persistAllPendingStates();
    
    _loadData();
    _showSnackBar('تم التنظيف الشامل بنجاح');
  }

  /// عرض رسالة
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: AppColors.success,
      ),
    );
  }

  /// عرض حوار الحالة
  void _showStateDialog(Map<String, dynamic> state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'حالة التطبيق',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView(
            children: state.entries.map((entry) {
              return ListTile(
                title: Text(
                  entry.key,
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                subtitle: Text(
                  entry.value.toString(),
                  style: GoogleFonts.cairo(),
                ),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(),
            ),
          ),
        ],
      ),
    );
  }
}
