import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';

class ProfileHeader extends StatelessWidget {
  final String name;
  final String email;
  final String userType;
  final double? rating;
  final int? reviewCount;
  final String? imageUrl;

  const ProfileHeader({
    super.key,
    required this.name,
    required this.email,
    required this.userType,
    this.rating,
    this.reviewCount,
    this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Profile Image
          Stack(
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: AppColors.buttonWhite,
                  borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowMedium,
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
                        child: Image.network(
                          imageUrl!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : const Icon(
                        Icons.person,
                        color: AppColors.primaryPurple,
                        size: 50,
                      ),
              ),
              
              // Edit Button
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.primaryPurple,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowMedium,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.edit,
                    color: AppColors.textWhite,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // User Name
          Text(
            name,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.textWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // Email
          Text(
            email,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textWhite.withValues(alpha: 0.8),
            ),
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // User Type Badge
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.smallPadding,
            ),
            decoration: BoxDecoration(
              color: AppColors.buttonWhite.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
            ),
            child: Text(
              userType,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // Rating (for service providers)
          if (rating != null && reviewCount != null) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: AppConstants.smallPadding,
              ),
              decoration: BoxDecoration(
                color: AppColors.buttonWhite.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.star,
                    color: AppColors.starYellow,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    rating!.toString(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '(${reviewCount!} ${AppStrings.reviews})',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textWhite.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // Default Profile Badge
          Text(
            AppStrings.defaultProfile,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textWhite.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}
