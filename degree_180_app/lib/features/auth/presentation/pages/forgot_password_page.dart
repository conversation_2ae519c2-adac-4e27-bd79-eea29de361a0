import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/auth_header.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundWhite,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header with gradient background
            AuthHeader(
              title: _emailSent ? 'تم الإرسال!' : AppStrings.resetPassword,
              subtitle: _emailSent 
                  ? 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'
                  : 'أدخل بريدك الإلكتروني لإرسال رابط إعادة تعيين كلمة المرور',
              showLogo: true,
              height: MediaQuery.of(context).size.height * 0.35,
            ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  if (!_emailSent) ...[
                    // Reset Password Form
                    Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // Email Field
                          CustomTextField(
                            controller: _emailController,
                            hintText: AppStrings.email,
                            keyboardType: TextInputType.emailAddress,
                            validator: _validateEmail,
                            prefixIcon: const Icon(
                              Icons.email_outlined,
                              color: AppColors.textGray,
                            ),
                          ),
                          
                          const SizedBox(height: AppConstants.extraLargePadding),
                          
                          // Send Reset Link Button
                          CustomButton(
                            text: AppStrings.sendResetLink,
                            onPressed: _isLoading ? null : _handleSendResetLink,
                            isLoading: _isLoading,
                            backgroundColor: AppColors.primaryPurple,
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    // Success State
                    _buildSuccessState(),
                  ],
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Back to Login Button
                  TextButton(
                    onPressed: () => context.pop(),
                    child: Text(
                      AppStrings.backToLogin,
                      style: GoogleFonts.cairo(
                        color: AppColors.primaryPurple,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessState() {
    return Column(
      children: [
        // Success Icon
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(50),
          ),
          child: const Icon(
            Icons.check_circle_outline,
            size: 60,
            color: Colors.green,
          ),
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        // Success Message
        Text(
          'تحقق من بريدك الإلكتروني',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          'لقد أرسلنا رابط إعادة تعيين كلمة المرور إلى:',
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: AppColors.textGray,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          _emailController.text,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.primaryPurple,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        // Resend Button
        TextButton(
          onPressed: _isLoading ? null : _handleResendEmail,
          child: Text(
            'إعادة إرسال الرابط',
            style: GoogleFonts.cairo(
              color: AppColors.textGray,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        // Instructions
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.backgroundGray,
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'لم تجد الرسالة؟',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDark,
                ),
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                '• تحقق من مجلد الرسائل غير المرغوب فيها\n• تأكد من صحة عنوان البريد الإلكتروني\n• قد تستغرق الرسالة بضع دقائق للوصول',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: AppColors.textGray,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.emailRequired;
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return AppStrings.invalidEmail;
    }
    return null;
  }

  Future<void> _handleSendResetLink() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate sending reset link
      await Future.delayed(const Duration(seconds: 2));
      
      setState(() {
        _emailSent = true;
      });
      
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء إرسال الرابط. يرجى المحاولة مرة أخرى.',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleResendEmail() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate resending email
      await Future.delayed(const Duration(seconds: 1));
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إعادة إرسال الرابط بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء إعادة الإرسال',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
