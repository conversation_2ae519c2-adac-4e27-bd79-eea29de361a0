import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

enum NotificationType {
  booking,
  reminder,
  promotion,
  review,
  payment,
  general,
  system,
  chat,
}

class NotificationModel extends Equatable {
  final String id;
  final String userId;
  final String title;
  final String message;
  final NotificationType type;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? actionUrl;
  final int priority; // 1-5, 5 being highest
  final DateTime? expiresAt;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    this.isRead = false,
    required this.createdAt,
    this.readAt,
    this.data,
    this.imageUrl,
    this.actionUrl,
    this.priority = 3,
    this.expiresAt,
  });

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? message,
    NotificationType? type,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    int? priority,
    DateTime? expiresAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      priority: priority ?? this.priority,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'message': message,
      'type': type.name,
      'isRead': isRead,
      'createdAt': Timestamp.fromDate(createdAt),
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'data': data,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'priority': priority,
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
    };
  }

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      type: NotificationType.values.firstWhere(
        (type) => type.name == map['type'],
        orElse: () => NotificationType.general,
      ),
      isRead: map['isRead'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      readAt: (map['readAt'] as Timestamp?)?.toDate(),
      data: map['data'] != null ? Map<String, dynamic>.from(map['data']) : null,
      imageUrl: map['imageUrl'],
      actionUrl: map['actionUrl'],
      priority: map['priority'] ?? 3,
      expiresAt: (map['expiresAt'] as Timestamp?)?.toDate(),
    );
  }

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel.fromMap({...data, 'id': doc.id});
  }

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  bool get isHighPriority => priority >= 4;

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        message,
        type,
        isRead,
        createdAt,
        readAt,
        data,
        imageUrl,
        actionUrl,
        priority,
        expiresAt,
      ];
}

// Factory methods for common notification types
extension NotificationFactory on NotificationModel {
  static NotificationModel bookingConfirmation({
    required String userId,
    required String bookingId,
    required String providerName,
    required DateTime appointmentTime,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'Booking Confirmed',
      message: 'Your appointment at $providerName has been confirmed for ${_formatDateTime(appointmentTime)}.',
      type: NotificationType.booking,
      createdAt: DateTime.now(),
      priority: 4,
      data: {
        'bookingId': bookingId,
        'providerName': providerName,
        'appointmentTime': appointmentTime.toIso8601String(),
      },
      actionUrl: '/booking-details/$bookingId',
    );
  }

  static NotificationModel bookingReminder({
    required String userId,
    required String bookingId,
    required String providerName,
    required DateTime appointmentTime,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'Appointment Reminder',
      message: 'Don\'t forget your appointment at $providerName in 1 hour.',
      type: NotificationType.reminder,
      createdAt: DateTime.now(),
      priority: 5,
      data: {
        'bookingId': bookingId,
        'providerName': providerName,
        'appointmentTime': appointmentTime.toIso8601String(),
      },
      actionUrl: '/booking-details/$bookingId',
    );
  }

  static NotificationModel promotionOffer({
    required String userId,
    required String title,
    required String message,
    String? imageUrl,
    String? promoCode,
    DateTime? expiresAt,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: title,
      message: message,
      type: NotificationType.promotion,
      createdAt: DateTime.now(),
      priority: 3,
      imageUrl: imageUrl,
      data: promoCode != null ? {'promoCode': promoCode} : null,
      actionUrl: '/promotions',
      expiresAt: expiresAt,
    );
  }

  static NotificationModel reviewRequest({
    required String userId,
    required String bookingId,
    required String providerName,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'Review Request',
      message: 'How was your experience at $providerName? Please leave a review.',
      type: NotificationType.review,
      createdAt: DateTime.now(),
      priority: 2,
      data: {
        'bookingId': bookingId,
        'providerName': providerName,
      },
      actionUrl: '/write-review/$bookingId',
    );
  }

  static NotificationModel paymentSuccess({
    required String userId,
    required String paymentId,
    required double amount,
    required String currency,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'Payment Successful',
      message: 'Payment of $amount $currency has been processed successfully.',
      type: NotificationType.payment,
      createdAt: DateTime.now(),
      priority: 3,
      data: {
        'paymentId': paymentId,
        'amount': amount,
        'currency': currency,
      },
      actionUrl: '/payment-history',
    );
  }

  static NotificationModel newMessage({
    required String userId,
    required String chatId,
    required String senderName,
    required String messagePreview,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'New Message from $senderName',
      message: messagePreview,
      type: NotificationType.chat,
      createdAt: DateTime.now(),
      priority: 4,
      data: {
        'chatId': chatId,
        'senderName': senderName,
      },
      actionUrl: '/chat/$chatId',
    );
  }

  static String _formatDateTime(DateTime dateTime) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    final month = months[dateTime.month - 1];
    final day = dateTime.day;
    final hour = dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '$month $day at $displayHour:$minute $period';
  }
}
