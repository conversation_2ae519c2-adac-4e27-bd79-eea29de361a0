import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class ServiceTypeChips extends StatelessWidget {
  final String selectedType;
  final Function(String) onTypeSelected;

  const ServiceTypeChips({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final serviceTypes = [
      {'id': 'all', 'label': 'All Services'},
      {'id': 'salon', 'label': 'Salon'},
      {'id': 'barbershop', 'label': 'Barbershop'},
      {'id': 'spa', 'label': 'Spa'},
      {'id': 'nails', 'label': 'Nails'},
      {'id': 'massage', 'label': 'Massage'},
      {'id': 'skincare', 'label': 'Skincare'},
    ];

    return Wrap(
      spacing: AppConstants.smallPadding,
      runSpacing: AppConstants.smallPadding,
      children: serviceTypes.map((type) {
        final isSelected = selectedType == type['id'];
        return _buildServiceChip(
          label: type['label']!,
          isSelected: isSelected,
          onTap: () => onTypeSelected(type['id']!),
        );
      }).toList(),
    );
  }

  Widget _buildServiceChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primaryPurple : AppColors.backgroundGray,
            borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
            border: Border.all(
              color: isSelected ? AppColors.primaryPurple : AppColors.borderGray,
            ),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: isSelected ? AppColors.textWhite : AppColors.textDark,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
        ),
      ),
    );
  }
}
