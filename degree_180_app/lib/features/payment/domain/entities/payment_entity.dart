import 'package:equatable/equatable.dart';

enum PaymentMethod {
  creditCard,
  debitCard,
  applePay,
  googlePay,
  paypal,
  cash,
  wallet,
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
  partiallyRefunded,
}

enum Currency {
  kwd, // Kuwaiti Dinar
  usd, // US Dollar
  eur, // Euro
  sar, // Saudi Riyal
}

class PaymentEntity extends Equatable {
  final String id;
  final String bookingId;
  final String customerId;
  final String providerId;
  final double amount;
  final double? taxAmount;
  final double? discountAmount;
  final double? tipAmount;
  final double totalAmount;
  final Currency currency;
  final PaymentMethod method;
  final PaymentStatus status;
  final String? transactionId;
  final String? paymentIntentId;
  final String? description;
  final DateTime createdAt;
  final DateTime? processedAt;
  final DateTime? completedAt;
  final String? failureReason;
  final Map<String, dynamic>? metadata;

  const PaymentEntity({
    required this.id,
    required this.bookingId,
    required this.customerId,
    required this.providerId,
    required this.amount,
    this.taxAmount,
    this.discountAmount,
    this.tipAmount,
    required this.totalAmount,
    this.currency = Currency.kwd,
    required this.method,
    required this.status,
    this.transactionId,
    this.paymentIntentId,
    this.description,
    required this.createdAt,
    this.processedAt,
    this.completedAt,
    this.failureReason,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        bookingId,
        customerId,
        providerId,
        amount,
        taxAmount,
        discountAmount,
        tipAmount,
        totalAmount,
        currency,
        method,
        status,
        transactionId,
        paymentIntentId,
        description,
        createdAt,
        processedAt,
        completedAt,
        failureReason,
        metadata,
      ];

  PaymentEntity copyWith({
    String? id,
    String? bookingId,
    String? customerId,
    String? providerId,
    double? amount,
    double? taxAmount,
    double? discountAmount,
    double? tipAmount,
    double? totalAmount,
    Currency? currency,
    PaymentMethod? method,
    PaymentStatus? status,
    String? transactionId,
    String? paymentIntentId,
    String? description,
    DateTime? createdAt,
    DateTime? processedAt,
    DateTime? completedAt,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentEntity(
      id: id ?? this.id,
      bookingId: bookingId ?? this.bookingId,
      customerId: customerId ?? this.customerId,
      providerId: providerId ?? this.providerId,
      amount: amount ?? this.amount,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      tipAmount: tipAmount ?? this.tipAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      method: method ?? this.method,
      status: status ?? this.status,
      transactionId: transactionId ?? this.transactionId,
      paymentIntentId: paymentIntentId ?? this.paymentIntentId,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      processedAt: processedAt ?? this.processedAt,
      completedAt: completedAt ?? this.completedAt,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }

  String get methodString {
    switch (method) {
      case PaymentMethod.creditCard:
        return 'credit_card';
      case PaymentMethod.debitCard:
        return 'debit_card';
      case PaymentMethod.applePay:
        return 'apple_pay';
      case PaymentMethod.googlePay:
        return 'google_pay';
      case PaymentMethod.paypal:
        return 'paypal';
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.wallet:
        return 'wallet';
    }
  }

  String get statusString {
    switch (status) {
      case PaymentStatus.pending:
        return 'pending';
      case PaymentStatus.processing:
        return 'processing';
      case PaymentStatus.completed:
        return 'completed';
      case PaymentStatus.failed:
        return 'failed';
      case PaymentStatus.cancelled:
        return 'cancelled';
      case PaymentStatus.refunded:
        return 'refunded';
      case PaymentStatus.partiallyRefunded:
        return 'partially_refunded';
    }
  }

  String get currencyString {
    switch (currency) {
      case Currency.kwd:
        return 'KWD';
      case Currency.usd:
        return 'USD';
      case Currency.eur:
        return 'EUR';
      case Currency.sar:
        return 'SAR';
    }
  }

  String get currencySymbol {
    switch (currency) {
      case Currency.kwd:
        return 'KD';
      case Currency.usd:
        return '\$';
      case Currency.eur:
        return '€';
      case Currency.sar:
        return 'SR';
    }
  }

  bool get isPending => status == PaymentStatus.pending;
  bool get isProcessing => status == PaymentStatus.processing;
  bool get isCompleted => status == PaymentStatus.completed;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isCancelled => status == PaymentStatus.cancelled;
  bool get isRefunded => status == PaymentStatus.refunded || status == PaymentStatus.partiallyRefunded;

  bool get canBeRefunded => isCompleted && !isRefunded;
  bool get canBeCancelled => isPending || isProcessing;

  String get formattedAmount => '${amount.toStringAsFixed(2)} $currencySymbol';
  String get formattedTotalAmount => '${totalAmount.toStringAsFixed(2)} $currencySymbol';

  double get netAmount => amount - (discountAmount ?? 0);
  double get finalAmount => netAmount + (taxAmount ?? 0) + (tipAmount ?? 0);
}
