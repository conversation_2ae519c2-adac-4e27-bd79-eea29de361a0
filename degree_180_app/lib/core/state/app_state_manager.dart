import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/memory_manager.dart';
import '../services/network_optimizer.dart';

/// نظام إدارة الحالة المحسن للتطبيق
class AppStateManager {
  static AppStateManager? _instance;
  static AppStateManager get instance => _instance ??= AppStateManager._();
  
  AppStateManager._();

  final Map<String, dynamic> _globalState = {};
  final Map<String, StreamController<dynamic>> _stateStreams = {};
  final Map<String, Timer> _persistenceTimers = {};
  
  bool _isInitialized = false;
  SharedPreferences? _prefs;

  // إعدادات الحفظ التلقائي
  static const Duration _autoPersistDelay = Duration(seconds: 2);
  static const String _stateKeyPrefix = 'app_state_';

  /// تهيئة نظام إدارة الحالة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      
      // استعادة الحالة المحفوظة
      await _restorePersistedState();
      
      // تسجيل callbacks إدارة الذاكرة
      _registerMemoryCallbacks();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('AppStateManager: تم تهيئة نظام إدارة الحالة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppStateManager: خطأ في التهيئة: $e');
      }
    }
  }

  /// تسجيل callbacks إدارة الذاكرة
  void _registerMemoryCallbacks() {
    MemoryManager.instance.registerMemoryWarningCallback(() async {
      await _cleanupUnusedStreams();
    });

    MemoryManager.instance.registerLowMemoryCallback(() async {
      await _aggressiveCleanup();
    });
  }

  /// استعادة الحالة المحفوظة
  Future<void> _restorePersistedState() async {
    if (_prefs == null) return;

    try {
      final keys = _prefs!.getKeys()
          .where((key) => key.startsWith(_stateKeyPrefix));

      for (String key in keys) {
        final stateKey = key.substring(_stateKeyPrefix.length);
        final value = _prefs!.get(key);
        
        if (value != null) {
          _globalState[stateKey] = value;
          
          if (kDebugMode) {
            print('AppStateManager: استعادة حالة: $stateKey');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppStateManager: خطأ في استعادة الحالة: $e');
      }
    }
  }

  /// تحديث حالة عامة
  void updateState<T>(String key, T value, {bool persist = false}) {
    final oldValue = _globalState[key];
    
    if (oldValue != value) {
      _globalState[key] = value;
      
      // إشعار المستمعين
      _notifyStateChange(key, value);
      
      // حفظ تلقائي إذا كان مطلوباً
      if (persist) {
        _schedulePersistence(key, value);
      }
      
      if (kDebugMode) {
        print('AppStateManager: تحديث حالة: $key = $value');
      }
    }
  }

  /// الحصول على حالة
  T? getState<T>(String key) {
    return _globalState[key] as T?;
  }

  /// الحصول على stream للحالة
  Stream<T> getStateStream<T>(String key) {
    if (!_stateStreams.containsKey(key)) {
      _stateStreams[key] = StreamController<T>.broadcast();
    }
    
    return _stateStreams[key]!.stream.cast<T>();
  }

  /// إشعار تغيير الحالة
  void _notifyStateChange(String key, dynamic value) {
    final controller = _stateStreams[key];
    if (controller != null && !controller.isClosed) {
      controller.add(value);
    }
  }

  /// جدولة حفظ الحالة
  void _schedulePersistence(String key, dynamic value) {
    // إلغاء المؤقت السابق إن وجد
    _persistenceTimers[key]?.cancel();
    
    // جدولة حفظ جديد
    _persistenceTimers[key] = Timer(_autoPersistDelay, () async {
      await _persistState(key, value);
      _persistenceTimers.remove(key);
    });
  }

  /// حفظ حالة محددة
  Future<void> _persistState(String key, dynamic value) async {
    if (_prefs == null) return;

    try {
      final prefKey = '$_stateKeyPrefix$key';
      
      if (value is String) {
        await _prefs!.setString(prefKey, value);
      } else if (value is int) {
        await _prefs!.setInt(prefKey, value);
      } else if (value is double) {
        await _prefs!.setDouble(prefKey, value);
      } else if (value is bool) {
        await _prefs!.setBool(prefKey, value);
      } else if (value is List<String>) {
        await _prefs!.setStringList(prefKey, value);
      } else {
        // للأنواع المعقدة، تحويل إلى String
        await _prefs!.setString(prefKey, value.toString());
      }
      
      if (kDebugMode) {
        print('AppStateManager: حفظ حالة: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppStateManager: خطأ في حفظ الحالة: $e');
      }
    }
  }

  /// حذف حالة
  void removeState(String key, {bool removePersisted = false}) {
    _globalState.remove(key);
    
    // إغلاق stream إن وجد
    final controller = _stateStreams[key];
    if (controller != null) {
      controller.close();
      _stateStreams.remove(key);
    }
    
    // إلغاء مؤقت الحفظ
    _persistenceTimers[key]?.cancel();
    _persistenceTimers.remove(key);
    
    // حذف من التخزين المستمر
    if (removePersisted && _prefs != null) {
      _prefs!.remove('$_stateKeyPrefix$key');
    }
    
    if (kDebugMode) {
      print('AppStateManager: حذف حالة: $key');
    }
  }

  /// تنظيف streams غير المستخدمة
  Future<void> _cleanupUnusedStreams() async {
    final keysToRemove = <String>[];
    
    for (final entry in _stateStreams.entries) {
      if (!entry.value.hasListener) {
        keysToRemove.add(entry.key);
      }
    }
    
    for (final key in keysToRemove) {
      _stateStreams[key]?.close();
      _stateStreams.remove(key);
    }
    
    if (kDebugMode && keysToRemove.isNotEmpty) {
      print('AppStateManager: تنظيف ${keysToRemove.length} streams غير مستخدمة');
    }
  }

  /// تنظيف شامل للذاكرة
  Future<void> _aggressiveCleanup() async {
    // تنظيف جميع streams
    for (final controller in _stateStreams.values) {
      controller.close();
    }
    _stateStreams.clear();
    
    // إلغاء جميع مؤقتات الحفظ
    for (final timer in _persistenceTimers.values) {
      timer.cancel();
    }
    _persistenceTimers.clear();
    
    // حفظ الحالة الحرجة فقط
    await _persistCriticalState();
    
    if (kDebugMode) {
      print('AppStateManager: تنظيف شامل للذاكرة');
    }
  }

  /// حفظ الحالة الحرجة فقط
  Future<void> _persistCriticalState() async {
    final criticalKeys = [
      'user_preferences',
      'auth_token',
      'selected_language',
      'theme_mode',
    ];
    
    for (final key in criticalKeys) {
      final value = _globalState[key];
      if (value != null) {
        await _persistState(key, value);
      }
    }
  }

  /// حفظ جميع الحالات المعلقة
  Future<void> persistAllPendingStates() async {
    final futures = <Future>[];
    
    for (final entry in _persistenceTimers.entries) {
      entry.value.cancel();
      final value = _globalState[entry.key];
      if (value != null) {
        futures.add(_persistState(entry.key, value));
      }
    }
    
    _persistenceTimers.clear();
    await Future.wait(futures);
    
    if (kDebugMode) {
      print('AppStateManager: حفظ جميع الحالات المعلقة');
    }
  }

  /// الحصول على إحصائيات الحالة
  StateManagerStats getStats() {
    return StateManagerStats(
      totalStates: _globalState.length,
      activeStreams: _stateStreams.length,
      pendingPersistence: _persistenceTimers.length,
      memoryUsageKB: _estimateMemoryUsage(),
    );
  }

  /// تقدير استخدام الذاكرة
  int _estimateMemoryUsage() {
    int totalSize = 0;
    
    for (final value in _globalState.values) {
      if (value is String) {
        totalSize += value.length * 2; // UTF-16
      } else if (value is List) {
        totalSize += value.length * 8; // تقدير تقريبي
      } else {
        totalSize += 8; // primitive types
      }
    }
    
    return totalSize ~/ 1024; // تحويل إلى KB
  }

  /// تصدير الحالة للتصحيح
  Map<String, dynamic> exportStateForDebug() {
    return Map.from(_globalState);
  }

  /// استيراد حالة للتصحيح
  void importStateForDebug(Map<String, dynamic> state) {
    _globalState.clear();
    _globalState.addAll(state);
    
    // إشعار جميع المستمعين
    for (final entry in state.entries) {
      _notifyStateChange(entry.key, entry.value);
    }
  }

  /// إيقاف نظام إدارة الحالة
  Future<void> dispose() async {
    // حفظ جميع الحالات المعلقة
    await persistAllPendingStates();
    
    // إغلاق جميع streams
    for (final controller in _stateStreams.values) {
      controller.close();
    }
    _stateStreams.clear();
    
    // إلغاء جميع المؤقتات
    for (final timer in _persistenceTimers.values) {
      timer.cancel();
    }
    _persistenceTimers.clear();
    
    _globalState.clear();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('AppStateManager: تم إيقاف نظام إدارة الحالة');
    }
  }
}

/// إحصائيات نظام إدارة الحالة
class StateManagerStats {
  final int totalStates;
  final int activeStreams;
  final int pendingPersistence;
  final int memoryUsageKB;

  const StateManagerStats({
    required this.totalStates,
    required this.activeStreams,
    required this.pendingPersistence,
    required this.memoryUsageKB,
  });

  @override
  String toString() {
    return 'StateManagerStats('
           'states: $totalStates, '
           'streams: $activeStreams, '
           'pending: $pendingPersistence, '
           'memory: ${memoryUsageKB}KB)';
  }
}
