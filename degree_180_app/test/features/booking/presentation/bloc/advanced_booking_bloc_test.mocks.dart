// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in degree_180_app/test/features/booking/presentation/bloc/advanced_booking_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:degree_180_app/core/errors/failures.dart' as _i5;
import 'package:degree_180_app/core/models/booking_model.dart' as _i7;
import 'package:degree_180_app/core/models/time_slot_model.dart' as _i6;
import 'package:degree_180_app/features/booking/domain/usecases/create_advanced_booking_usecase.dart'
    as _i8;
import 'package:degree_180_app/features/booking/domain/usecases/get_available_time_slots_usecase.dart'
    as _i9;
import 'package:degree_180_app/features/booking/domain/usecases/get_booking_statistics_usecase.dart'
    as _i10;
import 'package:degree_180_app/features/booking/domain/usecases/reschedule_booking_usecase.dart'
    as _i11;
import 'package:degree_180_app/features/booking/presentation/bloc/advanced_booking_state.dart'
    as _i12;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [GetAvailableTimeSlotsUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetAvailableTimeSlotsUseCase extends _i1.Mock
    implements _i9.GetAvailableTimeSlotsUseCase {
  MockGetAvailableTimeSlotsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.TimeSlotModel>>> call(
          _i9.GetAvailableTimeSlotsParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue:
            _i4.Future<_i2.Either<_i5.Failure, List<_i6.TimeSlotModel>>>.value(
                _FakeEither_0<_i5.Failure, List<_i6.TimeSlotModel>>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, List<_i6.TimeSlotModel>>>);
}

/// A class which mocks [CreateAdvancedBookingUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockCreateAdvancedBookingUseCase extends _i1.Mock
    implements _i8.CreateAdvancedBookingUseCase {
  MockCreateAdvancedBookingUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.BookingModel>> call(
          _i8.CreateAdvancedBookingParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.Failure, _i7.BookingModel>>.value(
            _FakeEither_0<_i5.Failure, _i7.BookingModel>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, _i7.BookingModel>>);
}

/// A class which mocks [RescheduleBookingUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockRescheduleBookingUseCase extends _i1.Mock
    implements _i11.RescheduleBookingUseCase {
  MockRescheduleBookingUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.BookingModel>> call(
          _i11.RescheduleBookingParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.Failure, _i7.BookingModel>>.value(
            _FakeEither_0<_i5.Failure, _i7.BookingModel>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, _i7.BookingModel>>);
}

/// A class which mocks [GetBookingStatisticsUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetBookingStatisticsUseCase extends _i1.Mock
    implements _i10.GetBookingStatisticsUseCase {
  MockGetBookingStatisticsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i12.BookingStatistics>> call(
          _i10.GetBookingStatisticsParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue:
            _i4.Future<_i2.Either<_i5.Failure, _i12.BookingStatistics>>.value(
                _FakeEither_0<_i5.Failure, _i12.BookingStatistics>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, _i12.BookingStatistics>>);
}
