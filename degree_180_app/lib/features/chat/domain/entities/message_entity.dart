import 'package:equatable/equatable.dart';

enum MessageType {
  text,
  image,
  audio,
  video,
  file,
  location,
  booking,
  system,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

class MessageEntity extends Equatable {
  final String id;
  final String chatId;
  final String senderId;
  final String? receiverId;
  final MessageType type;
  final String content;
  final String? mediaUrl;
  final String? thumbnailUrl;
  final MessageStatus status;
  final DateTime timestamp;
  final DateTime? editedAt;
  final String? replyToMessageId;
  final bool isDeleted;
  final Map<String, dynamic>? metadata;

  const MessageEntity({
    required this.id,
    required this.chatId,
    required this.senderId,
    this.receiverId,
    required this.type,
    required this.content,
    this.mediaUrl,
    this.thumbnailUrl,
    required this.status,
    required this.timestamp,
    this.editedAt,
    this.replyToMessageId,
    this.isDeleted = false,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        chatId,
        senderId,
        receiverId,
        type,
        content,
        mediaUrl,
        thumbnailUrl,
        status,
        timestamp,
        editedAt,
        replyToMessageId,
        isDeleted,
        metadata,
      ];

  MessageEntity copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? receiverId,
    MessageType? type,
    String? content,
    String? mediaUrl,
    String? thumbnailUrl,
    MessageStatus? status,
    DateTime? timestamp,
    DateTime? editedAt,
    String? replyToMessageId,
    bool? isDeleted,
    Map<String, dynamic>? metadata,
  }) {
    return MessageEntity(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      type: type ?? this.type,
      content: content ?? this.content,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      editedAt: editedAt ?? this.editedAt,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      isDeleted: isDeleted ?? this.isDeleted,
      metadata: metadata ?? this.metadata,
    );
  }

  String get typeString {
    switch (type) {
      case MessageType.text:
        return 'text';
      case MessageType.image:
        return 'image';
      case MessageType.audio:
        return 'audio';
      case MessageType.video:
        return 'video';
      case MessageType.file:
        return 'file';
      case MessageType.location:
        return 'location';
      case MessageType.booking:
        return 'booking';
      case MessageType.system:
        return 'system';
    }
  }

  String get statusString {
    switch (status) {
      case MessageStatus.sending:
        return 'sending';
      case MessageStatus.sent:
        return 'sent';
      case MessageStatus.delivered:
        return 'delivered';
      case MessageStatus.read:
        return 'read';
      case MessageStatus.failed:
        return 'failed';
    }
  }

  bool get isText => type == MessageType.text;
  bool get isMedia => type == MessageType.image || type == MessageType.video;
  bool get isAudio => type == MessageType.audio;
  bool get isFile => type == MessageType.file;
  bool get isLocation => type == MessageType.location;
  bool get isBooking => type == MessageType.booking;
  bool get isSystem => type == MessageType.system;

  bool get hasMedia => mediaUrl != null && mediaUrl!.isNotEmpty;
  bool get hasThumbnail => thumbnailUrl != null && thumbnailUrl!.isNotEmpty;
  bool get isEdited => editedAt != null;
  bool get isReply => replyToMessageId != null;

  bool get isSending => status == MessageStatus.sending;
  bool get isSent => status == MessageStatus.sent;
  bool get isDelivered => status == MessageStatus.delivered;
  bool get isRead => status == MessageStatus.read;
  bool get isFailed => status == MessageStatus.failed;

  String get displayContent {
    if (isDeleted) return 'This message was deleted';
    
    switch (type) {
      case MessageType.text:
        return content;
      case MessageType.image:
        return '📷 Image';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.file:
        return '📎 File';
      case MessageType.location:
        return '📍 Location';
      case MessageType.booking:
        return '📅 Booking';
      case MessageType.system:
        return content;
    }
  }
}
