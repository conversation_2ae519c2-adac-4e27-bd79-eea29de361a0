import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/models/service_provider_model.dart';
import '../repositories/service_provider_repository.dart';

class GetServiceProviderDetailsUseCase implements UseCase<ServiceProviderModel, String> {
  final ServiceProviderRepository repository;

  GetServiceProviderDetailsUseCase(this.repository);

  @override
  Future<Either<Failure, ServiceProviderModel>> call(String providerId) async {
    return await repository.getServiceProviderById(providerId);
  }
}
