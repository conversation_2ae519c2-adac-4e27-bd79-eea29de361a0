import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';

class RatingStarsWidget extends StatefulWidget {
  final double rating;
  final double size;
  final bool isInteractive;
  final Function(double)? onRatingChanged;
  final Color? activeColor;
  final Color? inactiveColor;

  const RatingStarsWidget({
    super.key,
    required this.rating,
    this.size = 24,
    this.isInteractive = true,
    this.onRatingChanged,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  State<RatingStarsWidget> createState() => _RatingStarsWidgetState();
}

class _RatingStarsWidgetState extends State<RatingStarsWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  double _currentRating = 0;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.rating;
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(RatingStarsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.rating != widget.rating) {
      setState(() {
        _currentRating = widget.rating;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return GestureDetector(
          onTap: widget.isInteractive ? () => _onStarTapped(index + 1) : null,
          onTapDown: widget.isInteractive ? (_) => _animateScale(true) : null,
          onTapUp: widget.isInteractive ? (_) => _animateScale(false) : null,
          onTapCancel: widget.isInteractive ? () => _animateScale(false) : null,
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: widget.isInteractive ? _scaleAnimation.value : 1.0,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: widget.size * 0.05),
                  child: _buildStar(index + 1),
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildStar(int starIndex) {
    final activeColor = widget.activeColor ?? AppColors.starYellow;
    final inactiveColor = widget.inactiveColor ?? AppColors.borderGray;
    
    if (_currentRating >= starIndex) {
      // Full star
      return Icon(
        Icons.star,
        size: widget.size,
        color: activeColor,
      );
    } else if (_currentRating > starIndex - 1) {
      // Half star
      return Stack(
        children: [
          Icon(
            Icons.star_border,
            size: widget.size,
            color: inactiveColor,
          ),
          ClipRect(
            clipper: _HalfStarClipper(
              fillPercentage: _currentRating - (starIndex - 1),
            ),
            child: Icon(
              Icons.star,
              size: widget.size,
              color: activeColor,
            ),
          ),
        ],
      );
    } else {
      // Empty star
      return Icon(
        Icons.star_border,
        size: widget.size,
        color: inactiveColor,
      );
    }
  }

  void _onStarTapped(int starIndex) {
    if (!widget.isInteractive) return;
    
    setState(() {
      _currentRating = starIndex.toDouble();
    });
    
    widget.onRatingChanged?.call(_currentRating);
  }

  void _animateScale(bool scaleUp) {
    if (scaleUp) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }
}

class _HalfStarClipper extends CustomClipper<Rect> {
  final double fillPercentage;

  _HalfStarClipper({required this.fillPercentage});

  @override
  Rect getClip(Size size) {
    return Rect.fromLTWH(0, 0, size.width * fillPercentage, size.height);
  }

  @override
  bool shouldReclip(CustomClipper<Rect> oldClipper) {
    return oldClipper is _HalfStarClipper && 
           oldClipper.fillPercentage != fillPercentage;
  }
}

// Static rating display widget (non-interactive)
class RatingDisplayWidget extends StatelessWidget {
  final double rating;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool showRatingText;
  final int? reviewCount;

  const RatingDisplayWidget({
    super.key,
    required this.rating,
    this.size = 16,
    this.activeColor,
    this.inactiveColor,
    this.showRatingText = false,
    this.reviewCount,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        RatingStarsWidget(
          rating: rating,
          size: size,
          isInteractive: false,
          activeColor: activeColor,
          inactiveColor: inactiveColor,
        ),
        
        if (showRatingText) ...[
          const SizedBox(width: 8),
          Text(
            rating.toStringAsFixed(1),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
        
        if (reviewCount != null) ...[
          const SizedBox(width: 4),
          Text(
            '($reviewCount)',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textLight,
            ),
          ),
        ],
      ],
    );
  }
}

// Compact rating widget for lists
class CompactRatingWidget extends StatelessWidget {
  final double rating;
  final int? reviewCount;
  final double size;

  const CompactRatingWidget({
    super.key,
    required this.rating,
    this.reviewCount,
    this.size = 14,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.starYellow.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            size: size,
            color: AppColors.starYellow,
          ),
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: TextStyle(
              fontSize: size * 0.9,
              fontWeight: FontWeight.w600,
              color: AppColors.textDark,
            ),
          ),
          if (reviewCount != null) ...[
            const SizedBox(width: 2),
            Text(
              '($reviewCount)',
              style: TextStyle(
                fontSize: size * 0.8,
                color: AppColors.textLight,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

// Large rating display for details pages
class LargeRatingWidget extends StatelessWidget {
  final double rating;
  final int reviewCount;
  final Map<int, int>? ratingDistribution;

  const LargeRatingWidget({
    super.key,
    required this.rating,
    required this.reviewCount,
    this.ratingDistribution,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Main rating display
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              rating.toStringAsFixed(1),
              style: Theme.of(context).textTheme.displayMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryPurple,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '/ 5',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.textLight,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Stars
        RatingStarsWidget(
          rating: rating,
          size: 32,
          isInteractive: false,
        ),
        
        const SizedBox(height: 8),
        
        // Review count
        Text(
          '$reviewCount reviews',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textLight,
          ),
        ),
        
        // Rating distribution
        if (ratingDistribution != null) ...[
          const SizedBox(height: 16),
          _buildRatingDistribution(context),
        ],
      ],
    );
  }

  Widget _buildRatingDistribution(BuildContext context) {
    final distribution = ratingDistribution!;
    final total = distribution.values.fold(0, (sum, count) => sum + count);
    
    return Column(
      children: [
        for (int i = 5; i >= 1; i--)
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Text(
                  '$i',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.star,
                  size: 12,
                  color: AppColors.starYellow,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value: total > 0 ? (distribution[i] ?? 0) / total : 0,
                    backgroundColor: AppColors.borderGray,
                    valueColor: const AlwaysStoppedAnimation<Color>(AppColors.starYellow),
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 30,
                  child: Text(
                    '${distribution[i] ?? 0}',
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
