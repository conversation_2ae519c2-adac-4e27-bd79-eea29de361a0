import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/promotion_card.dart';
import '../widgets/coupon_input_dialog.dart';
import '../widgets/promotion_filter_tabs.dart';

class PromotionsPage extends StatefulWidget {
  const PromotionsPage({super.key});

  @override
  State<PromotionsPage> createState() => _PromotionsPageState();
}

class _PromotionsPageState extends State<PromotionsPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Offers & Promotions'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showCouponDialog,
            icon: const Icon(Icons.confirmation_number),
            tooltip: 'Enter Coupon Code',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Filter Tabs
            Container(
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: PromotionFilterTabs(
                selectedFilter: _selectedFilter,
                onFilterChanged: (filter) {
                  setState(() {
                    _selectedFilter = filter;
                  });
                },
              ),
            ),
            
            // Promotions List
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: AppColors.backgroundWhite,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.largeBorderRadius),
                    topRight: Radius.circular(AppConstants.largeBorderRadius),
                  ),
                ),
                child: _buildPromotionsList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromotionsList() {
    final promotions = _getFilteredPromotions();
    
    if (promotions.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: promotions.length,
      itemBuilder: (context, index) {
        final promotion = promotions[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
          child: PromotionCard(
            promotion: promotion,
            onTap: () => _viewPromotionDetails(promotion),
            onClaim: () => _claimPromotion(promotion),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    String message;
    IconData icon;
    
    switch (_selectedFilter) {
      case 'active':
        message = 'No active promotions';
        icon = Icons.local_offer;
        break;
      case 'expired':
        message = 'No expired promotions';
        icon = Icons.history;
        break;
      case 'upcoming':
        message = 'No upcoming promotions';
        icon = Icons.schedule;
        break;
      default:
        message = 'No promotions available';
        icon = Icons.local_offer_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textGray.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textGray,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Check back later for new offers!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textGray,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredPromotions() {
    final allPromotions = _getMockPromotions();
    
    switch (_selectedFilter) {
      case 'active':
        return allPromotions.where((p) => p['status'] == 'active').toList();
      case 'expired':
        return allPromotions.where((p) => p['status'] == 'expired').toList();
      case 'upcoming':
        return allPromotions.where((p) => p['status'] == 'upcoming').toList();
      default:
        return allPromotions;
    }
  }

  List<Map<String, dynamic>> _getMockPromotions() {
    return [
      {
        'id': '1',
        'title': 'New Customer Special',
        'description': 'Get 30% off your first booking with any service provider',
        'type': 'percentage',
        'value': 30.0,
        'status': 'active',
        'imageUrl': null,
        'couponCode': 'WELCOME30',
        'validUntil': DateTime.now().add(const Duration(days: 30)),
        'minOrderAmount': 20.0,
        'maxDiscountAmount': 15.0,
        'target': 'new_customers',
      },
      {
        'id': '2',
        'title': 'Weekend Hair Special',
        'description': 'Buy any hair service and get a free beard trim',
        'type': 'buy_one_get_one',
        'value': 0.0,
        'status': 'active',
        'imageUrl': null,
        'couponCode': 'WEEKEND',
        'validUntil': DateTime.now().add(const Duration(days: 7)),
        'minOrderAmount': null,
        'maxDiscountAmount': null,
        'target': 'all',
      },
      {
        'id': '3',
        'title': 'Spa Day Package',
        'description': '20% off when you book 3 or more spa services',
        'type': 'percentage',
        'value': 20.0,
        'status': 'active',
        'imageUrl': null,
        'couponCode': 'SPADAY20',
        'validUntil': DateTime.now().add(const Duration(days: 14)),
        'minOrderAmount': 50.0,
        'maxDiscountAmount': 25.0,
        'target': 'all',
      },
      {
        'id': '4',
        'title': 'Summer Special',
        'description': 'Fixed 10 KD discount on orders above 40 KD',
        'type': 'fixed_amount',
        'value': 10.0,
        'status': 'upcoming',
        'imageUrl': null,
        'couponCode': 'SUMMER10',
        'validUntil': DateTime.now().add(const Duration(days: 60)),
        'minOrderAmount': 40.0,
        'maxDiscountAmount': null,
        'target': 'all',
      },
      {
        'id': '5',
        'title': 'VIP Member Exclusive',
        'description': '25% off all services for VIP members only',
        'type': 'percentage',
        'value': 25.0,
        'status': 'expired',
        'imageUrl': null,
        'couponCode': 'VIP25',
        'validUntil': DateTime.now().subtract(const Duration(days: 5)),
        'minOrderAmount': null,
        'maxDiscountAmount': 20.0,
        'target': 'vip_customers',
      },
    ];
  }

  void _showCouponDialog() {
    showDialog(
      context: context,
      builder: (context) => CouponInputDialog(
        onCouponApplied: (couponCode) {
          _applyCouponCode(couponCode);
        },
      ),
    );
  }

  void _applyCouponCode(String couponCode) {
    // TODO: Implement coupon validation and application
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Coupon "$couponCode" applied successfully!'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _viewPromotionDetails(Map<String, dynamic> promotion) {
    Navigator.pushNamed(
      context,
      '/promotion-details',
      arguments: promotion,
    );
  }

  void _claimPromotion(Map<String, dynamic> promotion) {
    if (promotion['status'] != 'active') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This promotion is not currently active'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Claim Promotion'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You are about to claim: ${promotion['title']}'),
            const SizedBox(height: AppConstants.defaultPadding),
            if (promotion['couponCode'] != null) ...[
              const Text('Coupon Code:'),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: AppColors.backgroundGray,
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                ),
                child: Text(
                  promotion['couponCode'],
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: AppConstants.fontSizeLarge,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _confirmClaimPromotion(promotion);
            },
            child: const Text('Claim'),
          ),
        ],
      ),
    );
  }

  void _confirmClaimPromotion(Map<String, dynamic> promotion) {
    // TODO: Implement promotion claiming logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${promotion['title']} claimed successfully!'),
        backgroundColor: AppColors.success,
      ),
    );
  }
}
