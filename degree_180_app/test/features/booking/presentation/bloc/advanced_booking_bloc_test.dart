import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';
import 'package:degree_180_app/core/errors/failures.dart';
import 'package:degree_180_app/core/models/booking_model.dart';
import 'package:degree_180_app/core/models/service_model.dart';
import 'package:degree_180_app/core/enums/booking_status.dart';
import 'package:degree_180_app/features/booking/domain/repositories/booking_repository.dart';
import 'package:degree_180_app/features/booking/domain/usecases/advanced_booking_usecase.dart';
import 'package:degree_180_app/features/booking/presentation/bloc/advanced_booking_bloc.dart';

import 'advanced_booking_bloc_test.mocks.dart';

@GenerateMocks([
  GetAvailableTimeSlotsUseCase,
  CreateAdvancedBookingUseCase,
  RescheduleBookingUseCase,
  GetBookingStatisticsUseCase,
])
void main() {
  group('AdvancedBookingBloc', () {
    late AdvancedBookingBloc bloc;
    late MockGetAvailableTimeSlotsUseCase mockGetAvailableTimeSlotsUseCase;
    late MockCreateAdvancedBookingUseCase mockCreateAdvancedBookingUseCase;
    late MockRescheduleBookingUseCase mockRescheduleBookingUseCase;
    late MockGetBookingStatisticsUseCase mockGetBookingStatisticsUseCase;

    setUp(() {
      mockGetAvailableTimeSlotsUseCase = MockGetAvailableTimeSlotsUseCase();
      mockCreateAdvancedBookingUseCase = MockCreateAdvancedBookingUseCase();
      mockRescheduleBookingUseCase = MockRescheduleBookingUseCase();
      mockGetBookingStatisticsUseCase = MockGetBookingStatisticsUseCase();

      bloc = AdvancedBookingBloc(
        getAvailableTimeSlotsUseCase: mockGetAvailableTimeSlotsUseCase,
        createAdvancedBookingUseCase: mockCreateAdvancedBookingUseCase,
        rescheduleBookingUseCase: mockRescheduleBookingUseCase,
        getBookingStatisticsUseCase: mockGetBookingStatisticsUseCase,
      );
    });

    tearDown(() {
      bloc.close();
    });

    test('initial state should be AdvancedBookingInitial', () {
      expect(bloc.state, equals(AdvancedBookingInitial()));
    });

    group('LoadAvailableTimeSlotsEvent', () {
      const providerId = 'provider-1';
      final date = DateTime(2024, 12, 25);
      const serviceDurationMinutes = 60;

      final timeSlots = [
        TimeSlotModel(
          id: 'slot-1',
          timeSlot: '09:00 - 10:00',
          startTime: DateTime(2024, 12, 25, 9, 0),
          endTime: DateTime(2024, 12, 25, 10, 0),
          isAvailable: true,
        ),
        TimeSlotModel(
          id: 'slot-2',
          timeSlot: '10:00 - 11:00',
          startTime: DateTime(2024, 12, 25, 10, 0),
          endTime: DateTime(2024, 12, 25, 11, 0),
          isAvailable: true,
        ),
      ];

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [TimeSlotsLoading, TimeSlotsLoaded] when time slots are loaded successfully',
        build: () {
          when(mockGetAvailableTimeSlotsUseCase(any))
              .thenAnswer((_) async => Right(timeSlots));
          return bloc;
        },
        act: (bloc) => bloc.add(LoadAvailableTimeSlotsEvent(
          providerId: providerId,
          date: date,
          serviceDurationMinutes: serviceDurationMinutes,
        )),
        expect: () => [
          TimeSlotsLoading(),
          TimeSlotsLoaded(timeSlots: timeSlots),
        ],
        verify: (_) {
          verify(mockGetAvailableTimeSlotsUseCase(
            GetAvailableTimeSlotsParams(
              providerId: providerId,
              date: date,
              serviceDurationMinutes: serviceDurationMinutes,
            ),
          )).called(1);
        },
      );

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [TimeSlotsLoading, TimeSlotsEmpty] when no time slots are available',
        build: () {
          when(mockGetAvailableTimeSlotsUseCase(any))
              .thenAnswer((_) async => const Right([]));
          return bloc;
        },
        act: (bloc) => bloc.add(LoadAvailableTimeSlotsEvent(
          providerId: providerId,
          date: date,
          serviceDurationMinutes: serviceDurationMinutes,
        )),
        expect: () => [
          TimeSlotsLoading(),
          const TimeSlotsEmpty(message: 'No available time slots for this date'),
        ],
      );

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [TimeSlotsLoading, TimeSlotsError] when loading time slots fails',
        build: () {
          when(mockGetAvailableTimeSlotsUseCase(any))
              .thenAnswer((_) async => const Left(ServerFailure(message: 'Server error')));
          return bloc;
        },
        act: (bloc) => bloc.add(LoadAvailableTimeSlotsEvent(
          providerId: providerId,
          date: date,
          serviceDurationMinutes: serviceDurationMinutes,
        )),
        expect: () => [
          TimeSlotsLoading(),
          const TimeSlotsError(message: 'Server error'),
        ],
      );
    });

    group('CreateAdvancedBookingEvent', () {
      final futureDate = DateTime.now().add(const Duration(days: 1));
      
      final createBookingEvent = CreateAdvancedBookingEvent(
        customerId: 'customer-1',
        customerName: 'John Doe',
        customerPhone: '+**********',
        customerEmail: '<EMAIL>',
        providerId: 'provider-1',
        providerName: 'Test Provider',
        serviceId: 'service-1',
        timeSlotId: 'slot-1',
        bookingDate: futureDate,
        timeSlot: '10:00 - 11:00',
        totalAmount: 25.0,
        serviceDurationMinutes: 60,
      );

      final testBooking = BookingModel(
        id: 'booking-1',
        customerId: 'customer-1',
        customerName: 'John Doe',
        customerPhone: '+**********',
        customerEmail: '<EMAIL>',
        providerId: 'provider-1',
        providerName: 'Test Provider',
        serviceId: 'service-1',
        serviceName: 'Test Service',
        service: ServiceModel(
          id: 'service-1',
          name: 'Test Service',
          description: 'Test description',
          price: 25.0,
          durationMinutes: 60,
          category: 'Test',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        bookingDate: futureDate,
        appointmentDateTime: futureDate.add(const Duration(hours: 10)),
        timeSlot: '10:00 - 11:00',
        status: BookingStatus.pending,
        totalAmount: 25.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [BookingCreating, BookingCreated] when booking is created successfully',
        build: () {
          when(mockCreateAdvancedBookingUseCase(any))
              .thenAnswer((_) async => Right(testBooking));
          return bloc;
        },
        act: (bloc) => bloc.add(createBookingEvent),
        expect: () => [
          BookingCreating(),
          BookingCreated(booking: testBooking),
        ],
        verify: (_) {
          verify(mockCreateAdvancedBookingUseCase(any)).called(1);
        },
      );

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [BookingCreating, BookingCreationError] when booking creation fails',
        build: () {
          when(mockCreateAdvancedBookingUseCase(any))
              .thenAnswer((_) async => Left(ValidationFailure(message: 'Invalid booking data')));
          return bloc;
        },
        act: (bloc) => bloc.add(createBookingEvent),
        expect: () => [
          BookingCreating(),
          const BookingCreationError(message: 'Invalid booking data'),
        ],
      );
    });

    group('RescheduleBookingEvent', () {
      const bookingId = 'booking-1';
      final newDate = DateTime.now().add(const Duration(days: 2));
      const newTimeSlot = '14:00 - 15:00';

      final rescheduledBooking = BookingModel(
        id: bookingId,
        customerId: 'customer-1',
        customerName: 'John Doe',
        providerId: 'provider-1',
        providerName: 'Test Provider',
        serviceId: 'service-1',
        serviceName: 'Test Service',
        service: ServiceModel(
          id: 'service-1',
          name: 'Test Service',
          description: 'Test description',
          price: 25.0,
          durationMinutes: 60,
          category: 'Test',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        bookingDate: newDate,
        appointmentDateTime: newDate.add(const Duration(hours: 14)),
        timeSlot: newTimeSlot,
        status: BookingStatus.pending,
        totalAmount: 25.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [BookingRescheduling, BookingRescheduled] when booking is rescheduled successfully',
        build: () {
          when(mockRescheduleBookingUseCase(any))
              .thenAnswer((_) async => Right(rescheduledBooking));
          return bloc;
        },
        act: (bloc) => bloc.add(RescheduleBookingEvent(
          bookingId: bookingId,
          newDate: newDate,
          newTimeSlot: newTimeSlot,
        )),
        expect: () => [
          BookingRescheduling(),
          BookingRescheduled(booking: rescheduledBooking),
        ],
        verify: (_) {
          verify(mockRescheduleBookingUseCase(
            RescheduleBookingParams(
              bookingId: bookingId,
              newDate: newDate,
              newTimeSlot: newTimeSlot,
            ),
          )).called(1);
        },
      );

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [BookingRescheduling, BookingRescheduleError] when rescheduling fails',
        build: () {
          when(mockRescheduleBookingUseCase(any))
              .thenAnswer((_) async => const Left(BookingFailure(message: 'Cannot reschedule booking')));
          return bloc;
        },
        act: (bloc) => bloc.add(RescheduleBookingEvent(
          bookingId: bookingId,
          newDate: newDate,
          newTimeSlot: newTimeSlot,
        )),
        expect: () => [
          BookingRescheduling(),
          const BookingRescheduleError(message: 'Cannot reschedule booking'),
        ],
      );
    });

    group('LoadBookingStatisticsEvent', () {
      const providerId = 'provider-1';
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 12, 31);

      final statistics = {
        'totalBookings': 100,
        'completedBookings': 85,
        'cancelledBookings': 10,
        'pendingBookings': 5,
        'totalRevenue': 2500.0,
        'averageRating': 4.5,
      };

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [BookingStatisticsLoading, BookingStatisticsLoaded] when statistics are loaded successfully',
        build: () {
          when(mockGetBookingStatisticsUseCase(any))
              .thenAnswer((_) async => Right(statistics));
          return bloc;
        },
        act: (bloc) => bloc.add(LoadBookingStatisticsEvent(
          providerId: providerId,
          startDate: startDate,
          endDate: endDate,
        )),
        expect: () => [
          BookingStatisticsLoading(),
          BookingStatisticsLoaded(statistics: statistics),
        ],
        verify: (_) {
          verify(mockGetBookingStatisticsUseCase(
            GetBookingStatisticsParams(
              providerId: providerId,
              startDate: startDate,
              endDate: endDate,
            ),
          )).called(1);
        },
      );

      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [BookingStatisticsLoading, BookingStatisticsError] when loading statistics fails',
        build: () {
          when(mockGetBookingStatisticsUseCase(any))
              .thenAnswer((_) async => const Left(ServerFailure(message: 'Failed to load statistics')));
          return bloc;
        },
        act: (bloc) => bloc.add(LoadBookingStatisticsEvent(
          providerId: providerId,
          startDate: startDate,
          endDate: endDate,
        )),
        expect: () => [
          BookingStatisticsLoading(),
          const BookingStatisticsError(message: 'Failed to load statistics'),
        ],
      );
    });

    group('ResetBookingStateEvent', () {
      blocTest<AdvancedBookingBloc, AdvancedBookingState>(
        'should emit [AdvancedBookingInitial] when reset event is added',
        build: () => bloc,
        seed: () => const TimeSlotsError(message: 'Some error'),
        act: (bloc) => bloc.add(const ResetBookingStateEvent()),
        expect: () => [
          AdvancedBookingInitial(),
        ],
      );
    });
  });
}
