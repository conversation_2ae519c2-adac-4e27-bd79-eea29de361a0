/// أنواع الرسائل في الدردشة
enum MessageType {
  text,
  image,
  video,
  audio,
  file,
  location,
  contact,
  sticker,
  gif,
  system,
  notification,
  typing,
  seen,
  delivered,
}

/// امتداد لأنواع الرسائل
extension MessageTypeExtension on MessageType {
  /// الحصول على اسم نوع الرسالة
  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'نص';
      case MessageType.image:
        return 'صورة';
      case MessageType.video:
        return 'فيديو';
      case MessageType.audio:
        return 'صوت';
      case MessageType.file:
        return 'ملف';
      case MessageType.location:
        return 'موقع';
      case MessageType.contact:
        return 'جهة اتصال';
      case MessageType.sticker:
        return 'ملصق';
      case MessageType.gif:
        return 'صورة متحركة';
      case MessageType.system:
        return 'رسالة نظام';
      case MessageType.notification:
        return 'إشعار';
      case MessageType.typing:
        return 'يكتب...';
      case MessageType.seen:
        return 'تم القراءة';
      case MessageType.delivered:
        return 'تم التسليم';
    }
  }

  /// فحص ما إذا كان نوع الرسالة يحتوي على وسائط
  bool get hasMedia {
    switch (this) {
      case MessageType.image:
      case MessageType.video:
      case MessageType.audio:
      case MessageType.file:
      case MessageType.sticker:
      case MessageType.gif:
        return true;
      case MessageType.text:
      case MessageType.location:
      case MessageType.contact:
      case MessageType.system:
      case MessageType.notification:
      case MessageType.typing:
      case MessageType.seen:
      case MessageType.delivered:
        return false;
    }
  }

  /// فحص ما إذا كان نوع الرسالة قابل للحفظ
  bool get canSave {
    switch (this) {
      case MessageType.text:
      case MessageType.image:
      case MessageType.video:
      case MessageType.audio:
      case MessageType.file:
      case MessageType.location:
      case MessageType.contact:
      case MessageType.sticker:
      case MessageType.gif:
        return true;
      case MessageType.system:
      case MessageType.notification:
      case MessageType.typing:
      case MessageType.seen:
      case MessageType.delivered:
        return false;
    }
  }

  /// فحص ما إذا كان نوع الرسالة قابل للرد عليه
  bool get canReply {
    switch (this) {
      case MessageType.text:
      case MessageType.image:
      case MessageType.video:
      case MessageType.audio:
      case MessageType.file:
      case MessageType.location:
      case MessageType.contact:
      case MessageType.sticker:
      case MessageType.gif:
        return true;
      case MessageType.system:
      case MessageType.notification:
      case MessageType.typing:
      case MessageType.seen:
      case MessageType.delivered:
        return false;
    }
  }

  /// فحص ما إذا كان نوع الرسالة قابل للتحرير
  bool get canEdit {
    switch (this) {
      case MessageType.text:
        return true;
      case MessageType.image:
      case MessageType.video:
      case MessageType.audio:
      case MessageType.file:
      case MessageType.location:
      case MessageType.contact:
      case MessageType.sticker:
      case MessageType.gif:
      case MessageType.system:
      case MessageType.notification:
      case MessageType.typing:
      case MessageType.seen:
      case MessageType.delivered:
        return false;
    }
  }

  /// فحص ما إذا كان نوع الرسالة قابل للحذف
  bool get canDelete {
    switch (this) {
      case MessageType.text:
      case MessageType.image:
      case MessageType.video:
      case MessageType.audio:
      case MessageType.file:
      case MessageType.location:
      case MessageType.contact:
      case MessageType.sticker:
      case MessageType.gif:
        return true;
      case MessageType.system:
      case MessageType.notification:
      case MessageType.typing:
      case MessageType.seen:
      case MessageType.delivered:
        return false;
    }
  }

  /// فحص ما إذا كان نوع الرسالة قابل للإعادة توجيه
  bool get canForward {
    switch (this) {
      case MessageType.text:
      case MessageType.image:
      case MessageType.video:
      case MessageType.audio:
      case MessageType.file:
      case MessageType.location:
      case MessageType.contact:
      case MessageType.sticker:
      case MessageType.gif:
        return true;
      case MessageType.system:
      case MessageType.notification:
      case MessageType.typing:
      case MessageType.seen:
      case MessageType.delivered:
        return false;
    }
  }

  /// فحص ما إذا كان نوع الرسالة رسالة حالة
  bool get isStatus {
    switch (this) {
      case MessageType.typing:
      case MessageType.seen:
      case MessageType.delivered:
        return true;
      case MessageType.text:
      case MessageType.image:
      case MessageType.video:
      case MessageType.audio:
      case MessageType.file:
      case MessageType.location:
      case MessageType.contact:
      case MessageType.sticker:
      case MessageType.gif:
      case MessageType.system:
      case MessageType.notification:
        return false;
    }
  }

  /// تحويل من نص إلى enum
  static MessageType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'text':
        return MessageType.text;
      case 'image':
        return MessageType.image;
      case 'video':
        return MessageType.video;
      case 'audio':
        return MessageType.audio;
      case 'file':
        return MessageType.file;
      case 'location':
        return MessageType.location;
      case 'contact':
        return MessageType.contact;
      case 'sticker':
        return MessageType.sticker;
      case 'gif':
        return MessageType.gif;
      case 'system':
        return MessageType.system;
      case 'notification':
        return MessageType.notification;
      case 'typing':
        return MessageType.typing;
      case 'seen':
        return MessageType.seen;
      case 'delivered':
        return MessageType.delivered;
      default:
        return MessageType.text;
    }
  }

  /// تحويل إلى نص
  String toStringValue() {
    return toString().split('.').last;
  }
}

/// فئة مساعدة لأنواع الرسائل
class MessageTypeHelper {
  /// الحصول على جميع أنواع الرسائل
  static List<MessageType> get allTypes => MessageType.values;

  /// الحصول على أنواع الرسائل التي تحتوي على وسائط
  static List<MessageType> get mediaTypes =>
      MessageType.values.where((type) => type.hasMedia).toList();

  /// الحصول على أنواع الرسائل النصية
  static List<MessageType> get textTypes =>
      MessageType.values.where((type) => !type.hasMedia && !type.isStatus).toList();

  /// الحصول على أنواع رسائل الحالة
  static List<MessageType> get statusTypes =>
      MessageType.values.where((type) => type.isStatus).toList();

  /// فحص ما إذا كان النوع صالح للإرسال
  static bool canSend(MessageType type) {
    return !type.isStatus && type != MessageType.system && type != MessageType.notification;
  }

  /// الحصول على الحد الأقصى لحجم الملف حسب النوع (بالميجابايت)
  static double getMaxFileSize(MessageType type) {
    switch (type) {
      case MessageType.image:
        return 10.0; // 10 MB
      case MessageType.video:
        return 100.0; // 100 MB
      case MessageType.audio:
        return 50.0; // 50 MB
      case MessageType.file:
        return 25.0; // 25 MB
      case MessageType.sticker:
      case MessageType.gif:
        return 5.0; // 5 MB
      default:
        return 0.0;
    }
  }

  /// الحصول على امتدادات الملفات المدعومة
  static List<String> getSupportedExtensions(MessageType type) {
    switch (type) {
      case MessageType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
      case MessageType.video:
        return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
      case MessageType.audio:
        return ['mp3', 'wav', 'aac', 'ogg', 'flac', 'm4a'];
      case MessageType.file:
        return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar'];
      default:
        return [];
    }
  }
}
