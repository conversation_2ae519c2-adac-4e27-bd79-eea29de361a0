import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'dart:io';
import '../models/user_model.dart';
import '../models/service_provider_model.dart';
import '../models/booking_model.dart';
import '../models/notification_model.dart';
import '../models/chat_model.dart';
import '../models/payment_model.dart';
// import '../models/promotion_model.dart';
// import '../models/review_model.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  // Collections
  CollectionReference get users => _firestore.collection('users');
  CollectionReference get serviceProviders => _firestore.collection('service_providers');
  CollectionReference get bookings => _firestore.collection('bookings');
  CollectionReference get notifications => _firestore.collection('notifications');
  CollectionReference get chats => _firestore.collection('chats');
  CollectionReference get payments => _firestore.collection('payments');
  CollectionReference get promotions => _firestore.collection('promotions');
  CollectionReference get reviews => _firestore.collection('reviews');
  CollectionReference get analytics => _firestore.collection('analytics');

  // Current user
  User? get currentUser => _auth.currentUser;
  String? get currentUserId => _auth.currentUser?.uid;

  // Initialize Firebase services
  Future<void> initialize() async {
    try {
      // Enable offline persistence
      _firestore.settings = const Settings(persistenceEnabled: true);
      
      // Configure Crashlytics
      await _crashlytics.setCrashlyticsCollectionEnabled(true);
      
      // Request notification permissions
      await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );
      
      // Set up FCM token refresh
      _messaging.onTokenRefresh.listen(_updateFCMToken);
      
      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      
      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
      
      print('Firebase services initialized successfully');
    } catch (e) {
      print('Error initializing Firebase services: $e');
      await _crashlytics.recordError(e, null);
    }
  }

  // User Management
  Future<UserModel?> getUserById(String userId) async {
    try {
      final doc = await users.doc(userId).get();
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Future<void> createUser(UserModel user) async {
    try {
      await users.doc(user.id).set(user.toMap());
      await _analytics.logEvent(name: 'user_created', parameters: {
        'user_type': user.userType.name,
      });
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Future<void> updateUser(UserModel user) async {
    try {
      await users.doc(user.id).update(user.toMap());
      await _analytics.logEvent(name: 'user_updated');
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  // Service Provider Management
  Future<List<ServiceProviderModel>> getServiceProviders({
    String? serviceType,
    double? latitude,
    double? longitude,
    double? radiusKm,
    int limit = 20,
  }) async {
    try {
      Query query = serviceProviders.where('isActive', isEqualTo: true);
      
      if (serviceType != null) {
        query = query.where('serviceType', isEqualTo: serviceType);
      }
      
      query = query.limit(limit);
      
      final snapshot = await query.get();
      final providers = snapshot.docs
          .map((doc) => ServiceProviderModel.fromFirestore(doc))
          .toList();
      
      // Filter by location if provided
      if (latitude != null && longitude != null && radiusKm != null) {
        providers.removeWhere((provider) {
          if (provider.location == null) return true;
          final distance = _calculateDistance(
            latitude,
            longitude,
            provider.location!.latitude,
            provider.location!.longitude,
          );
          return distance > radiusKm;
        });
      }
      
      return providers;
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Future<ServiceProviderModel?> getServiceProviderById(String providerId) async {
    try {
      final doc = await serviceProviders.doc(providerId).get();
      if (doc.exists) {
        return ServiceProviderModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  // Booking Management
  Future<String> createBooking(BookingModel booking) async {
    try {
      final docRef = await bookings.add(booking.toMap());
      
      // Create notification for provider
      await _createBookingNotification(booking, 'new_booking');
      
      await _analytics.logEvent(name: 'booking_created', parameters: {
        'provider_id': booking.providerId,
        'service_type': booking.serviceType,
        'amount': booking.totalAmount,
      });
      
      return docRef.id;
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Future<void> updateBooking(BookingModel booking) async {
    try {
      await bookings.doc(booking.id).update(booking.toMap());
      
      // Create notification based on status change
      await _createBookingNotification(booking, 'booking_updated');
      
      await _analytics.logEvent(name: 'booking_updated', parameters: {
        'booking_id': booking.id,
        'status': booking.status.name,
      });
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Future<List<BookingModel>> getUserBookings(String userId) async {
    try {
      final snapshot = await bookings
          .where('customerId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();
      
      return snapshot.docs
          .map((doc) => BookingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  // Chat Management
  Future<String> createChat(String userId, String providerId) async {
    try {
      final chatId = '${userId}_$providerId';
      
      // Check if chat already exists
      final existingChat = await chats.doc(chatId).get();
      if (existingChat.exists) {
        return chatId;
      }
      
      // Get provider info
      final provider = await getServiceProviderById(providerId);
      
      final chat = ChatModel(
        id: chatId,
        participantId: providerId,
        participantName: provider?.name ?? 'Provider',
        participantImage: provider?.profileImageUrl,
        lastMessage: '',
        lastMessageTime: DateTime.now(),
        createdAt: DateTime.now(),
      );
      
      await chats.doc(chatId).set(chat.toMap());
      return chatId;
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Future<void> sendMessage(MessageModel message) async {
    try {
      // Add message to subcollection
      await chats
          .doc(message.chatId)
          .collection('messages')
          .add(message.toMap());
      
      // Update chat with last message
      await chats.doc(message.chatId).update({
        'lastMessage': message.displayText,
        'lastMessageTime': Timestamp.fromDate(message.timestamp),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
      
      await _analytics.logEvent(name: 'message_sent', parameters: {
        'message_type': message.messageType.name,
      });
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Stream<List<MessageModel>> getChatMessages(String chatId) {
    return chats
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => MessageModel.fromFirestore(doc))
            .toList());
  }

  // Notification Management
  Future<void> createNotification(NotificationModel notification) async {
    try {
      await notifications.add(notification.toMap());
      
      // Send push notification
      await _sendPushNotification(notification);
      
      await _analytics.logEvent(name: 'notification_created', parameters: {
        'type': notification.type.name,
        'priority': notification.priority,
      });
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Future<List<NotificationModel>> getUserNotifications(String userId) async {
    try {
      final snapshot = await notifications
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();
      
      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  // Payment Management
  Future<String> createPayment(PaymentModel payment) async {
    try {
      final docRef = await payments.add(payment.toMap());
      
      await _analytics.logEvent(name: 'payment_created', parameters: {
        'amount': payment.amount,
        'method': payment.paymentMethod.name,
      });
      
      return docRef.id;
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  Future<void> updatePayment(PaymentModel payment) async {
    try {
      await payments.doc(payment.id).update(payment.toMap());
      
      // Create notification for payment status
      if (payment.status == PaymentStatus.completed) {
        final notification = NotificationModel(
          id: '',
          userId: payment.userId,
          title: 'Payment Successful',
          message: 'Payment of ${payment.amount} ${payment.currency} has been processed successfully.',
          type: NotificationType.payment,
          createdAt: DateTime.now(),
          priority: 3,
          data: {
            'paymentId': payment.id,
            'amount': payment.amount,
            'currency': payment.currency,
          },
          actionUrl: '/payment-history',
        );
        await createNotification(notification);
      }
      
      await _analytics.logEvent(name: 'payment_updated', parameters: {
        'payment_id': payment.id,
        'status': payment.status.name,
      });
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  // File Upload
  Future<String> uploadFile(File file, String path) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      await _crashlytics.recordError(e, null);
      rethrow;
    }
  }

  // Analytics
  Future<void> logEvent(String name, Map<String, dynamic>? parameters) async {
    try {
      await _analytics.logEvent(name: name, parameters: parameters?.cast<String, Object>());
    } catch (e) {
      await _crashlytics.recordError(e, null);
    }
  }

  // Private helper methods
  Future<void> _updateFCMToken(String token) async {
    if (currentUserId != null) {
      await users.doc(currentUserId).update({'fcmToken': token});
    }
  }

  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    // Handle foreground notifications
    print('Received foreground message: ${message.notification?.title}');
  }

  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    // Handle background notifications
    print('Received background message: ${message.notification?.title}');
  }

  Future<void> _createBookingNotification(BookingModel booking, String type) async {
    // Implementation for creating booking-related notifications
  }

  Future<void> _sendPushNotification(NotificationModel notification) async {
    // Implementation for sending push notifications
  }

  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    // Haversine formula implementation
    const double earthRadius = 6371; // km
    
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    
    final double a = 
        (dLat / 2).sin() * (dLat / 2).sin() +
        lat1.cos() * lat2.cos() *
        (dLon / 2).sin() * (dLon / 2).sin();
    
    final double c = 2 * a.sqrt().asin();
    
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }
}

// Extension for math functions
extension MathExtension on double {
  double sin() => 0.0; // Placeholder - use dart:math
  double cos() => 0.0; // Placeholder - use dart:math
  double asin() => 0.0; // Placeholder - use dart:math
  double sqrt() => 0.0; // Placeholder - use dart:math
}
