import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Purple Gradient Theme
  static const Color primaryPurple = Color(0xFF6B46C1);
  static const Color primaryPurpleLight = Color(0xFF8B5CF6);
  static const Color primaryPurpleDark = Color(0xFF553C9A);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF8B5CF6),
      Color(0xFF6B46C1),
      Color(0xFF553C9A),
    ],
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF8B5CF6),
      Color(0xFF6B46C1),
    ],
  );
  
  // Text Colors
  static const Color textWhite = Color(0xFFFFFFFF);
  static const Color textGray = Color(0xFFB0B0B0);
  static const Color textDark = Color(0xFF1F2937);
  static const Color textLight = Color(0xFF6B7280);
  
  // Background Colors
  static const Color backgroundWhite = Color(0xFFFFFFFF);
  static const Color backgroundGray = Color(0xFFF3F4F6);
  static const Color backgroundDark = Color(0xFF1F2937);
  
  // Button Colors
  static const Color buttonWhite = Color(0xFFFFFFFF);
  static const Color buttonGray = Color(0xFF9CA3AF);
  static const Color buttonTransparent = Colors.transparent;
  
  // Status Colors
  static const Color success = Color(0xFF10B981);
  static const Color error = Color(0xFFEF4444);
  static const Color warning = Color(0xFFF59E0B);
  static const Color info = Color(0xFF3B82F6);
  
  // Rating Colors
  static const Color starYellow = Color(0xFFFBBF24);
  static const Color starGray = Color(0xFFD1D5DB);
  
  // Service Type Colors
  static const Color salonColor = Color(0xFFEC4899);
  static const Color barbershopColor = Color(0xFF3B82F6);
  static const Color spaColor = Color(0xFF10B981);
  
  // Overlay Colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  
  // Border Colors
  static const Color borderGray = Color(0xFFE5E7EB);
  static const Color borderLight = Color(0xFFF3F4F6);
  
  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
}
