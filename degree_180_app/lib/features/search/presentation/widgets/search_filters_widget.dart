import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../auth/domain/entities/user_entity.dart';

/// ويدجت تصفية البحث المتقدم
class SearchFiltersWidget extends StatefulWidget {
  final Function(SearchFilters) onFiltersChanged;
  final SearchFilters? initialFilters;

  const SearchFiltersWidget({
    super.key,
    required this.onFiltersChanged,
    this.initialFilters,
  });

  @override
  State<SearchFiltersWidget> createState() => _SearchFiltersWidgetState();
}

class _SearchFiltersWidgetState extends State<SearchFiltersWidget> {
  late SearchFilters _filters;

  @override
  void initState() {
    super.initState();
    _filters = widget.initialFilters ?? SearchFilters();
  }

  void _updateFilters() {
    widget.onFiltersChanged(_filters);
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.filter_list,
                  color: AppColors.primaryPurple,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'تصفية النتائج',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDark,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text(
                    'مسح الكل',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: AppColors.primaryPurple,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Service Type Filter
            _buildServiceTypeFilter(),
            const SizedBox(height: 16),

            // Price Range Filter
            _buildPriceRangeFilter(),
            const SizedBox(height: 16),

            // Rating Filter
            _buildRatingFilter(),
            const SizedBox(height: 16),

            // Distance Filter
            _buildDistanceFilter(),
            const SizedBox(height: 16),

            // Availability Filter
            _buildAvailabilityFilter(),
            const SizedBox(height: 16),

            // Sort Options
            _buildSortOptions(),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الخدمة',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: ServiceType.values.map((type) {
            final isSelected = _filters.serviceTypes.contains(type);
            return FilterChip(
              label: Text(_getServiceTypeName(type)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _filters.serviceTypes.add(type);
                  } else {
                    _filters.serviceTypes.remove(type);
                  }
                });
                _updateFilters();
              },
              selectedColor: AppColors.primaryPurple.withValues(alpha: 0.2),
              checkmarkColor: AppColors.primaryPurple,
              labelStyle: GoogleFonts.cairo(
                fontSize: 14,
                color: isSelected ? AppColors.primaryPurple : AppColors.textGray,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPriceRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نطاق السعر (د.ك)',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        RangeSlider(
          values: RangeValues(_filters.minPrice, _filters.maxPrice),
          min: 0,
          max: 200,
          divisions: 20,
          labels: RangeLabels(
            '${_filters.minPrice.round()}',
            '${_filters.maxPrice.round()}',
          ),
          onChanged: (values) {
            setState(() {
              _filters.minPrice = values.start;
              _filters.maxPrice = values.end;
            });
            _updateFilters();
          },
          activeColor: AppColors.primaryPurple,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${_filters.minPrice.round()} د.ك',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textGray,
              ),
            ),
            Text(
              '${_filters.maxPrice.round()} د.ك',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textGray,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRatingFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقييم الأدنى',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(5, (index) {
            final rating = index + 1;
            final isSelected = _filters.minRating >= rating;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _filters.minRating = rating.toDouble();
                });
                _updateFilters();
              },
              child: Padding(
                padding: const EdgeInsets.only(right: 4),
                child: Icon(
                  isSelected ? Icons.star : Icons.star_border,
                  color: isSelected ? Colors.amber : AppColors.textGray,
                  size: 28,
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildDistanceFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المسافة القصوى (كم)',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        Slider(
          value: _filters.maxDistance,
          min: 1,
          max: 50,
          divisions: 49,
          label: '${_filters.maxDistance.round()} كم',
          onChanged: (value) {
            setState(() {
              _filters.maxDistance = value;
            });
            _updateFilters();
          },
          activeColor: AppColors.primaryPurple,
        ),
        Text(
          '${_filters.maxDistance.round()} كم',
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: AppColors.textGray,
          ),
        ),
      ],
    );
  }

  Widget _buildAvailabilityFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التوفر',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: Text(
            'متاح الآن',
            style: GoogleFonts.cairo(fontSize: 14),
          ),
          value: _filters.availableNow,
          onChanged: (value) {
            setState(() {
              _filters.availableNow = value ?? false;
            });
            _updateFilters();
          },
          activeColor: AppColors.primaryPurple,
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: Text(
            'متاح اليوم',
            style: GoogleFonts.cairo(fontSize: 14),
          ),
          value: _filters.availableToday,
          onChanged: (value) {
            setState(() {
              _filters.availableToday = value ?? false;
            });
            _updateFilters();
          },
          activeColor: AppColors.primaryPurple,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildSortOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ترتيب حسب',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 8),
        ...SortOption.values.map((option) {
          return RadioListTile<SortOption>(
            title: Text(
              _getSortOptionName(option),
              style: GoogleFonts.cairo(fontSize: 14),
            ),
            value: option,
            groupValue: _filters.sortBy,
            onChanged: (value) {
              setState(() {
                _filters.sortBy = value!;
              });
              _updateFilters();
            },
            activeColor: AppColors.primaryPurple,
            contentPadding: EdgeInsets.zero,
          );
        }).toList(),
      ],
    );
  }

  void _clearFilters() {
    setState(() {
      _filters = SearchFilters();
    });
    _updateFilters();
  }

  String _getServiceTypeName(ServiceType type) {
    switch (type) {
      case ServiceType.salon:
        return 'صالون';
      case ServiceType.barbershop:
        return 'حلاق';
      case ServiceType.spa:
        return 'سبا';
      case ServiceType.fitness:
        return 'لياقة';
    }
  }

  String _getSortOptionName(SortOption option) {
    switch (option) {
      case SortOption.relevance:
        return 'الأكثر صلة';
      case SortOption.rating:
        return 'التقييم';
      case SortOption.distance:
        return 'المسافة';
      case SortOption.price:
        return 'السعر';
      case SortOption.popularity:
        return 'الشعبية';
    }
  }
}

/// نموذج تصفية البحث
class SearchFilters {
  Set<ServiceType> serviceTypes;
  double minPrice;
  double maxPrice;
  double minRating;
  double maxDistance;
  bool availableNow;
  bool availableToday;
  SortOption sortBy;

  SearchFilters({
    Set<ServiceType>? serviceTypes,
    this.minPrice = 0,
    this.maxPrice = 200,
    this.minRating = 0,
    this.maxDistance = 25,
    this.availableNow = false,
    this.availableToday = false,
    this.sortBy = SortOption.relevance,
  }) : serviceTypes = serviceTypes ?? <ServiceType>{};

  bool get hasActiveFilters {
    return serviceTypes.isNotEmpty ||
           minPrice > 0 ||
           maxPrice < 200 ||
           minRating > 0 ||
           maxDistance < 25 ||
           availableNow ||
           availableToday ||
           sortBy != SortOption.relevance;
  }

  SearchFilters copyWith({
    Set<ServiceType>? serviceTypes,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    double? maxDistance,
    bool? availableNow,
    bool? availableToday,
    SortOption? sortBy,
  }) {
    return SearchFilters(
      serviceTypes: serviceTypes ?? this.serviceTypes,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minRating: minRating ?? this.minRating,
      maxDistance: maxDistance ?? this.maxDistance,
      availableNow: availableNow ?? this.availableNow,
      availableToday: availableToday ?? this.availableToday,
      sortBy: sortBy ?? this.sortBy,
    );
  }
}

/// خيارات الترتيب
enum SortOption {
  relevance,
  rating,
  distance,
  price,
  popularity,
}
