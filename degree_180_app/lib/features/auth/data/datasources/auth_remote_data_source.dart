import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../../domain/entities/user_entity.dart';
import '../models/user_model.dart';
import '../../../../core/errors/exceptions.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<UserModel> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    required UserType userType,
    ServiceType? serviceType,
  });

  Future<UserModel> signInWithGoogle();

  Future<void> signOut();

  Future<UserModel> getCurrentUser();

  Future<bool> isUserSignedIn();

  Future<UserModel> updateUserProfile({
    String? name,
    String? phoneNumber,
    String? profileImageUrl,
  });
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final FirebaseAuth firebaseAuth;
  final FirebaseFirestore firestore;
  final GoogleSignIn googleSignIn;

  AuthRemoteDataSourceImpl({
    required this.firebaseAuth,
    required this.firestore,
    required this.googleSignIn,
  });

  @override
  Future<UserModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw const ServerException(message: 'Failed to sign in');
      }

      return await _getUserFromFirestore(credential.user!.uid);
    } on FirebaseAuthException catch (e) {
      throw ServerException(message: e.message ?? 'Authentication failed');
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<UserModel> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    required UserType userType,
    ServiceType? serviceType,
  }) async {
    try {
      final credential = await firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw const ServerException(message: 'Failed to create account');
      }

      final user = UserModel(
        id: credential.user!.uid,
        uid: credential.user!.uid,
        email: email,
        name: name,
        userType: userType,
        serviceType: serviceType,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _saveUserToFirestore(user);
      return user;
    } on FirebaseAuthException catch (e) {
      throw ServerException(message: e.message ?? 'Failed to create account');
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<UserModel> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
      
      if (googleUser == null) {
        throw const ServerException(message: 'Google sign in was cancelled');
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await firebaseAuth.signInWithCredential(credential);

      if (userCredential.user == null) {
        throw const ServerException(message: 'Failed to sign in with Google');
      }

      // Check if user exists in Firestore
      try {
        return await _getUserFromFirestore(userCredential.user!.uid);
      } catch (e) {
        // User doesn't exist, create new user
        final user = UserModel(
          id: userCredential.user!.uid,
          uid: userCredential.user!.uid,
          email: userCredential.user!.email ?? '',
          name: userCredential.user!.displayName ?? '',
          photoURL: userCredential.user!.photoURL,
          profileImageUrl: userCredential.user!.photoURL,
          userType: UserType.customer, // Default to customer
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _saveUserToFirestore(user);
        return user;
      }
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await Future.wait([
        firebaseAuth.signOut(),
        googleSignIn.signOut(),
      ]);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      final user = firebaseAuth.currentUser;
      if (user == null) {
        throw const ServerException(message: 'No user signed in');
      }

      return await _getUserFromFirestore(user.uid);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<bool> isUserSignedIn() async {
    return firebaseAuth.currentUser != null;
  }

  @override
  Future<UserModel> updateUserProfile({
    String? name,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    try {
      final user = firebaseAuth.currentUser;
      if (user == null) {
        throw const ServerException(message: 'No user signed in');
      }

      final currentUser = await _getUserFromFirestore(user.uid);
      final updatedUser = currentUser.copyWith(
        name: name ?? currentUser.name,
        phoneNumber: phoneNumber ?? currentUser.phoneNumber,
        profileImageUrl: profileImageUrl ?? currentUser.profileImageUrl,
        updatedAt: DateTime.now(),
      );

      await _saveUserToFirestore(updatedUser);
      return updatedUser;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  Future<UserModel> _getUserFromFirestore(String uid) async {
    final doc = await firestore.collection('users').doc(uid).get();
    
    if (!doc.exists) {
      throw const ServerException(message: 'User not found');
    }

    return UserModel.fromFirestore(doc);
  }

  Future<void> _saveUserToFirestore(UserModel user) async {
    await firestore.collection('users').doc(user.id).set(user.toFirestore());
  }
}
