import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
  partiallyRefunded,
}

enum PaymentMethod {
  creditCard,
  debitCard,
  knet,
  applePay,
  googlePay,
  cash,
  wallet,
}

class PaymentModel extends Equatable {
  final String id;
  final String bookingId;
  final String userId;
  final String providerId;
  final double amount;
  final String currency;
  final PaymentMethod paymentMethod;
  final PaymentStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? transactionId;
  final String? gatewayResponse;
  final String? failureReason;
  final double? refundAmount;
  final DateTime? refundedAt;
  final Map<String, dynamic>? metadata;
  final String? receiptUrl;
  final double? serviceFee;
  final double? tax;
  final double? discount;
  final String? promoCode;

  const PaymentModel({
    required this.id,
    required this.bookingId,
    required this.userId,
    required this.providerId,
    required this.amount,
    this.currency = 'KWD',
    required this.paymentMethod,
    this.status = PaymentStatus.pending,
    required this.createdAt,
    this.completedAt,
    this.transactionId,
    this.gatewayResponse,
    this.failureReason,
    this.refundAmount,
    this.refundedAt,
    this.metadata,
    this.receiptUrl,
    this.serviceFee,
    this.tax,
    this.discount,
    this.promoCode,
  });

  PaymentModel copyWith({
    String? id,
    String? bookingId,
    String? userId,
    String? providerId,
    double? amount,
    String? currency,
    PaymentMethod? paymentMethod,
    PaymentStatus? status,
    DateTime? createdAt,
    DateTime? completedAt,
    String? transactionId,
    String? gatewayResponse,
    String? failureReason,
    double? refundAmount,
    DateTime? refundedAt,
    Map<String, dynamic>? metadata,
    String? receiptUrl,
    double? serviceFee,
    double? tax,
    double? discount,
    String? promoCode,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      bookingId: bookingId ?? this.bookingId,
      userId: userId ?? this.userId,
      providerId: providerId ?? this.providerId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      transactionId: transactionId ?? this.transactionId,
      gatewayResponse: gatewayResponse ?? this.gatewayResponse,
      failureReason: failureReason ?? this.failureReason,
      refundAmount: refundAmount ?? this.refundAmount,
      refundedAt: refundedAt ?? this.refundedAt,
      metadata: metadata ?? this.metadata,
      receiptUrl: receiptUrl ?? this.receiptUrl,
      serviceFee: serviceFee ?? this.serviceFee,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      promoCode: promoCode ?? this.promoCode,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bookingId': bookingId,
      'userId': userId,
      'providerId': providerId,
      'amount': amount,
      'currency': currency,
      'paymentMethod': paymentMethod.name,
      'status': status.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'transactionId': transactionId,
      'gatewayResponse': gatewayResponse,
      'failureReason': failureReason,
      'refundAmount': refundAmount,
      'refundedAt': refundedAt != null ? Timestamp.fromDate(refundedAt!) : null,
      'metadata': metadata,
      'receiptUrl': receiptUrl,
      'serviceFee': serviceFee,
      'tax': tax,
      'discount': discount,
      'promoCode': promoCode,
    };
  }

  factory PaymentModel.fromMap(Map<String, dynamic> map) {
    return PaymentModel(
      id: map['id'] ?? '',
      bookingId: map['bookingId'] ?? '',
      userId: map['userId'] ?? '',
      providerId: map['providerId'] ?? '',
      amount: (map['amount'] ?? 0.0).toDouble(),
      currency: map['currency'] ?? 'KWD',
      paymentMethod: PaymentMethod.values.firstWhere(
        (method) => method.name == map['paymentMethod'],
        orElse: () => PaymentMethod.creditCard,
      ),
      status: PaymentStatus.values.firstWhere(
        (status) => status.name == map['status'],
        orElse: () => PaymentStatus.pending,
      ),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      completedAt: (map['completedAt'] as Timestamp?)?.toDate(),
      transactionId: map['transactionId'],
      gatewayResponse: map['gatewayResponse'],
      failureReason: map['failureReason'],
      refundAmount: map['refundAmount']?.toDouble(),
      refundedAt: (map['refundedAt'] as Timestamp?)?.toDate(),
      metadata: map['metadata'] != null ? Map<String, dynamic>.from(map['metadata']) : null,
      receiptUrl: map['receiptUrl'],
      serviceFee: map['serviceFee']?.toDouble(),
      tax: map['tax']?.toDouble(),
      discount: map['discount']?.toDouble(),
      promoCode: map['promoCode'],
    );
  }

  factory PaymentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PaymentModel.fromMap({...data, 'id': doc.id});
  }

  double get totalAmount {
    double total = amount;
    if (serviceFee != null) total += serviceFee!;
    if (tax != null) total += tax!;
    if (discount != null) total -= discount!;
    return total;
  }

  String get formattedAmount => '${amount.toStringAsFixed(3)} $currency';
  String get formattedTotalAmount => '${totalAmount.toStringAsFixed(3)} $currency';

  String get statusDisplayName {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.cancelled:
        return 'Cancelled';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.partiallyRefunded:
        return 'Partially Refunded';
    }
  }

  String get paymentMethodDisplayName {
    switch (paymentMethod) {
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.knet:
        return 'K-Net';
      case PaymentMethod.applePay:
        return 'Apple Pay';
      case PaymentMethod.googlePay:
        return 'Google Pay';
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.wallet:
        return 'Wallet';
    }
  }

  bool get isSuccessful => status == PaymentStatus.completed;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isPending => status == PaymentStatus.pending || status == PaymentStatus.processing;
  bool get isRefunded => status == PaymentStatus.refunded || status == PaymentStatus.partiallyRefunded;

  @override
  List<Object?> get props => [
        id,
        bookingId,
        userId,
        providerId,
        amount,
        currency,
        paymentMethod,
        status,
        createdAt,
        completedAt,
        transactionId,
        gatewayResponse,
        failureReason,
        refundAmount,
        refundedAt,
        metadata,
        receiptUrl,
        serviceFee,
        tax,
        discount,
        promoCode,
      ];
}

class PaymentCardModel extends Equatable {
  final String id;
  final String userId;
  final String last4Digits;
  final String brand; // visa, mastercard, etc.
  final String expiryMonth;
  final String expiryYear;
  final String holderName;
  final bool isDefault;
  final DateTime createdAt;
  final String? token; // Payment gateway token

  const PaymentCardModel({
    required this.id,
    required this.userId,
    required this.last4Digits,
    required this.brand,
    required this.expiryMonth,
    required this.expiryYear,
    required this.holderName,
    this.isDefault = false,
    required this.createdAt,
    this.token,
  });

  PaymentCardModel copyWith({
    String? id,
    String? userId,
    String? last4Digits,
    String? brand,
    String? expiryMonth,
    String? expiryYear,
    String? holderName,
    bool? isDefault,
    DateTime? createdAt,
    String? token,
  }) {
    return PaymentCardModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      last4Digits: last4Digits ?? this.last4Digits,
      brand: brand ?? this.brand,
      expiryMonth: expiryMonth ?? this.expiryMonth,
      expiryYear: expiryYear ?? this.expiryYear,
      holderName: holderName ?? this.holderName,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      token: token ?? this.token,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'last4Digits': last4Digits,
      'brand': brand,
      'expiryMonth': expiryMonth,
      'expiryYear': expiryYear,
      'holderName': holderName,
      'isDefault': isDefault,
      'createdAt': Timestamp.fromDate(createdAt),
      'token': token,
    };
  }

  factory PaymentCardModel.fromMap(Map<String, dynamic> map) {
    return PaymentCardModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      last4Digits: map['last4Digits'] ?? '',
      brand: map['brand'] ?? '',
      expiryMonth: map['expiryMonth'] ?? '',
      expiryYear: map['expiryYear'] ?? '',
      holderName: map['holderName'] ?? '',
      isDefault: map['isDefault'] ?? false,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      token: map['token'],
    );
  }

  factory PaymentCardModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PaymentCardModel.fromMap({...data, 'id': doc.id});
  }

  String get maskedNumber => '**** **** **** $last4Digits';
  String get expiryDate => '$expiryMonth/$expiryYear';
  
  bool get isExpired {
    final now = DateTime.now();
    final expiry = DateTime(int.parse('20$expiryYear'), int.parse(expiryMonth));
    return now.isAfter(expiry);
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        last4Digits,
        brand,
        expiryMonth,
        expiryYear,
        holderName,
        isDefault,
        createdAt,
        token,
      ];
}
