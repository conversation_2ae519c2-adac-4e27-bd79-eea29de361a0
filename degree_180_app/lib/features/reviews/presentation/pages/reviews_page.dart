import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/review_card.dart';
import '../widgets/review_summary_card.dart';
import '../widgets/review_filter_bar.dart';

class ReviewsPage extends StatefulWidget {
  final String providerId;

  const ReviewsPage({
    super.key,
    required this.providerId,
  });

  @override
  State<ReviewsPage> createState() => _ReviewsPageState();
}

class _ReviewsPageState extends State<ReviewsPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String _selectedFilter = 'all';
  String _sortBy = 'newest';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.reviews),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showSortOptions,
            icon: const Icon(Icons.sort),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Review Summary
            Container(
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: const Padding(
                padding: EdgeInsets.all(AppConstants.defaultPadding),
                child: ReviewSummaryCard(
                  averageRating: 4.8,
                  totalReviews: 127,
                  ratingDistribution: {
                    5: 85,
                    4: 25,
                    3: 10,
                    2: 5,
                    1: 2,
                  },
                ),
              ),
            ),
            
            // Filter Bar
            Container(
              color: AppColors.backgroundWhite,
              child: ReviewFilterBar(
                selectedFilter: _selectedFilter,
                onFilterChanged: (filter) {
                  setState(() {
                    _selectedFilter = filter;
                  });
                },
              ),
            ),
            
            // Reviews List
            Expanded(
              child: Container(
                color: AppColors.backgroundWhite,
                child: _buildReviewsList(),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _writeReview,
        backgroundColor: AppColors.primaryPurple,
        child: const Icon(
          Icons.rate_review,
          color: AppColors.textWhite,
        ),
      ),
    );
  }

  Widget _buildReviewsList() {
    final reviews = _getFilteredReviews();
    
    if (reviews.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: reviews.length,
      itemBuilder: (context, index) {
        final review = reviews[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
          child: ReviewCard(
            review: review,
            onHelpful: () => _markHelpful(review['id']),
            onNotHelpful: () => _markNotHelpful(review['id']),
            onReport: () => _reportReview(review['id']),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.rate_review_outlined,
            size: 64,
            color: AppColors.textGray.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No reviews yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textGray,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Be the first to write a review',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textGray,
            ),
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton(
            onPressed: _writeReview,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryPurple,
              foregroundColor: AppColors.textWhite,
            ),
            child: const Text('Write Review'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredReviews() {
    final allReviews = _getMockReviews();
    
    List<Map<String, dynamic>> filtered = allReviews;
    
    // Apply rating filter
    if (_selectedFilter != 'all') {
      final rating = int.tryParse(_selectedFilter);
      if (rating != null) {
        filtered = filtered.where((review) {
          final reviewRating = review['rating'] as double;
          return reviewRating.floor() == rating;
        }).toList();
      } else if (_selectedFilter == 'with_photos') {
        filtered = filtered.where((review) {
          final images = review['images'] as List<String>;
          return images.isNotEmpty;
        }).toList();
      } else if (_selectedFilter == 'with_comments') {
        filtered = filtered.where((review) {
          final comment = review['comment'] as String?;
          return comment != null && comment.isNotEmpty;
        }).toList();
      }
    }
    
    // Apply sorting
    switch (_sortBy) {
      case 'newest':
        filtered.sort((a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));
        break;
      case 'oldest':
        filtered.sort((a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));
        break;
      case 'highest_rating':
        filtered.sort((a, b) => (b['rating'] as double).compareTo(a['rating'] as double));
        break;
      case 'lowest_rating':
        filtered.sort((a, b) => (a['rating'] as double).compareTo(b['rating'] as double));
        break;
      case 'most_helpful':
        filtered.sort((a, b) => (b['helpfulCount'] as int).compareTo(a['helpfulCount'] as int));
        break;
    }
    
    return filtered;
  }

  List<Map<String, dynamic>> _getMockReviews() {
    return [
      {
        'id': '1',
        'customerName': 'Sara Ahmed',
        'customerAvatar': null,
        'rating': 5.0,
        'date': DateTime.now().subtract(const Duration(days: 2)),
        'comment': 'Excellent service! Ahmed was very professional and gave me exactly the haircut I wanted. The salon was clean and the atmosphere was great.',
        'images': ['image1.jpg', 'image2.jpg'],
        'isAnonymous': false,
        'helpfulCount': 12,
        'notHelpfulCount': 1,
        'providerResponse': 'Thank you Sara! It was a pleasure serving you.',
        'providerResponseDate': DateTime.now().subtract(const Duration(days: 1)),
      },
      {
        'id': '2',
        'customerName': 'Mohammed Ali',
        'customerAvatar': null,
        'rating': 4.0,
        'date': DateTime.now().subtract(const Duration(days: 5)),
        'comment': 'Good service overall. The haircut was nice but I had to wait a bit longer than expected.',
        'images': [],
        'isAnonymous': false,
        'helpfulCount': 8,
        'notHelpfulCount': 2,
        'providerResponse': null,
        'providerResponseDate': null,
      },
      {
        'id': '3',
        'customerName': 'Anonymous',
        'customerAvatar': null,
        'rating': 5.0,
        'date': DateTime.now().subtract(const Duration(days: 7)),
        'comment': 'Amazing experience! Highly recommended.',
        'images': ['image3.jpg'],
        'isAnonymous': true,
        'helpfulCount': 15,
        'notHelpfulCount': 0,
        'providerResponse': 'Thank you for your kind words!',
        'providerResponseDate': DateTime.now().subtract(const Duration(days: 6)),
      },
      {
        'id': '4',
        'customerName': 'Layla Hassan',
        'customerAvatar': null,
        'rating': 3.0,
        'date': DateTime.now().subtract(const Duration(days: 10)),
        'comment': 'Service was okay. Could be better in terms of attention to detail.',
        'images': [],
        'isAnonymous': false,
        'helpfulCount': 3,
        'notHelpfulCount': 5,
        'providerResponse': 'Thank you for your feedback. We will work on improving our service.',
        'providerResponseDate': DateTime.now().subtract(const Duration(days: 9)),
      },
    ];
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sort by',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildSortOption('Newest first', 'newest'),
            _buildSortOption('Oldest first', 'oldest'),
            _buildSortOption('Highest rating', 'highest_rating'),
            _buildSortOption('Lowest rating', 'lowest_rating'),
            _buildSortOption('Most helpful', 'most_helpful'),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(String title, String value) {
    return ListTile(
      title: Text(title),
      trailing: _sortBy == value ? const Icon(Icons.check, color: AppColors.primaryPurple) : null,
      onTap: () {
        setState(() {
          _sortBy = value;
        });
        Navigator.pop(context);
      },
    );
  }

  void _writeReview() {
    Navigator.pushNamed(
      context,
      '/write-review',
      arguments: {'providerId': widget.providerId},
    );
  }

  void _markHelpful(String reviewId) {
    // TODO: Implement mark helpful
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Marked as helpful')),
    );
  }

  void _markNotHelpful(String reviewId) {
    // TODO: Implement mark not helpful
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Marked as not helpful')),
    );
  }

  void _reportReview(String reviewId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Review'),
        content: const Text('Are you sure you want to report this review?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement report review
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Review reported')),
              );
            },
            child: const Text('Report'),
          ),
        ],
      ),
    );
  }
}
