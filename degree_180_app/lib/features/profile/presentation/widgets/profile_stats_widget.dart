import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';

class ProfileStatsWidget extends StatelessWidget {
  const ProfileStatsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: EnhancedCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: _StatItem(
                  icon: Icons.calendar_today,
                  value: '12',
                  label: 'Bookings',
                  color: AppColors.primaryPurple,
                  onTap: () => _navigateToBookings(context),
                ),
              ),
              Container(
                width: 1,
                height: 50,
                color: AppColors.borderGray,
              ),
              Expanded(
                child: _StatItem(
                  icon: Icons.star,
                  value: '4.8',
                  label: 'Rating',
                  color: AppColors.starYellow,
                  onTap: () => _navigateToReviews(context),
                ),
              ),
              Container(
                width: 1,
                height: 50,
                color: AppColors.borderGray,
              ),
              Expanded(
                child: _StatItem(
                  icon: Icons.favorite,
                  value: '8',
                  label: 'Favorites',
                  color: AppColors.error,
                  onTap: () => _navigateToFavorites(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToBookings(BuildContext context) {
    Navigator.pushNamed(context, '/my-bookings');
  }

  void _navigateToReviews(BuildContext context) {
    Navigator.pushNamed(context, '/my-reviews');
  }

  void _navigateToFavorites(BuildContext context) {
    Navigator.pushNamed(context, '/favorites');
  }
}

class _StatItem extends StatelessWidget {
  final IconData icon;
  final String value;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _StatItem({
    required this.icon,
    required this.value,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textLight,
            ),
          ),
        ],
      ),
    );
  }
}
