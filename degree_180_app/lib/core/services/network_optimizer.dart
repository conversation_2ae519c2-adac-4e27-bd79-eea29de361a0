import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';

/// نظام تحسين الشبكة والاتصال
class NetworkOptimizer {
  static NetworkOptimizer? _instance;
  static NetworkOptimizer get instance => _instance ??= NetworkOptimizer._();
  
  NetworkOptimizer._();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  ConnectivityResult _currentConnectionType = ConnectivityResult.none;
  NetworkQuality _currentNetworkQuality = NetworkQuality.unknown;
  bool _isInitialized = false;
  
  final List<NetworkStatusCallback> _statusCallbacks = [];
  final Map<String, RequestConfig> _requestConfigs = {};

  /// تهيئة نظام تحسين الشبكة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // فحص حالة الاتصال الحالية
      final connectivityResults = await _connectivity.checkConnectivity();
      _currentConnectionType = connectivityResults.isNotEmpty 
          ? connectivityResults.first 
          : ConnectivityResult.none;
      
      // بدء مراقبة تغييرات الاتصال
      _startConnectivityMonitoring();
      
      // فحص جودة الشبكة
      await _checkNetworkQuality();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('NetworkOptimizer: تم تهيئة نظام تحسين الشبكة');
        print('NetworkOptimizer: نوع الاتصال: $_currentConnectionType');
        print('NetworkOptimizer: جودة الشبكة: $_currentNetworkQuality');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NetworkOptimizer: خطأ في التهيئة: $e');
      }
    }
  }

  /// بدء مراقبة تغييرات الاتصال
  void _startConnectivityMonitoring() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) async {
        final newConnectionType = results.isNotEmpty 
            ? results.first 
            : ConnectivityResult.none;
            
        if (newConnectionType != _currentConnectionType) {
          _currentConnectionType = newConnectionType;
          
          // فحص جودة الشبكة الجديدة
          await _checkNetworkQuality();
          
          // إشعار المستمعين
          _notifyStatusCallbacks();
          
          if (kDebugMode) {
            print('NetworkOptimizer: تغيير الاتصال إلى: $_currentConnectionType');
            print('NetworkOptimizer: جودة الشبكة: $_currentNetworkQuality');
          }
        }
      },
    );
  }

  /// فحص جودة الشبكة
  Future<void> _checkNetworkQuality() async {
    if (_currentConnectionType == ConnectivityResult.none) {
      _currentNetworkQuality = NetworkQuality.none;
      return;
    }

    try {
      // قياس سرعة الاستجابة
      final stopwatch = Stopwatch()..start();
      
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 5);
      dio.options.receiveTimeout = const Duration(seconds: 5);
      
      await dio.get('https://www.google.com');
      
      stopwatch.stop();
      final responseTime = stopwatch.elapsedMilliseconds;
      
      // تحديد جودة الشبكة بناءً على وقت الاستجابة
      if (responseTime < 200) {
        _currentNetworkQuality = NetworkQuality.excellent;
      } else if (responseTime < 500) {
        _currentNetworkQuality = NetworkQuality.good;
      } else if (responseTime < 1000) {
        _currentNetworkQuality = NetworkQuality.fair;
      } else {
        _currentNetworkQuality = NetworkQuality.poor;
      }
      
      if (kDebugMode) {
        print('NetworkOptimizer: وقت الاستجابة: ${responseTime}ms');
      }
    } catch (e) {
      _currentNetworkQuality = NetworkQuality.poor;
      if (kDebugMode) {
        print('NetworkOptimizer: خطأ في فحص جودة الشبكة: $e');
      }
    }
  }

  /// إشعار callbacks حالة الشبكة
  void _notifyStatusCallbacks() {
    for (final callback in _statusCallbacks) {
      try {
        callback(_currentConnectionType, _currentNetworkQuality);
      } catch (e) {
        if (kDebugMode) {
          print('NetworkOptimizer: خطأ في callback: $e');
        }
      }
    }
  }

  /// الحصول على إعدادات الطلب المحسنة
  RequestConfig getOptimizedRequestConfig(String endpoint) {
    // البحث عن إعدادات مخصصة للـ endpoint
    final customConfig = _requestConfigs[endpoint];
    if (customConfig != null) {
      return customConfig;
    }

    // إعدادات افتراضية بناءً على جودة الشبكة
    switch (_currentNetworkQuality) {
      case NetworkQuality.excellent:
        return RequestConfig(
          timeout: const Duration(seconds: 10),
          retryCount: 2,
          retryDelay: const Duration(seconds: 1),
          compressionEnabled: false,
          cacheEnabled: true,
          cacheDuration: const Duration(minutes: 5),
        );
      
      case NetworkQuality.good:
        return RequestConfig(
          timeout: const Duration(seconds: 15),
          retryCount: 3,
          retryDelay: const Duration(seconds: 2),
          compressionEnabled: true,
          cacheEnabled: true,
          cacheDuration: const Duration(minutes: 10),
        );
      
      case NetworkQuality.fair:
        return RequestConfig(
          timeout: const Duration(seconds: 20),
          retryCount: 3,
          retryDelay: const Duration(seconds: 3),
          compressionEnabled: true,
          cacheEnabled: true,
          cacheDuration: const Duration(minutes: 15),
        );
      
      case NetworkQuality.poor:
        return RequestConfig(
          timeout: const Duration(seconds: 30),
          retryCount: 5,
          retryDelay: const Duration(seconds: 5),
          compressionEnabled: true,
          cacheEnabled: true,
          cacheDuration: const Duration(hours: 1),
        );
      
      case NetworkQuality.none:
      case NetworkQuality.unknown:
        return RequestConfig(
          timeout: const Duration(seconds: 5),
          retryCount: 0,
          retryDelay: Duration.zero,
          compressionEnabled: false,
          cacheEnabled: true,
          cacheDuration: const Duration(hours: 24),
        );
    }
  }

  /// تحسين Dio instance بناءً على حالة الشبكة
  Dio createOptimizedDio({String? baseUrl}) {
    final dio = Dio();
    
    if (baseUrl != null) {
      dio.options.baseUrl = baseUrl;
    }
    
    final config = getOptimizedRequestConfig('default');
    
    // إعدادات أساسية
    dio.options.connectTimeout = config.timeout;
    dio.options.receiveTimeout = config.timeout;
    dio.options.sendTimeout = config.timeout;
    
    // تفعيل الضغط إذا كان مطلوباً
    if (config.compressionEnabled) {
      dio.options.headers['Accept-Encoding'] = 'gzip, deflate';
    }
    
    // إضافة interceptor للإعادة المحاولة
    if (config.retryCount > 0) {
      dio.interceptors.add(RetryInterceptor(
        retryCount: config.retryCount,
        retryDelay: config.retryDelay,
      ));
    }
    
    // إضافة interceptor للتخزين المؤقت
    if (config.cacheEnabled) {
      dio.interceptors.add(CacheInterceptor(
        cacheDuration: config.cacheDuration,
      ));
    }
    
    return dio;
  }

  /// تسجيل إعدادات مخصصة لـ endpoint
  void registerEndpointConfig(String endpoint, RequestConfig config) {
    _requestConfigs[endpoint] = config;
  }

  /// تسجيل callback لحالة الشبكة
  void registerStatusCallback(NetworkStatusCallback callback) {
    _statusCallbacks.add(callback);
  }

  /// إلغاء تسجيل callback لحالة الشبكة
  void unregisterStatusCallback(NetworkStatusCallback callback) {
    _statusCallbacks.remove(callback);
  }

  /// فحص إمكانية الاتصال
  bool get isConnected => _currentConnectionType != ConnectivityResult.none;

  /// الحصول على نوع الاتصال الحالي
  ConnectivityResult get connectionType => _currentConnectionType;

  /// الحصول على جودة الشبكة الحالية
  NetworkQuality get networkQuality => _currentNetworkQuality;

  /// فحص ما إذا كان الاتصال عبر WiFi
  bool get isWiFi => _currentConnectionType == ConnectivityResult.wifi;

  /// فحص ما إذا كان الاتصال عبر بيانات الجوال
  bool get isMobile => _currentConnectionType == ConnectivityResult.mobile;

  /// إيقاف نظام تحسين الشبكة
  void dispose() {
    _connectivitySubscription?.cancel();
    _statusCallbacks.clear();
    _requestConfigs.clear();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('NetworkOptimizer: تم إيقاف نظام تحسين الشبكة');
    }
  }
}

/// جودة الشبكة
enum NetworkQuality {
  none,
  unknown,
  poor,
  fair,
  good,
  excellent,
}

/// إعدادات الطلب
class RequestConfig {
  final Duration timeout;
  final int retryCount;
  final Duration retryDelay;
  final bool compressionEnabled;
  final bool cacheEnabled;
  final Duration cacheDuration;

  const RequestConfig({
    required this.timeout,
    required this.retryCount,
    required this.retryDelay,
    required this.compressionEnabled,
    required this.cacheEnabled,
    required this.cacheDuration,
  });
}

/// نوع callback لحالة الشبكة
typedef NetworkStatusCallback = void Function(
  ConnectivityResult connectionType,
  NetworkQuality networkQuality,
);

/// Interceptor للإعادة المحاولة
class RetryInterceptor extends Interceptor {
  final int retryCount;
  final Duration retryDelay;

  RetryInterceptor({
    required this.retryCount,
    required this.retryDelay,
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (_shouldRetry(err) && err.requestOptions.extra['retryCount'] == null) {
      err.requestOptions.extra['retryCount'] = 0;
    }

    final currentRetryCount = err.requestOptions.extra['retryCount'] ?? 0;
    
    if (currentRetryCount < retryCount && _shouldRetry(err)) {
      err.requestOptions.extra['retryCount'] = currentRetryCount + 1;
      
      await Future.delayed(retryDelay);
      
      try {
        final response = await Dio().fetch(err.requestOptions);
        handler.resolve(response);
        return;
      } catch (e) {
        // المحاولة التالية
      }
    }
    
    handler.next(err);
  }

  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           err.type == DioExceptionType.sendTimeout ||
           (err.type == DioExceptionType.unknown && 
            err.error is SocketException);
  }
}

/// Interceptor للتخزين المؤقت البسيط
class CacheInterceptor extends Interceptor {
  final Duration cacheDuration;
  final Map<String, CacheEntry> _cache = {};

  CacheInterceptor({
    required this.cacheDuration,
  });

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (options.method.toUpperCase() == 'GET') {
      final cacheKey = _generateCacheKey(options);
      final cacheEntry = _cache[cacheKey];
      
      if (cacheEntry != null && !cacheEntry.isExpired) {
        handler.resolve(cacheEntry.response);
        return;
      }
    }
    
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.requestOptions.method.toUpperCase() == 'GET') {
      final cacheKey = _generateCacheKey(response.requestOptions);
      _cache[cacheKey] = CacheEntry(
        response: response,
        expiryTime: DateTime.now().add(cacheDuration),
      );
    }
    
    handler.next(response);
  }

  String _generateCacheKey(RequestOptions options) {
    return '${options.method}_${options.uri}';
  }
}

/// إدخال التخزين المؤقت
class CacheEntry {
  final Response response;
  final DateTime expiryTime;

  CacheEntry({
    required this.response,
    required this.expiryTime,
  });

  bool get isExpired => DateTime.now().isAfter(expiryTime);
}
