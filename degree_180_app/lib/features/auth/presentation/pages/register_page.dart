import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/theme/theme_manager.dart';
import '../../../../core/validators/password_validator.dart';
import '../../../../core/widgets/password_strength_indicator.dart';
import '../../../../core/security/rate_limiter.dart';
import '../../domain/entities/user_entity.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/auth_header.dart';
import '../widgets/social_login_buttons.dart';
import '../widgets/gender_theme_selector.dart';
import '../bloc/auth_bloc.dart';

class RegisterPage extends StatefulWidget {
  final String? userType;
  final String? serviceType;

  const RegisterPage({
    super.key,
    this.userType,
    this.serviceType,
  });

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  Gender? _selectedGender;



  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeManager.currentTheme,
      child: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthLoading) {
            setState(() {
              _isLoading = true;
            });
          } else if (state is AuthAuthenticated) {
            setState(() {
              _isLoading = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  AppStrings.registrationSuccess,
                  style: GoogleFonts.cairo(),
                ),
                backgroundColor: Colors.green,
              ),
            );

            // Navigate based on user type
            if (widget.userType == 'customer') {
              context.go('/customer-home');
            } else {
              context.go('/worker-dashboard');
            }
          } else if (state is AuthError) {
            setState(() {
              _isLoading = false;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: GoogleFonts.cairo(),
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.backgroundWhite,
          body: SingleChildScrollView(
            child: Column(
              children: [
                // Header with gradient background
                AnimatedAuthHeader(
                  title: AppStrings.createAccount,
                  subtitle: 'انضم إلينا واستمتع بأفضل خدمات التجميل',
                  showLogo: true,
                  height: MediaQuery.of(context).size.height * 0.25,
                ),

              // Register Form
              Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: AppConstants.defaultPadding),
                // Logo
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildLogo(),
                ),
                
                const SizedBox(height: AppConstants.extraLargePadding),
                
                // Register Form
                SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // Name Field
                          CustomTextField(
                            controller: _nameController,
                            hintText: AppStrings.name,
                            keyboardType: TextInputType.name,
                            validator: _validateName,
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          // Email Field
                          CustomTextField(
                            controller: _emailController,
                            hintText: AppStrings.email,
                            keyboardType: TextInputType.emailAddress,
                            validator: _validateEmail,
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          // Password Field
                          CustomTextField(
                            controller: _passwordController,
                            hintText: AppStrings.password,
                            obscureText: _obscurePassword,
                            suffixIcon: IconButton(
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                              icon: Icon(
                                _obscurePassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: AppColors.textWhite,
                              ),
                            ),
                            validator: _validatePassword,
                            onChanged: (value) {
                              setState(() {
                                // تحديث مؤشر قوة كلمة المرور
                              });
                            },
                          ),

                          // مؤشر قوة كلمة المرور
                          if (_passwordController.text.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: PasswordStrengthIndicator(
                                password: _passwordController.text,
                                showDetails: false,
                                showSuggestions: false,
                                padding: const EdgeInsets.symmetric(horizontal: 4),
                              ),
                            ),

                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          // Confirm Password Field
                          CustomTextField(
                            controller: _confirmPasswordController,
                            hintText: AppStrings.confirmPassword,
                            obscureText: _obscureConfirmPassword,
                            suffixIcon: IconButton(
                              onPressed: () {
                                setState(() {
                                  _obscureConfirmPassword = !_obscureConfirmPassword;
                                });
                              },
                              icon: Icon(
                                _obscureConfirmPassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: AppColors.textWhite,
                              ),
                            ),
                            validator: _validateConfirmPassword,
                          ),

                          const SizedBox(height: AppConstants.defaultPadding),

                          // Gender Selection
                          GenderThemeSelector(
                            selectedGender: _selectedGender,
                            onGenderChanged: (gender) {
                              setState(() {
                                _selectedGender = gender;
                              });
                            },
                          ),

                          const SizedBox(height: AppConstants.extraLargePadding),

                          // Register Button
                          CustomButton(
                            text: AppStrings.signUp,
                            onPressed: _handleRegister,
                            isLoading: _isLoading,
                            backgroundColor: AppColors.buttonWhite,
                            textColor: AppColors.textDark,
                          ),
                          
                          const SizedBox(height: AppConstants.largePadding),
                          
                          // Divider
                          Row(
                            children: [
                              Expanded(
                                child: Container(
                                  height: 1,
                                  color: AppColors.textWhite.withOpacity(0.3),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppConstants.defaultPadding,
                                ),
                                child: Text(
                                  AppStrings.or,
                                  style: Theme.of(context).textTheme.labelMedium,
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  height: 1,
                                  color: AppColors.textWhite.withOpacity(0.3),
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: AppConstants.largePadding),
                          
                          // Social Sign Up Buttons
                          SocialButton(
                            text: AppStrings.signInWithGoogle,
                            icon: const Icon(Icons.g_mobiledata, color: AppColors.textWhite),
                            onPressed: _handleGoogleSignUp,
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          SocialButton(
                            text: AppStrings.signInWithApple,
                            icon: const Icon(Icons.apple, color: AppColors.textWhite),
                            onPressed: _handleAppleSignUp,
                          ),
                          
                          const SizedBox(height: AppConstants.largePadding),
                          
                          // Already have account
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                AppStrings.alreadyHaveAccount,
                                style: Theme.of(context).textTheme.labelMedium,
                              ),
                              TextButton(
                                onPressed: _navigateToLogin,
                                child: Text(
                                  AppStrings.signIn,
                                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                  ],
                ),
              ),
            ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '180',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w300,
                color: AppColors.textWhite,
                letterSpacing: 1,
              ),
            ),
            Container(
              width: 25,
              height: 1,
              color: AppColors.textWhite,
            ),
            const SizedBox(height: 2),
            Text(
              '°',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w300,
                color: AppColors.textWhite,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (value.length < AppConstants.minNameLength) {
      return 'Name must be at least ${AppConstants.minNameLength} characters';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (!RegExp(AppConstants.emailPattern).hasMatch(value)) {
      return AppStrings.invalidEmail;
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }

    // استخدام النظام المحسن للتحقق من كلمة المرور
    final result = PasswordValidator.validatePassword(value);
    if (!result.isValid) {
      return result.message;
    }

    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.fieldRequired;
    }
    if (value != _passwordController.text) {
      return AppStrings.passwordsDoNotMatch;
    }
    return null;
  }

  void _handleRegister() async {
    if (_selectedGender == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppStrings.genderRequired,
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_formKey.currentState!.validate()) {
      // فحص Rate Limiting قبل المحاولة
      final email = _emailController.text.trim();
      final rateLimitResult = await RateLimiter.instance.canAttempt(email);

      if (!mounted) return;

      if (!rateLimitResult.canAttempt) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              rateLimitResult.reason ?? 'تم تجاوز الحد الأقصى للمحاولات',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
        return;
      }

      // Use AuthBloc to handle registration
      context.read<AuthBloc>().add(
        SignUpWithEmailEvent(
          email: email,
          password: _passwordController.text,
          name: _nameController.text.trim(),
          userType: widget.userType == 'worker' ? UserType.worker : UserType.customer,
          serviceType: widget.serviceType == 'salon' ? ServiceType.salon
              : widget.serviceType == 'spa' ? ServiceType.spa
              : ServiceType.barbershop,
        ),
      );
    }
  }

  void _handleGoogleSignUp() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تسجيل الدخول بجوجل قريباً',
          style: GoogleFonts.cairo(),
        ),
      ),
    );
  }

  void _handleAppleSignUp() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تسجيل الدخول بآبل قريباً',
          style: GoogleFonts.cairo(),
        ),
      ),
    );
  }

  void _navigateToLogin() {
    final queryParams = <String, String>{};
    
    if (widget.userType != null) {
      queryParams['userType'] = widget.userType!;
    }
    
    if (widget.serviceType != null) {
      queryParams['serviceType'] = widget.serviceType!;
    }
    
    context.pushReplacementNamed(
      'login',
      queryParameters: queryParams,
    );
  }
}
