import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/theme_manager.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';
import '../../../booking/presentation/bloc/booking_bloc.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/stats_card.dart';
import '../widgets/shift_card.dart';

class WorkerDashboardPage extends StatefulWidget {
  const WorkerDashboardPage({super.key});

  @override
  State<WorkerDashboardPage> createState() => _WorkerDashboardPageState();
}

class _WorkerDashboardPageState extends State<WorkerDashboardPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    // Load worker data from Firebase
    context.read<BookingBloc>().add(LoadBookingStatisticsEvent(providerId: 'current_user_id'));
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with Profile
                  _buildHeader(),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // Personal Info Card
                  _buildPersonalInfoCard(),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Shift Card
                  _buildShiftCard(),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Stats Grid
                  _buildStatsGrid(),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Action Cards
                  _buildActionCards(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // Profile Avatar
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppColors.buttonWhite,
            borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
          ),
          child: const Icon(
            Icons.person,
            color: AppColors.primaryPurple,
            size: 30,
          ),
        ),
        
        const SizedBox(width: AppConstants.defaultPadding),
        
        // User Info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Ahmed Hassan',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppColors.textWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                AppStrings.barbershop,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textWhite.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
        ),
        
        // Notification Icon
        IconButton(
          onPressed: () {},
          icon: const Icon(
            Icons.notifications_outlined,
            color: AppColors.textWhite,
            size: 28,
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalInfoCard() {
    return DashboardCard(
      title: AppStrings.personalInfo,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow(Icons.location_on, AppStrings.location, 'Kuwait City'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildInfoRow(Icons.star, AppStrings.rating, '4.8 (127 ${AppStrings.reviews})'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildInfoRow(Icons.work, AppStrings.exp, '5 years'),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.smallPadding,
            ),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
            ),
            child: Text(
              AppStrings.available,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.textGray,
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Text(
          '$label: ',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textGray,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textDark,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildShiftCard() {
    return ShiftCard(
      fromTime: '09:00 ${AppStrings.am}',
      toTime: '06:00 ${AppStrings.pm}',
      duration: '9h',
      onEditPressed: () {
        // TODO: Implement edit shift
      },
    );
  }

  Widget _buildStatsGrid() {
    return Row(
      children: [
        Expanded(
          child: StatsCard(
            title: AppStrings.finished,
            value: '24',
            color: AppColors.success,
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: StatsCard(
            title: AppStrings.canceled,
            value: '3',
            color: AppColors.error,
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: StatsCard(
            title: AppStrings.requested,
            value: '7',
            color: AppColors.info,
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: StatsCard(
            title: AppStrings.extraTime,
            value: '2h',
            color: AppColors.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildActionCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: DashboardCard(
                title: AppStrings.chats,
                subtitle: AppStrings.inbox,
                icon: Icons.chat_bubble_outline,
                onTap: () {
                  // TODO: Navigate to chats
                },
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: DashboardCard(
                title: AppStrings.tools,
                subtitle: AppStrings.product,
                icon: Icons.build_outlined,
                onTap: () {
                  // TODO: Navigate to tools
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: DashboardCard(
                title: AppStrings.homeService,
                subtitle: AppStrings.jobs,
                icon: Icons.home_outlined,
                onTap: () {
                  // TODO: Navigate to home service
                },
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: DashboardCard(
                title: AppStrings.skill,
                subtitle: AppStrings.offer,
                icon: Icons.star_outline,
                onTap: () {
                  // TODO: Navigate to skills
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
