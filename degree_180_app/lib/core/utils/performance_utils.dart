import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PerformanceUtils {
  static final Map<String, Completer<dynamic>> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _defaultCacheExpiry = Duration(minutes: 5);

  /// Cache a future result with optional expiry time
  static Future<T> cacheResult<T>(
    String key,
    Future<T> Function() futureFunction, {
    Duration? expiry,
  }) async {
    final cacheExpiry = expiry ?? _defaultCacheExpiry;
    
    // Check if we have a valid cached result
    if (_cache.containsKey(key) && _cacheTimestamps.containsKey(key)) {
      final timestamp = _cacheTimestamps[key]!;
      if (DateTime.now().difference(timestamp) < cacheExpiry) {
        return await _cache[key]!.future as T;
      } else {
        // Cache expired, remove it
        _cache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }

    // Create new cache entry
    final completer = Completer<T>();
    _cache[key] = completer;
    _cacheTimestamps[key] = DateTime.now();

    try {
      final result = await futureFunction();
      completer.complete(result);
      return result;
    } catch (error) {
      completer.completeError(error);
      _cache.remove(key);
      _cacheTimestamps.remove(key);
      rethrow;
    }
  }

  /// Clear specific cache entry
  static void clearCache(String key) {
    _cache.remove(key);
    _cacheTimestamps.remove(key);
  }

  /// Clear all cache
  static void clearAllCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  /// Debounce function calls
  static Timer? _debounceTimer;
  
  static void debounce(
    Duration duration,
    VoidCallback callback,
  ) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(duration, callback);
  }

  /// Throttle function calls
  static DateTime? _lastThrottleTime;
  
  static void throttle(
    Duration duration,
    VoidCallback callback,
  ) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || 
        now.difference(_lastThrottleTime!) >= duration) {
      _lastThrottleTime = now;
      callback();
    }
  }

  /// Preload images for better performance
  static Future<void> preloadImages(
    List<String> imageUrls,
    BuildContext context,
  ) async {
    final futures = imageUrls.map((url) {
      if (url.startsWith('http')) {
        return precacheImage(NetworkImage(url), context);
      } else {
        return precacheImage(AssetImage(url), context);
      }
    });
    
    await Future.wait(futures);
  }

  /// Optimize list performance with pagination
  static List<T> paginateList<T>(
    List<T> items,
    int page,
    int itemsPerPage,
  ) {
    final startIndex = page * itemsPerPage;
    final endIndex = (startIndex + itemsPerPage).clamp(0, items.length);
    
    if (startIndex >= items.length) return [];
    
    return items.sublist(startIndex, endIndex);
  }

  /// Memory optimization for large lists
  static void optimizeMemory() {
    // Force garbage collection (use sparingly)
    if (kDebugMode) {
      print('Optimizing memory...');
    }
    
    // Clear image cache if needed
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  /// Measure performance of a function
  static Future<T> measurePerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      stopwatch.stop();
      
      if (kDebugMode) {
        print('$operationName took ${stopwatch.elapsedMilliseconds}ms');
      }
      
      return result;
    } catch (error) {
      stopwatch.stop();
      
      if (kDebugMode) {
        print('$operationName failed after ${stopwatch.elapsedMilliseconds}ms');
      }
      
      rethrow;
    }
  }

  /// Batch operations for better performance
  static Future<List<T>> batchOperations<T>(
    List<Future<T> Function()> operations,
    int batchSize,
  ) async {
    final results = <T>[];
    
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations
          .skip(i)
          .take(batchSize)
          .map((op) => op())
          .toList();
      
      final batchResults = await Future.wait(batch);
      results.addAll(batchResults);
      
      // Small delay between batches to prevent overwhelming
      if (i + batchSize < operations.length) {
        await Future.delayed(const Duration(milliseconds: 10));
      }
    }
    
    return results;
  }

  /// Reduce haptic feedback for better battery life
  static void lightHapticFeedback() {
    HapticFeedback.lightImpact();
  }

  static void mediumHapticFeedback() {
    HapticFeedback.mediumImpact();
  }

  static void heavyHapticFeedback() {
    HapticFeedback.heavyImpact();
  }

  /// Check if device has sufficient resources
  static bool hasLowMemory() {
    // This is a simplified check - in a real app you might use
    // platform-specific code to check actual memory usage
    return false;
  }

  /// Adaptive quality based on device performance
  static ImageQuality getAdaptiveImageQuality() {
    if (hasLowMemory()) {
      return ImageQuality.low;
    }
    return ImageQuality.high;
  }
}

enum ImageQuality {
  low,
  medium,
  high,
}

/// Mixin for widgets that need performance optimization
mixin PerformanceOptimizedWidget {
  bool _isDisposed = false;
  
  void markDisposed() {
    _isDisposed = true;
  }
  
  bool get isDisposed => _isDisposed;
  
  /// Safe setState that checks if widget is still mounted
  void safeSetState(VoidCallback fn) {
    if (!_isDisposed) {
      fn();
    }
  }
}

/// Custom scroll physics for better performance
class OptimizedScrollPhysics extends BouncingScrollPhysics {
  const OptimizedScrollPhysics({super.parent});

  @override
  OptimizedScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return OptimizedScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  double get minFlingVelocity => 100.0;

  @override
  double get maxFlingVelocity => 5000.0;
}
