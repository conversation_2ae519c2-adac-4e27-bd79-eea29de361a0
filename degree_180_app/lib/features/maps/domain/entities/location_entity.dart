import 'package:equatable/equatable.dart';

class LocationEntity extends Equatable {
  final double latitude;
  final double longitude;
  final String? address;
  final String? city;
  final String? country;
  final String? postalCode;
  final String? name;
  final String? description;

  const LocationEntity({
    required this.latitude,
    required this.longitude,
    this.address,
    this.city,
    this.country,
    this.postalCode,
    this.name,
    this.description,
  });

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        address,
        city,
        country,
        postalCode,
        name,
        description,
      ];

  LocationEntity copyWith({
    double? latitude,
    double? longitude,
    String? address,
    String? city,
    String? country,
    String? postalCode,
    String? name,
    String? description,
  }) {
    return LocationEntity(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      name: name ?? this.name,
      description: description ?? this.description,
    );
  }

  String get fullAddress {
    final parts = <String>[];
    if (address != null && address!.isNotEmpty) parts.add(address!);
    if (city != null && city!.isNotEmpty) parts.add(city!);
    if (country != null && country!.isNotEmpty) parts.add(country!);
    return parts.join(', ');
  }

  String get coordinates => '$latitude, $longitude';

  double distanceTo(LocationEntity other) {
    // Simple distance calculation (not accurate for long distances)
    final latDiff = latitude - other.latitude;
    final lonDiff = longitude - other.longitude;
    return (latDiff * latDiff + lonDiff * lonDiff) * 111; // Approximate km
  }

  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
      'name': name,
      'description': description,
    };
  }

  factory LocationEntity.fromMap(Map<String, dynamic> map) {
    return LocationEntity(
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
      address: map['address'],
      city: map['city'],
      country: map['country'],
      postalCode: map['postalCode'],
      name: map['name'],
      description: map['description'],
    );
  }
}
