import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class ReviewImagesGrid extends StatelessWidget {
  final List<String> images;

  const ReviewImagesGrid({
    super.key,
    required this.images,
  });

  @override
  Widget build(BuildContext context) {
    if (images.isEmpty) return const SizedBox.shrink();

    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length > 4 ? 4 : images.length,
        itemBuilder: (context, index) {
          if (index == 3 && images.length > 4) {
            return _buildMoreImagesIndicator(images.length - 3);
          }
          return _buildImageItem(images[index], index);
        },
      ),
    );
  }

  Widget _buildImageItem(String imageUrl, int index) {
    return Container(
      width: 80,
      height: 80,
      margin: const EdgeInsets.only(right: AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: AppColors.backgroundGray,
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(color: AppColors.borderGray),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        child: imageUrl.startsWith('http')
            ? Image.network(
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholder();
                },
              )
            : _buildPlaceholder(),
      ),
    );
  }

  Widget _buildMoreImagesIndicator(int remainingCount) {
    return Container(
      width: 80,
      height: 80,
      margin: const EdgeInsets.only(right: AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: AppColors.primaryPurple.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(color: AppColors.primaryPurple.withValues(alpha: 0.3)),
      ),
      child: Center(
        child: Text(
          '+$remainingCount',
          style: const TextStyle(
            color: AppColors.primaryPurple,
            fontWeight: FontWeight.w600,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: AppColors.backgroundGray,
      child: Icon(
        Icons.image,
        color: AppColors.textGray.withValues(alpha: 0.5),
        size: 32,
      ),
    );
  }
}
