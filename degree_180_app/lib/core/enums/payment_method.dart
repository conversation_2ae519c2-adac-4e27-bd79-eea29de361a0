import 'package:flutter/material.dart';

/// طرق الدفع المتاحة
enum PaymentMethod {
  cash,
  creditCard,
  debitCard,
  knet,
  applePay,
  googlePay,
  paypal,
  bankTransfer,
  wallet,
}

/// امتداد لطرق الدفع
extension PaymentMethodExtension on PaymentMethod {
  /// الحصول على اسم طريقة الدفع للعرض
  String get paymentMethodDisplayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'نقداً';
      case PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
      case PaymentMethod.debitCard:
        return 'بطاقة خصم';
      case PaymentMethod.knet:
        return 'كي نت';
      case PaymentMethod.applePay:
        return 'Apple Pay';
      case PaymentMethod.googlePay:
        return 'Google Pay';
      case PaymentMethod.paypal:
        return 'PayPal';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.wallet:
        return 'محفظة إلكترونية';
    }
  }

  /// الحصول على اسم طريقة الدفع بالإنجليزية
  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.knet:
        return 'K-Net';
      case PaymentMethod.applePay:
        return 'Apple Pay';
      case PaymentMethod.googlePay:
        return 'Google Pay';
      case PaymentMethod.paypal:
        return 'PayPal';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.wallet:
        return 'Digital Wallet';
    }
  }

  /// الحصول على أيقونة طريقة الدفع
  IconData get icon {
    switch (this) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.creditCard:
        return Icons.credit_card;
      case PaymentMethod.debitCard:
        return Icons.payment;
      case PaymentMethod.knet:
        return Icons.account_balance;
      case PaymentMethod.applePay:
        return Icons.phone_iphone;
      case PaymentMethod.googlePay:
        return Icons.android;
      case PaymentMethod.paypal:
        return Icons.account_balance_wallet;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.wallet:
        return Icons.wallet;
    }
  }

  /// الحصول على لون طريقة الدفع
  Color get color {
    switch (this) {
      case PaymentMethod.cash:
        return Colors.green;
      case PaymentMethod.creditCard:
        return Colors.blue;
      case PaymentMethod.debitCard:
        return Colors.purple;
      case PaymentMethod.knet:
        return Colors.orange;
      case PaymentMethod.applePay:
        return Colors.black;
      case PaymentMethod.googlePay:
        return Colors.blue;
      case PaymentMethod.paypal:
        return Colors.blue;
      case PaymentMethod.bankTransfer:
        return Colors.indigo;
      case PaymentMethod.wallet:
        return Colors.teal;
    }
  }

  /// فحص ما إذا كانت طريقة الدفع رقمية
  bool get isDigital {
    switch (this) {
      case PaymentMethod.cash:
        return false;
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
      case PaymentMethod.knet:
      case PaymentMethod.applePay:
      case PaymentMethod.googlePay:
      case PaymentMethod.paypal:
      case PaymentMethod.bankTransfer:
      case PaymentMethod.wallet:
        return true;
    }
  }

  /// فحص ما إذا كانت طريقة الدفع تتطلب معلومات إضافية
  bool get requiresAdditionalInfo {
    switch (this) {
      case PaymentMethod.cash:
      case PaymentMethod.applePay:
      case PaymentMethod.googlePay:
        return false;
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
      case PaymentMethod.knet:
      case PaymentMethod.paypal:
      case PaymentMethod.bankTransfer:
      case PaymentMethod.wallet:
        return true;
    }
  }

  /// الحصول على رسوم المعاملة (نسبة مئوية)
  double get transactionFee {
    switch (this) {
      case PaymentMethod.cash:
        return 0.0;
      case PaymentMethod.creditCard:
        return 2.5;
      case PaymentMethod.debitCard:
        return 1.5;
      case PaymentMethod.knet:
        return 0.5;
      case PaymentMethod.applePay:
        return 1.0;
      case PaymentMethod.googlePay:
        return 1.0;
      case PaymentMethod.paypal:
        return 3.0;
      case PaymentMethod.bankTransfer:
        return 0.0;
      case PaymentMethod.wallet:
        return 1.0;
    }
  }

  /// الحصول على الحد الأدنى للمبلغ
  double get minimumAmount {
    switch (this) {
      case PaymentMethod.cash:
        return 0.0;
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return 1.0;
      case PaymentMethod.knet:
        return 0.5;
      case PaymentMethod.applePay:
      case PaymentMethod.googlePay:
        return 0.1;
      case PaymentMethod.paypal:
        return 1.0;
      case PaymentMethod.bankTransfer:
        return 10.0;
      case PaymentMethod.wallet:
        return 0.1;
    }
  }

  /// الحصول على الحد الأقصى للمبلغ
  double get maximumAmount {
    switch (this) {
      case PaymentMethod.cash:
        return 500.0;
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return 10000.0;
      case PaymentMethod.knet:
        return 5000.0;
      case PaymentMethod.applePay:
      case PaymentMethod.googlePay:
        return 3000.0;
      case PaymentMethod.paypal:
        return 5000.0;
      case PaymentMethod.bankTransfer:
        return 50000.0;
      case PaymentMethod.wallet:
        return 2000.0;
    }
  }

  /// تحويل من نص إلى enum
  static PaymentMethod fromString(String value) {
    switch (value.toLowerCase()) {
      case 'cash':
        return PaymentMethod.cash;
      case 'creditcard':
      case 'credit_card':
        return PaymentMethod.creditCard;
      case 'debitcard':
      case 'debit_card':
        return PaymentMethod.debitCard;
      case 'knet':
        return PaymentMethod.knet;
      case 'applepay':
      case 'apple_pay':
        return PaymentMethod.applePay;
      case 'googlepay':
      case 'google_pay':
        return PaymentMethod.googlePay;
      case 'paypal':
        return PaymentMethod.paypal;
      case 'banktransfer':
      case 'bank_transfer':
        return PaymentMethod.bankTransfer;
      case 'wallet':
        return PaymentMethod.wallet;
      default:
        return PaymentMethod.cash;
    }
  }

  /// تحويل إلى نص
  String toStringValue() {
    return toString().split('.').last;
  }
}

/// فئة مساعدة لطرق الدفع
class PaymentMethodHelper {
  /// الحصول على جميع طرق الدفع المتاحة
  static List<PaymentMethod> get availableMethods => PaymentMethod.values;

  /// الحصول على طرق الدفع الرقمية
  static List<PaymentMethod> get digitalMethods =>
      PaymentMethod.values.where((method) => method.isDigital).toList();

  /// الحصول على طرق الدفع التقليدية
  static List<PaymentMethod> get traditionalMethods =>
      PaymentMethod.values.where((method) => !method.isDigital).toList();

  /// فحص ما إذا كان المبلغ صالح لطريقة الدفع
  static bool isAmountValid(PaymentMethod method, double amount) {
    return amount >= method.minimumAmount && amount <= method.maximumAmount;
  }

  /// حساب رسوم المعاملة
  static double calculateTransactionFee(PaymentMethod method, double amount) {
    return amount * (method.transactionFee / 100);
  }

  /// الحصول على المبلغ الإجمالي مع الرسوم
  static double getTotalAmount(PaymentMethod method, double amount) {
    return amount + calculateTransactionFee(method, amount);
  }
}
