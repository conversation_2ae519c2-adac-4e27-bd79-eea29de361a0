import 'package:equatable/equatable.dart';
import '../../../../core/models/time_slot_model.dart';

/// أحداث الحجز المتقدم
abstract class AdvancedBookingEvent extends Equatable {
  const AdvancedBookingEvent();

  @override
  List<Object?> get props => [];
}

/// حدث تحميل الفترات الزمنية المتاحة
class LoadAvailableTimeSlotsEvent extends AdvancedBookingEvent {
  final String providerId;
  final String serviceId;
  final DateTime date;

  const LoadAvailableTimeSlotsEvent({
    required this.providerId,
    required this.serviceId,
    required this.date,
  });

  @override
  List<Object?> get props => [providerId, serviceId, date];
}

/// حدث إنشاء حجز متقدم
class CreateAdvancedBookingEvent extends AdvancedBookingEvent {
  final String customerId;
  final String providerId;
  final String serviceId;
  final TimeSlotModel timeSlot;
  final Map<String, dynamic>? additionalData;
  final String? notes;
  final String? couponCode;

  const CreateAdvancedBookingEvent({
    required this.customerId,
    required this.providerId,
    required this.serviceId,
    required this.timeSlot,
    this.additionalData,
    this.notes,
    this.couponCode,
  });

  @override
  List<Object?> get props => [
        customerId,
        providerId,
        serviceId,
        timeSlot,
        additionalData,
        notes,
        couponCode,
      ];
}

/// حدث إعادة جدولة الحجز
class RescheduleBookingEvent extends AdvancedBookingEvent {
  final String bookingId;
  final TimeSlotModel newTimeSlot;
  final String? reason;

  const RescheduleBookingEvent({
    required this.bookingId,
    required this.newTimeSlot,
    this.reason,
  });

  @override
  List<Object?> get props => [bookingId, newTimeSlot, reason];
}

/// حدث إلغاء الحجز
class CancelBookingEvent extends AdvancedBookingEvent {
  final String bookingId;
  final String reason;
  final bool refundRequested;

  const CancelBookingEvent({
    required this.bookingId,
    required this.reason,
    this.refundRequested = false,
  });

  @override
  List<Object?> get props => [bookingId, reason, refundRequested];
}

/// حدث تأكيد الحجز
class ConfirmBookingEvent extends AdvancedBookingEvent {
  final String bookingId;

  const ConfirmBookingEvent({
    required this.bookingId,
  });

  @override
  List<Object?> get props => [bookingId];
}

/// حدث بدء الخدمة
class StartServiceEvent extends AdvancedBookingEvent {
  final String bookingId;

  const StartServiceEvent({
    required this.bookingId,
  });

  @override
  List<Object?> get props => [bookingId];
}

/// حدث إكمال الخدمة
class CompleteServiceEvent extends AdvancedBookingEvent {
  final String bookingId;
  final String? notes;
  final List<String>? images;

  const CompleteServiceEvent({
    required this.bookingId,
    this.notes,
    this.images,
  });

  @override
  List<Object?> get props => [bookingId, notes, images];
}

/// حدث تحميل إحصائيات الحجز
class LoadBookingStatisticsEvent extends AdvancedBookingEvent {
  final String? providerId;
  final String? customerId;
  final DateTime? startDate;
  final DateTime? endDate;

  const LoadBookingStatisticsEvent({
    this.providerId,
    this.customerId,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [providerId, customerId, startDate, endDate];
}

/// حدث تحميل تاريخ الحجوزات
class LoadBookingHistoryEvent extends AdvancedBookingEvent {
  final String userId;
  final int page;
  final int limit;
  final String? status;

  const LoadBookingHistoryEvent({
    required this.userId,
    this.page = 1,
    this.limit = 20,
    this.status,
  });

  @override
  List<Object?> get props => [userId, page, limit, status];
}

/// حدث البحث عن الحجوزات
class SearchBookingsEvent extends AdvancedBookingEvent {
  final String query;
  final Map<String, dynamic>? filters;

  const SearchBookingsEvent({
    required this.query,
    this.filters,
  });

  @override
  List<Object?> get props => [query, filters];
}

/// حدث تحديث حالة الحجز
class UpdateBookingStatusEvent extends AdvancedBookingEvent {
  final String bookingId;
  final String newStatus;
  final String? reason;

  const UpdateBookingStatusEvent({
    required this.bookingId,
    required this.newStatus,
    this.reason,
  });

  @override
  List<Object?> get props => [bookingId, newStatus, reason];
}

/// حدث إرسال تذكير
class SendReminderEvent extends AdvancedBookingEvent {
  final String bookingId;
  final String reminderType;

  const SendReminderEvent({
    required this.bookingId,
    required this.reminderType,
  });

  @override
  List<Object?> get props => [bookingId, reminderType];
}

/// حدث تقييم الخدمة
class RateServiceEvent extends AdvancedBookingEvent {
  final String bookingId;
  final double rating;
  final String? review;
  final List<String>? images;

  const RateServiceEvent({
    required this.bookingId,
    required this.rating,
    this.review,
    this.images,
  });

  @override
  List<Object?> get props => [bookingId, rating, review, images];
}

/// حدث تحديث تفاصيل الحجز
class UpdateBookingDetailsEvent extends AdvancedBookingEvent {
  final String bookingId;
  final Map<String, dynamic> updates;

  const UpdateBookingDetailsEvent({
    required this.bookingId,
    required this.updates,
  });

  @override
  List<Object?> get props => [bookingId, updates];
}

/// حدث تحميل الحجوزات القادمة
class LoadUpcomingBookingsEvent extends AdvancedBookingEvent {
  final String userId;
  final int days;

  const LoadUpcomingBookingsEvent({
    required this.userId,
    this.days = 7,
  });

  @override
  List<Object?> get props => [userId, days];
}

/// حدث إعادة تعيين الحالة
class ResetAdvancedBookingStateEvent extends AdvancedBookingEvent {
  const ResetAdvancedBookingStateEvent();
}

/// حدث تحديث الفترات الزمنية
class RefreshTimeSlotsEvent extends AdvancedBookingEvent {
  final String providerId;
  final String serviceId;
  final DateTime date;

  const RefreshTimeSlotsEvent({
    required this.providerId,
    required this.serviceId,
    required this.date,
  });

  @override
  List<Object?> get props => [providerId, serviceId, date];
}
