# 🚀 دليل التشغيل السريع - تطبيق 180 درجة

## ⚡ البدء السريع (5 دقائق)

### 1. 📥 تحميل المشروع
```bash
git clone https://github.com/your-repo/degree_180_app.git
cd degree_180_app
```

### 2. 📦 تثبيت التبعيات
```bash
flutter pub get
```

### 3. 🔥 إعداد Firebase (اختياري للتطوير)
```bash
# إنشاء مشروع Firebase جديد
firebase login
firebase init

# أو استخدام التكوين التجريبي المرفق
cp firebase_config_demo.json android/app/google-services.json
cp firebase_config_demo.plist ios/Runner/GoogleService-Info.plist
```

### 4. 🗺️ إعداد Google Maps (اختياري)
```bash
# إضافة API Key في android/app/src/main/AndroidManifest.xml
# إضافة API Key في ios/Runner/AppDelegate.swift
```

### 5. ▶️ تشغيل التطبيق
```bash
flutter run
```

## 🎯 الميزات الجاهزة للاختبار

### ✅ ما يعمل فوراً (بدون إعداد)
- 🏠 **الصفحة الرئيسية**: تصفح الخدمات والمقدمين
- 🔍 **البحث والفلترة**: بحث متقدم مع فلاتر
- 📅 **نظام الحجز**: حجز المواعيد (محاكاة)
- 💳 **نظام الدفع**: واجهات الدفع المتكاملة
- ⭐ **المراجعات**: كتابة وعرض المراجعات
- 👤 **الملف الشخصي**: إدارة البيانات الشخصية
- 🎨 **واجهة المستخدم**: جميع الشاشات والمكونات

### 🔧 ما يحتاج إعداد للعمل الكامل
- 🔐 **المصادقة**: تحتاج Firebase Auth
- 💬 **المحادثات**: تحتاج Firestore
- 📍 **الخرائط**: تحتاج Google Maps API
- 🔔 **الإشعارات**: تحتاج FCM
- 💰 **الدفع الحقيقي**: تحتاج بوابات الدفع

## 📱 اختبار الميزات

### 🏠 الصفحة الرئيسية
```
1. افتح التطبيق
2. تصفح الخدمات المختلفة
3. استخدم شريط البحث
4. جرب الفلاتر (النوع، المسافة، التقييم)
```

### 📅 نظام الحجز
```
1. اختر خدمة من الصفحة الرئيسية
2. اختر مقدم الخدمة
3. حدد التاريخ والوقت
4. أكمل عملية الحجز
```

### 💳 نظام الدفع
```
1. أكمل عملية الحجز
2. اختر طريقة الدفع
3. أدخل بيانات البطاقة (تجريبية)
4. شاهد صفحة نجاح الدفع
```

### ⭐ المراجعات
```
1. اذهب إلى صفحة مقدم الخدمة
2. اضغط "كتابة مراجعة"
3. قيم الخدمة بالنجوم
4. اكتب تعليقك وأضف صور
```

## 🧪 بيانات تجريبية

### 👤 مستخدمين تجريبيين
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: 123456

البريد الإلكتروني: <EMAIL>
كلمة المرور: 123456
```

### 💳 بطاقات تجريبية
```
رقم البطاقة: 4111 1111 1111 1111
تاريخ الانتهاء: 12/25
CVV: 123
الاسم: Test User
```

### 🎫 أكواد خصم تجريبية
```
WELCOME10 - خصم 10%
SAVE5 - خصم 5 دينار
NEWUSER - خصم 15% للمستخدمين الجدد
```

## 🔧 إعدادات التطوير

### 🎨 تخصيص الألوان
```dart
// lib/core/constants/app_colors.dart
class AppColors {
  static const primaryPurple = Color(0xFF6C63FF);
  static const primaryPurpleLight = Color(0xFF9C88FF);
  // يمكنك تغيير الألوان هنا
}
```

### 📝 تخصيص النصوص
```dart
// lib/core/constants/app_strings.dart
class AppStrings {
  static const appName = '180 Degree';
  // يمكنك تغيير النصوص هنا
}
```

### 🏗️ إضافة ميزات جديدة
```
1. أنشئ مجلد جديد في lib/features/
2. اتبع هيكل Clean Architecture
3. أضف النماذج في core/models/
4. أضف الخدمات في core/services/
```

## 🐛 حل المشاكل الشائعة

### ❌ خطأ في التبعيات
```bash
flutter clean
flutter pub get
```

### ❌ خطأ في Firebase
```bash
# تأكد من وجود ملفات التكوين
ls android/app/google-services.json
ls ios/Runner/GoogleService-Info.plist
```

### ❌ خطأ في Google Maps
```bash
# تأكد من إضافة API Key
grep -r "YOUR_GOOGLE_MAPS_API_KEY" android/
grep -r "YOUR_GOOGLE_MAPS_API_KEY" ios/
```

### ❌ خطأ في البناء
```bash
# للـ Android
flutter build apk --debug

# للـ iOS
flutter build ios --debug
```

## 📚 موارد إضافية

### 📖 الوثائق
- [Flutter Documentation](https://docs.flutter.dev/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Google Maps Documentation](https://developers.google.com/maps)

### 🎥 فيديوهات تعليمية
- [Flutter Basics](https://www.youtube.com/watch?v=1ukSR1GRtMU)
- [Firebase with Flutter](https://www.youtube.com/watch?v=EXp0gq9kGxI)
- [State Management with BLoC](https://www.youtube.com/watch?v=THCkkQ-V1-8)

### 🛠️ أدوات مفيدة
- [Flutter Inspector](https://docs.flutter.dev/development/tools/flutter-inspector)
- [Firebase Console](https://console.firebase.google.com/)
- [Google Cloud Console](https://console.cloud.google.com/)

## 🤝 المساهمة

### 🔄 سير العمل
```
1. Fork المشروع
2. أنشئ branch جديد (git checkout -b feature/amazing-feature)
3. Commit التغييرات (git commit -m 'Add amazing feature')
4. Push للـ branch (git push origin feature/amazing-feature)
5. افتح Pull Request
```

### 📋 معايير الكود
- اتبع [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- اكتب تعليقات واضحة
- أضف اختبارات للميزات الجديدة
- تأكد من عمل جميع الاختبارات

## 📞 الدعم

### 💬 طرق التواصل
- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [رابط المشاكل](https://github.com/your-repo/degree_180_app/issues)
- **Discord**: [رابط الخادم](https://discord.gg/your-server)

### ❓ الأسئلة الشائعة
**س: كيف أغير لغة التطبيق؟**
ج: التطبيق يدعم العربية والإنجليزية تلقائياً حسب إعدادات الجهاز.

**س: كيف أضيف خدمة جديدة؟**
ج: أضف الخدمة في ServiceType enum وأنشئ الواجهات المطلوبة.

**س: كيف أختبر الدفع؟**
ج: استخدم بيانات البطاقات التجريبية المذكورة أعلاه.

---

## 🎉 مبروك!

أنت الآن جاهز لاستكشاف تطبيق 180 درجة المكتمل! 

**استمتع بالتطوير! 🚀**
