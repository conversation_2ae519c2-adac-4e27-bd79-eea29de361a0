import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/theme/theme_manager.dart';
import '../bloc/auth_flow_bloc.dart';

/// اختيار الجنس العصري والمحسن
class ModernGenderSelector extends StatefulWidget {
  const ModernGenderSelector({super.key});

  @override
  State<ModernGenderSelector> createState() => _ModernGenderSelectorState();
}

class _ModernGenderSelectorState extends State<ModernGenderSelector>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _cardAnimations;
  late List<Animation<double>> _scaleAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _cardAnimations = List.generate(2, (index) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.3,
          0.7 + (index * 0.3),
          curve: Curves.elasticOut,
        ),
      ));
    });

    _scaleAnimations = List.generate(2, (index) {
      return Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.2,
          0.6 + (index * 0.2),
          curve: Curves.bounceOut,
        ),
      ));
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // وصف الثيم
        _buildThemeDescription(),
        
        const SizedBox(height: AppConstants.extraLargePadding),
        
        // خيارات الجنس
        Expanded(
          child: Row(
            children: [
              // ثيم نسائي
              Expanded(
                child: AnimatedBuilder(
                  animation: _cardAnimations[0],
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimations[0].value,
                      child: Opacity(
                        opacity: _cardAnimations[0].value,
                        child: _buildGenderCard(
                          gender: Gender.female,
                          title: 'ثيم نسائي',
                          subtitle: 'ألوان وردية ناعمة\nتصميم أنثوي راقي',
                          icon: Icons.favorite_rounded,
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFFF6B9D),
                              Color(0xFFFF8E9B),
                              Color(0xFFFFA8B5),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          decorativeIcon: Icons.auto_awesome,
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(width: AppConstants.defaultPadding),
              
              // ثيم رجالي
              Expanded(
                child: AnimatedBuilder(
                  animation: _cardAnimations[1],
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimations[1].value,
                      child: Opacity(
                        opacity: _cardAnimations[1].value,
                        child: _buildGenderCard(
                          gender: Gender.male,
                          title: 'ثيم رجالي',
                          subtitle: 'ألوان زرقاء قوية\nتصميم عصري وأنيق',
                          icon: Icons.star_rounded,
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFF4A90E2),
                              Color(0xFF357ABD),
                              Color(0xFF2E5F8A),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          decorativeIcon: Icons.bolt,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildThemeDescription() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: ThemeManager.currentColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(
          color: ThemeManager.currentColors.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: ThemeManager.currentColors.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.palette_rounded,
              color: AppColors.textWhite,
              size: 24,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'اختر ثيمك المفضل',
                  style: GoogleFonts.cairo(
                    fontSize: AppConstants.fontSizeMedium,
                    fontWeight: FontWeight.bold,
                    color: ThemeManager.currentColors.primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'سيتم تطبيق الألوان والتصميم حسب اختيارك في جميع أنحاء التطبيق',
                  style: GoogleFonts.cairo(
                    fontSize: AppConstants.fontSizeSmall,
                    color: ThemeManager.currentColors.primary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderCard({
    required Gender gender,
    required String title,
    required String subtitle,
    required IconData icon,
    required Gradient gradient,
    required IconData decorativeIcon,
  }) {
    return BlocBuilder<AuthFlowBloc, AuthFlowState>(
      builder: (context, state) {
        final isSelected = state is AuthFlowInProgress && state.selectedGender == gender;
        
        return GestureDetector(
          onTap: () {
            context.read<AuthFlowBloc>().add(AuthFlowGenderSelected(gender));
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: 220,
            decoration: BoxDecoration(
              gradient: gradient,
              borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
              boxShadow: [
                BoxShadow(
                  color: gradient.colors.first.withOpacity(isSelected ? 0.4 : 0.2),
                  blurRadius: isSelected ? 25 : 15,
                  spreadRadius: isSelected ? 3 : 1,
                  offset: const Offset(0, 8),
                ),
              ],
              border: isSelected
                  ? Border.all(color: AppColors.textWhite, width: 3)
                  : null,
            ),
            child: Stack(
              children: [
                // أيقونة الخلفية الزخرفية
                Positioned(
                  top: -30,
                  right: -30,
                  child: Icon(
                    decorativeIcon,
                    size: 120,
                    color: AppColors.textWhite.withOpacity(0.15),
                  ),
                ),
                
                // المحتوى الرئيسي
                Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // الأيقونة الرئيسية
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.textWhite.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          icon,
                          color: AppColors.textWhite,
                          size: 28,
                        ),
                      ),
                      
                      const Spacer(),
                      
                      // العنوان
                      Text(
                        title,
                        style: GoogleFonts.cairo(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textWhite,
                        ),
                      ),
                      
                      const SizedBox(height: AppConstants.smallPadding),
                      
                      // الوصف
                      Text(
                        subtitle,
                        style: GoogleFonts.cairo(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppColors.textWhite.withOpacity(0.9),
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // علامة الاختيار
                if (isSelected)
                  Positioned(
                    top: AppConstants.smallPadding,
                    left: AppConstants.smallPadding,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: 36,
                      height: 36,
                      decoration: const BoxDecoration(
                        color: AppColors.textWhite,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black26,
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.check_rounded,
                        color: gradient.colors.first,
                        size: 24,
                      ),
                    ),
                  ),
                
                // تأثير الضوء
                if (isSelected)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(AppConstants.largeBorderRadius),
                        gradient: LinearGradient(
                          colors: [
                            AppColors.textWhite.withOpacity(0.1),
                            Colors.transparent,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
