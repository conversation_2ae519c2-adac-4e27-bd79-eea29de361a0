import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class PaymentSummaryCard extends StatelessWidget {
  final double amount;
  final double tipAmount;
  final double taxAmount;
  final double totalAmount;

  const PaymentSummaryCard({
    super.key,
    required this.amount,
    required this.tipAmount,
    required this.taxAmount,
    required this.totalAmount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.buttonWhite.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(
          color: AppColors.buttonWhite.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Summary',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textWhite,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          _buildSummaryRow('Service Amount', amount),
          if (tipAmount > 0) _buildSummaryRow('Tip', tipAmount),
          _buildSummaryRow('Tax', taxAmount),
          
          const SizedBox(height: AppConstants.smallPadding),
          Divider(color: AppColors.textWhite.withValues(alpha: 0.3)),
          const SizedBox(height: AppConstants.smallPadding),
          
          _buildSummaryRow('Total', totalAmount, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: AppColors.textWhite,
              fontSize: isTotal ? AppConstants.fontSizeLarge : AppConstants.fontSizeMedium,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} KD',
            style: TextStyle(
              color: AppColors.textWhite,
              fontSize: isTotal ? AppConstants.fontSizeLarge : AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
