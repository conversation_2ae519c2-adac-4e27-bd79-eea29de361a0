import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import '../enums/user_type.dart';
import '../enums/gender.dart';
import 'location_model.dart';

class UserModel extends Equatable {
  final String id;
  final String email;
  final String? displayName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final UserType userType;
  final Gender gender;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final LocationModel? location;
  final Map<String, dynamic> preferences;
  final List<String> favoriteProviders;
  final int totalBookings;
  final double totalSpent;
  final int reviewsCount;
  final double averageRating;
  final bool isActive;
  final String? fcmToken;
  final Map<String, bool> notificationSettings;

  const UserModel({
    required this.id,
    required this.email,
    this.displayName,
    this.phoneNumber,
    this.profileImageUrl,
    required this.userType,
    this.gender = Gender.notSpecified,
    required this.createdAt,
    this.lastLoginAt,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    this.location,
    this.preferences = const {},
    this.favoriteProviders = const [],
    this.totalBookings = 0,
    this.totalSpent = 0.0,
    this.reviewsCount = 0,
    this.averageRating = 0.0,
    this.isActive = true,
    this.fcmToken,
    this.notificationSettings = const {
      'bookingReminders': true,
      'promotions': true,
      'newMessages': true,
      'reviewRequests': true,
      'systemUpdates': false,
    },
  });

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? phoneNumber,
    String? profileImageUrl,
    UserType? userType,
    Gender? gender,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    LocationModel? location,
    Map<String, dynamic>? preferences,
    List<String>? favoriteProviders,
    int? totalBookings,
    double? totalSpent,
    int? reviewsCount,
    double? averageRating,
    bool? isActive,
    String? fcmToken,
    Map<String, bool>? notificationSettings,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      userType: userType ?? this.userType,
      gender: gender ?? this.gender,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      location: location ?? this.location,
      preferences: preferences ?? this.preferences,
      favoriteProviders: favoriteProviders ?? this.favoriteProviders,
      totalBookings: totalBookings ?? this.totalBookings,
      totalSpent: totalSpent ?? this.totalSpent,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      averageRating: averageRating ?? this.averageRating,
      isActive: isActive ?? this.isActive,
      fcmToken: fcmToken ?? this.fcmToken,
      notificationSettings: notificationSettings ?? this.notificationSettings,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'phoneNumber': phoneNumber,
      'profileImageUrl': profileImageUrl,
      'userType': userType.name,
      'gender': gender.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastLoginAt': lastLoginAt != null ? Timestamp.fromDate(lastLoginAt!) : null,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'location': location?.toMap(),
      'preferences': preferences,
      'favoriteProviders': favoriteProviders,
      'totalBookings': totalBookings,
      'totalSpent': totalSpent,
      'reviewsCount': reviewsCount,
      'averageRating': averageRating,
      'isActive': isActive,
      'fcmToken': fcmToken,
      'notificationSettings': notificationSettings,
    };
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      displayName: map['displayName'],
      phoneNumber: map['phoneNumber'],
      profileImageUrl: map['profileImageUrl'],
      userType: UserType.values.firstWhere(
        (type) => type.name == map['userType'],
        orElse: () => UserType.customer,
      ),
      gender: Gender.values.firstWhere(
        (gender) => gender.name == map['gender'],
        orElse: () => Gender.notSpecified,
      ),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastLoginAt: (map['lastLoginAt'] as Timestamp?)?.toDate(),
      isEmailVerified: map['isEmailVerified'] ?? false,
      isPhoneVerified: map['isPhoneVerified'] ?? false,
      location: map['location'] != null ? LocationModel.fromMap(map['location']) : null,
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
      favoriteProviders: List<String>.from(map['favoriteProviders'] ?? []),
      totalBookings: map['totalBookings'] ?? 0,
      totalSpent: (map['totalSpent'] ?? 0.0).toDouble(),
      reviewsCount: map['reviewsCount'] ?? 0,
      averageRating: (map['averageRating'] ?? 0.0).toDouble(),
      isActive: map['isActive'] ?? true,
      fcmToken: map['fcmToken'],
      notificationSettings: Map<String, bool>.from(map['notificationSettings'] ?? {
        'bookingReminders': true,
        'promotions': true,
        'newMessages': true,
        'reviewRequests': true,
        'systemUpdates': false,
      }),
    );
  }

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel.fromMap({...data, 'id': doc.id});
  }

  String get initials {
    if (displayName != null && displayName!.isNotEmpty) {
      final names = displayName!.split(' ');
      if (names.length >= 2) {
        return '${names[0][0]}${names[1][0]}'.toUpperCase();
      } else {
        return names[0][0].toUpperCase();
      }
    }
    return email[0].toUpperCase();
  }

  String get fullName => displayName ?? email.split('@')[0];

  bool get hasCompletedProfile {
    return displayName != null && 
           phoneNumber != null && 
           location != null;
  }

  bool get canReceiveNotifications => fcmToken != null && isActive;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'phoneNumber': phoneNumber,
      'profileImageUrl': profileImageUrl,
      'userType': userType.name,
      'gender': gender.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastLoginAt': lastLoginAt != null ? Timestamp.fromDate(lastLoginAt!) : null,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'location': location?.toMap(),
      'preferences': preferences,
      'favoriteProviders': favoriteProviders,
      'totalBookings': totalBookings,
      'totalSpent': totalSpent,
      'reviewsCount': reviewsCount,
      'averageRating': averageRating,
      'isActive': isActive,
      'fcmToken': fcmToken,
      'notificationSettings': notificationSettings,
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel.fromMap(json);
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        phoneNumber,
        profileImageUrl,
        userType,
        createdAt,
        lastLoginAt,
        isEmailVerified,
        isPhoneVerified,
        location,
        preferences,
        favoriteProviders,
        totalBookings,
        totalSpent,
        reviewsCount,
        averageRating,
        isActive,
        fcmToken,
        notificationSettings,
      ];
}
