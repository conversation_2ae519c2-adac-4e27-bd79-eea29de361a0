import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/enums/service_type.dart';

class SearchSuggestionsWidget extends StatelessWidget {
  const SearchSuggestionsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Popular Searches',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Popular Categories
        _buildPopularCategories(context),
        
        const SizedBox(height: 24),
        
        Text(
          'Trending Services',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Trending Services
        _buildTrendingServices(context),
        
        const SizedBox(height: 24),
        
        Text(
          'Quick Searches',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Quick Search Tags
        _buildQuickSearchTags(context),
      ],
    );
  }

  Widget _buildPopularCategories(BuildContext context) {
    final categories = [
      _CategorySuggestion(
        name: 'Hair Salon',
        icon: Icons.content_cut,
        color: AppColors.primaryPurple,
        serviceType: ServiceType.salon,
        description: 'Hair cuts, styling, coloring',
      ),
      _CategorySuggestion(
        name: 'Spa & Wellness',
        icon: Icons.spa,
        color: AppColors.success,
        serviceType: ServiceType.spa,
        description: 'Relaxation and wellness',
      ),
      _CategorySuggestion(
        name: 'Nail Care',
        icon: Icons.back_hand,
        color: AppColors.warning,
        serviceType: ServiceType.nails,
        description: 'Manicure, pedicure, nail art',
      ),
      _CategorySuggestion(
        name: 'Makeup',
        icon: Icons.face,
        color: Colors.pink,
        serviceType: ServiceType.makeup,
        description: 'Professional makeup services',
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return _CategoryCard(
          category: category,
          onTap: () => _searchCategory(context, category),
        );
      },
    );
  }

  Widget _buildTrendingServices(BuildContext context) {
    final trendingServices = [
      'Hair Cut & Styling',
      'Deep Cleansing Facial',
      'Gel Manicure',
      'Eyebrow Threading',
      'Hot Stone Massage',
      'Bridal Makeup',
    ];

    return Column(
      children: trendingServices.map((service) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _TrendingServiceItem(
            serviceName: service,
            onTap: () => _searchService(context, service),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildQuickSearchTags(BuildContext context) {
    final quickTags = [
      'Near me',
      'Open now',
      'Highly rated',
      'Verified',
      'Under 30 KWD',
      'Same day booking',
      'Weekend available',
      'Home service',
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: quickTags.map((tag) {
        return _QuickSearchTag(
          tag: tag,
          onTap: () => _quickSearch(context, tag),
        );
      }).toList(),
    );
  }

  void _searchCategory(BuildContext context, _CategorySuggestion category) {
    Navigator.pushNamed(
      context,
      '/providers',
      arguments: {
        'serviceType': category.serviceType,
      },
    );
  }

  void _searchService(BuildContext context, String service) {
    Navigator.pushNamed(
      context,
      '/search',
      arguments: {
        'query': service,
      },
    );
  }

  void _quickSearch(BuildContext context, String tag) {
    // Implement quick search logic based on tag
    Map<String, dynamic> searchParams = {};
    
    switch (tag) {
      case 'Near me':
        // Use current location
        break;
      case 'Open now':
        searchParams['isOnline'] = true;
        break;
      case 'Highly rated':
        searchParams['minRating'] = 4.5;
        break;
      case 'Verified':
        searchParams['isVerified'] = true;
        break;
      case 'Under 30 KWD':
        searchParams['maxPrice'] = 30.0;
        break;
      default:
        searchParams['query'] = tag;
    }

    Navigator.pushNamed(
      context,
      '/search',
      arguments: searchParams,
    );
  }
}

class _CategorySuggestion {
  final String name;
  final IconData icon;
  final Color color;
  final ServiceType serviceType;
  final String description;

  const _CategorySuggestion({
    required this.name,
    required this.icon,
    required this.color,
    required this.serviceType,
    required this.description,
  });
}

class _CategoryCard extends StatelessWidget {
  final _CategorySuggestion category;
  final VoidCallback onTap;

  const _CategoryCard({
    required this.category,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: category.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                category.icon,
                color: category.color,
                size: 28,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              category.name,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 4),
            
            Text(
              category.description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

class _TrendingServiceItem extends StatelessWidget {
  final String serviceName;
  final VoidCallback onTap;

  const _TrendingServiceItem({
    required this.serviceName,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primaryPurple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.trending_up,
                color: AppColors.primaryPurple,
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: Text(
                serviceName,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textLight,
            ),
          ],
        ),
      ),
    );
  }
}

class _QuickSearchTag extends StatelessWidget {
  final String tag;
  final VoidCallback onTap;

  const _QuickSearchTag({
    required this.tag,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.backgroundGray,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppColors.borderGray,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search,
              size: 16,
              color: AppColors.textLight,
            ),
            const SizedBox(width: 6),
            Text(
              tag,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textDark,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
