import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import '../services/memory_manager.dart';
import '../services/network_optimizer.dart';

/// نظام تحليلات الأداء المتقدم
class PerformanceAnalytics {
  static PerformanceAnalytics? _instance;
  static PerformanceAnalytics get instance => _instance ??= PerformanceAnalytics._();
  
  PerformanceAnalytics._();

  final Queue<PerformanceMetric> _metrics = Queue();
  final Map<String, Stopwatch> _activeTimers = {};
  final Map<String, List<Duration>> _operationTimes = {};
  
  Timer? _reportingTimer;
  bool _isInitialized = false;
  
  // إعدادات التحليلات
  static const int _maxMetricsCount = 1000;
  static const Duration _reportingInterval = Duration(minutes: 5);
  
  int _frameDropCount = 0;
  int _totalFrames = 0;
  double _averageFPS = 60.0;

  /// تهيئة نظام تحليلات الأداء
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // بدء مراقبة الإطارات
      _startFrameMonitoring();
      
      // بدء التقارير الدورية
      _startPeriodicReporting();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('PerformanceAnalytics: تم تهيئة نظام تحليلات الأداء');
      }
    } catch (e) {
      if (kDebugMode) {
        print('PerformanceAnalytics: خطأ في التهيئة: $e');
      }
    }
  }

  /// بدء مراقبة الإطارات
  void _startFrameMonitoring() {
    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      _totalFrames++;
      
      // حساب FPS
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      if (_totalFrames % 60 == 0) {
        _calculateFPS();
      }
    });
  }

  /// حساب معدل الإطارات في الثانية
  void _calculateFPS() {
    // تنفيذ بسيط لحساب FPS
    // في التطبيق الحقيقي، يمكن استخدام مكتبات متخصصة
    _averageFPS = 60.0; // قيمة افتراضية
  }

  /// بدء التقارير الدورية
  void _startPeriodicReporting() {
    _reportingTimer = Timer.periodic(_reportingInterval, (_) {
      _generatePerformanceReport();
    });
  }

  /// بدء قياس عملية
  void startOperation(String operationName) {
    final stopwatch = Stopwatch()..start();
    _activeTimers[operationName] = stopwatch;
    
    if (kDebugMode) {
      print('PerformanceAnalytics: بدء قياس: $operationName');
    }
  }

  /// إنهاء قياس عملية
  void endOperation(String operationName, {Map<String, dynamic>? metadata}) {
    final stopwatch = _activeTimers.remove(operationName);
    if (stopwatch == null) return;
    
    stopwatch.stop();
    final duration = stopwatch.elapsed;
    
    // حفظ وقت العملية
    _operationTimes.putIfAbsent(operationName, () => []).add(duration);
    
    // إنشاء metric
    final metric = PerformanceMetric(
      name: operationName,
      type: MetricType.operation,
      value: duration.inMilliseconds.toDouble(),
      timestamp: DateTime.now(),
      metadata: metadata,
    );
    
    _addMetric(metric);
    
    if (kDebugMode) {
      print('PerformanceAnalytics: انتهاء قياس: $operationName (${duration.inMilliseconds}ms)');
    }
  }

  /// قياس عملية مع callback
  Future<T> measureOperation<T>(
    String operationName,
    Future<T> Function() operation, {
    Map<String, dynamic>? metadata,
  }) async {
    startOperation(operationName);
    try {
      final result = await operation();
      endOperation(operationName, metadata: metadata);
      return result;
    } catch (e) {
      endOperation(operationName, metadata: {
        ...?metadata,
        'error': e.toString(),
      });
      rethrow;
    }
  }

  /// تسجيل metric مخصص
  void recordMetric(
    String name,
    double value,
    MetricType type, {
    Map<String, dynamic>? metadata,
  }) {
    final metric = PerformanceMetric(
      name: name,
      type: type,
      value: value,
      timestamp: DateTime.now(),
      metadata: metadata,
    );
    
    _addMetric(metric);
  }

  /// تسجيل خطأ في الأداء
  void recordError(String operation, String error, {StackTrace? stackTrace}) {
    final metric = PerformanceMetric(
      name: operation,
      type: MetricType.error,
      value: 1.0,
      timestamp: DateTime.now(),
      metadata: {
        'error': error,
        'stackTrace': stackTrace?.toString(),
      },
    );
    
    _addMetric(metric);
    
    if (kDebugMode) {
      print('PerformanceAnalytics: خطأ في الأداء: $operation - $error');
    }
  }

  /// إضافة metric إلى القائمة
  void _addMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    
    // الحفاظ على حد أقصى للعدد
    while (_metrics.length > _maxMetricsCount) {
      _metrics.removeFirst();
    }
  }

  /// توليد تقرير الأداء
  void _generatePerformanceReport() async {
    if (_metrics.isEmpty) return;

    final report = PerformanceReport(
      timestamp: DateTime.now(),
      totalMetrics: _metrics.length,
      averageFPS: _averageFPS,
      frameDropCount: _frameDropCount,
      operationStats: _generateOperationStats(),
      memoryStats: await _getMemoryStats(),
      networkStats: _getNetworkStats(),
    );
    
    if (kDebugMode) {
      print('PerformanceAnalytics: تقرير الأداء:');
      print('  - إجمالي المقاييس: ${report.totalMetrics}');
      print('  - متوسط FPS: ${report.averageFPS.toStringAsFixed(1)}');
      print('  - إسقاط الإطارات: ${report.frameDropCount}');
      print('  - استخدام الذاكرة: ${report.memoryStats?.currentUsageMB}MB');
    }
  }

  /// توليد إحصائيات العمليات
  Map<String, OperationStats> _generateOperationStats() {
    final stats = <String, OperationStats>{};
    
    for (final entry in _operationTimes.entries) {
      final times = entry.value;
      if (times.isEmpty) continue;
      
      final totalMs = times.fold<int>(0, (sum, duration) => sum + duration.inMilliseconds);
      final avgMs = totalMs / times.length;
      final minMs = times.map((d) => d.inMilliseconds).reduce((a, b) => a < b ? a : b);
      final maxMs = times.map((d) => d.inMilliseconds).reduce((a, b) => a > b ? a : b);
      
      stats[entry.key] = OperationStats(
        operationName: entry.key,
        totalCalls: times.length,
        averageTimeMs: avgMs,
        minTimeMs: minMs.toDouble(),
        maxTimeMs: maxMs.toDouble(),
      );
    }
    
    return stats;
  }

  /// الحصول على إحصائيات الذاكرة
  Future<MemoryStats?> _getMemoryStats() async {
    try {
      return await MemoryManager.instance.getMemoryStats();
    } catch (e) {
      return null;
    }
  }

  /// الحصول على إحصائيات الشبكة
  NetworkStats _getNetworkStats() {
    final optimizer = NetworkOptimizer.instance;
    return NetworkStats(
      connectionType: optimizer.connectionType.toString(),
      networkQuality: optimizer.networkQuality.toString(),
      isConnected: optimizer.isConnected,
    );
  }

  /// الحصول على تقرير الأداء الحالي
  PerformanceReport getCurrentReport() {
    return PerformanceReport(
      timestamp: DateTime.now(),
      totalMetrics: _metrics.length,
      averageFPS: _averageFPS,
      frameDropCount: _frameDropCount,
      operationStats: _generateOperationStats(),
      memoryStats: null, // سيتم تحديثه بشكل غير متزامن
      networkStats: _getNetworkStats(),
    );
  }

  /// الحصول على metrics بناءً على النوع
  List<PerformanceMetric> getMetricsByType(MetricType type) {
    return _metrics.where((metric) => metric.type == type).toList();
  }

  /// الحصول على metrics بناءً على الاسم
  List<PerformanceMetric> getMetricsByName(String name) {
    return _metrics.where((metric) => metric.name == name).toList();
  }

  /// تنظيف البيانات القديمة
  void clearOldMetrics({Duration? olderThan}) {
    final cutoffTime = DateTime.now().subtract(olderThan ?? const Duration(hours: 1));
    _metrics.removeWhere((metric) => metric.timestamp.isBefore(cutoffTime));
    
    if (kDebugMode) {
      print('PerformanceAnalytics: تنظيف المقاييس القديمة');
    }
  }

  /// تصدير البيانات للتحليل
  Map<String, dynamic> exportData() {
    return {
      'metrics': _metrics.map((m) => m.toJson()).toList(),
      'operationStats': _generateOperationStats()
          .map((key, value) => MapEntry(key, value.toJson())),
      'summary': {
        'totalMetrics': _metrics.length,
        'averageFPS': _averageFPS,
        'frameDropCount': _frameDropCount,
      },
    };
  }

  /// إيقاف نظام التحليلات
  void dispose() {
    _reportingTimer?.cancel();
    _metrics.clear();
    _activeTimers.clear();
    _operationTimes.clear();
    _isInitialized = false;
    
    if (kDebugMode) {
      print('PerformanceAnalytics: تم إيقاف نظام تحليلات الأداء');
    }
  }
}

/// نوع المقياس
enum MetricType {
  operation,
  memory,
  network,
  ui,
  error,
  custom,
}

/// مقياس الأداء
class PerformanceMetric {
  final String name;
  final MetricType type;
  final double value;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const PerformanceMetric({
    required this.name,
    required this.type,
    required this.value,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type.toString(),
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// إحصائيات العملية
class OperationStats {
  final String operationName;
  final int totalCalls;
  final double averageTimeMs;
  final double minTimeMs;
  final double maxTimeMs;

  const OperationStats({
    required this.operationName,
    required this.totalCalls,
    required this.averageTimeMs,
    required this.minTimeMs,
    required this.maxTimeMs,
  });

  Map<String, dynamic> toJson() {
    return {
      'operationName': operationName,
      'totalCalls': totalCalls,
      'averageTimeMs': averageTimeMs,
      'minTimeMs': minTimeMs,
      'maxTimeMs': maxTimeMs,
    };
  }
}

/// إحصائيات الشبكة
class NetworkStats {
  final String connectionType;
  final String networkQuality;
  final bool isConnected;

  const NetworkStats({
    required this.connectionType,
    required this.networkQuality,
    required this.isConnected,
  });
}

/// تقرير الأداء
class PerformanceReport {
  final DateTime timestamp;
  final int totalMetrics;
  final double averageFPS;
  final int frameDropCount;
  final Map<String, OperationStats> operationStats;
  final MemoryStats? memoryStats;
  final NetworkStats networkStats;

  const PerformanceReport({
    required this.timestamp,
    required this.totalMetrics,
    required this.averageFPS,
    required this.frameDropCount,
    required this.operationStats,
    required this.memoryStats,
    required this.networkStats,
  });
}
