import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/services/theme_service.dart';

class GenderSelectionWidget extends StatefulWidget {
  final Gender? selectedGender;
  final Function(Gender) onGenderSelected;
  final bool isRequired;
  final String? title;

  const GenderSelectionWidget({
    super.key,
    this.selectedGender,
    required this.onGenderSelected,
    this.isRequired = false,
    this.title,
  });

  @override
  State<GenderSelectionWidget> createState() => _GenderSelectionWidgetState();
}

class _GenderSelectionWidgetState extends State<GenderSelectionWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  final ThemeService _themeService = ThemeService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null) ...[
          Row(
            children: [
              Text(
                widget.title!,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.isRequired) ...[
                const SizedBox(width: 4),
                const Text(
                  '*',
                  style: TextStyle(
                    color: AppColors.error,
                    fontSize: 16,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
        ],
        
        Row(
          children: [
            Expanded(
              child: _buildGenderCard(
                gender: Gender.male,
                icon: Icons.male,
                title: 'ذكر',
                subtitle: 'Male',
                color: const Color(0xFF6C63FF),
                gradientColors: [
                  const Color(0xFF6C63FF),
                  const Color(0xFF9C88FF),
                ],
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: _buildGenderCard(
                gender: Gender.female,
                icon: Icons.female,
                title: 'أنثى',
                subtitle: 'Female',
                color: const Color(0xFFE91E63),
                gradientColors: [
                  const Color(0xFFE91E63),
                  const Color(0xFFF8BBD9),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Other option
        _buildOtherGenderOption(),
      ],
    );
  }

  Widget _buildGenderCard({
    required Gender gender,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required List<Color> gradientColors,
  }) {
    final isSelected = widget.selectedGender == gender;
    
    return GestureDetector(
      onTap: () {
        _animationController.forward().then((_) {
          _animationController.reverse();
        });
        
        widget.onGenderSelected(gender);
        
        // Auto-apply theme based on gender selection
        _themeService.setGender(gender);
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: isSelected ? _scaleAnimation.value : 1.0,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: isSelected
                    ? LinearGradient(
                        colors: gradientColors,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : null,
                color: isSelected ? null : AppColors.backgroundWhite,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected ? Colors.transparent : AppColors.borderGray,
                  width: 2,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: color.withValues(alpha: 0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 6),
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
              ),
              child: Column(
                children: [
                  // Icon
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.white.withValues(alpha: 0.2)
                          : color.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      size: 32,
                      color: isSelected ? Colors.white : color,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Title
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : AppColors.textDark,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Subtitle
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Colors.white.withValues(alpha: 0.8)
                          : AppColors.textLight,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Selection indicator
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: isSelected ? 24 : 0,
                    height: isSelected ? 24 : 0,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: isSelected
                        ? Icon(
                            Icons.check,
                            size: 16,
                            color: color,
                          )
                        : null,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildOtherGenderOption() {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => widget.onGenderSelected(Gender.other),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: widget.selectedGender == Gender.other
                    ? AppColors.textLight.withValues(alpha: 0.1)
                    : AppColors.backgroundWhite,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: widget.selectedGender == Gender.other
                      ? AppColors.textLight
                      : AppColors.borderGray,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.more_horiz,
                    color: widget.selectedGender == Gender.other
                        ? AppColors.textLight
                        : AppColors.textLight.withValues(alpha: 0.6),
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'آخر',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: widget.selectedGender == Gender.other
                          ? AppColors.textLight
                          : AppColors.textLight.withValues(alpha: 0.8),
                      fontWeight: widget.selectedGender == Gender.other
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                  const Spacer(),
                  if (widget.selectedGender == Gender.other)
                    Icon(
                      Icons.check_circle,
                      color: AppColors.textLight,
                      size: 20,
                    ),
                ],
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: GestureDetector(
            onTap: () => widget.onGenderSelected(Gender.notSpecified),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: widget.selectedGender == Gender.notSpecified
                    ? AppColors.textLight.withValues(alpha: 0.1)
                    : AppColors.backgroundWhite,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: widget.selectedGender == Gender.notSpecified
                      ? AppColors.textLight
                      : AppColors.borderGray,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.help_outline,
                    color: widget.selectedGender == Gender.notSpecified
                        ? AppColors.textLight
                        : AppColors.textLight.withValues(alpha: 0.6),
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'أفضل عدم التحديد',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: widget.selectedGender == Gender.notSpecified
                          ? AppColors.textLight
                          : AppColors.textLight.withValues(alpha: 0.8),
                      fontWeight: widget.selectedGender == Gender.notSpecified
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                  const Spacer(),
                  if (widget.selectedGender == Gender.notSpecified)
                    Icon(
                      Icons.check_circle,
                      color: AppColors.textLight,
                      size: 16,
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
