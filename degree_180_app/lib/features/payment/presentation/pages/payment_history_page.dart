import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/models/payment_model.dart';
import '../widgets/payment_history_card.dart';
import '../widgets/payment_filter_sheet.dart';

class PaymentHistoryPage extends StatefulWidget {
  const PaymentHistoryPage({super.key});

  @override
  State<PaymentHistoryPage> createState() => _PaymentHistoryPageState();
}

class _PaymentHistoryPageState extends State<PaymentHistoryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<PaymentModel> _payments = [];
  List<PaymentModel> _filteredPayments = [];
  String _selectedFilter = 'All';
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPayments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadPayments() {
    // Mock payment data
    _payments = [
      PaymentModel(
        id: 'pay_1',
        bookingId: 'book_1',
        userId: 'user_1',
        providerId: 'provider_1',
        amount: 25.0,
        currency: 'KWD',
        paymentMethod: PaymentMethod.creditCard,
        status: PaymentStatus.completed,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        completedAt: DateTime.now().subtract(const Duration(days: 1)),
        transactionId: 'TXN_123456789',
        serviceFee: 0.625,
        tax: 1.25,
      ),
      PaymentModel(
        id: 'pay_2',
        bookingId: 'book_2',
        userId: 'user_1',
        providerId: 'provider_2',
        amount: 15.0,
        currency: 'KWD',
        paymentMethod: PaymentMethod.knet,
        status: PaymentStatus.completed,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        completedAt: DateTime.now().subtract(const Duration(days: 3)),
        transactionId: 'TXN_987654321',
        serviceFee: 0.375,
        tax: 0.75,
      ),
      PaymentModel(
        id: 'pay_3',
        bookingId: 'book_3',
        userId: 'user_1',
        providerId: 'provider_3',
        amount: 40.0,
        currency: 'KWD',
        paymentMethod: PaymentMethod.wallet,
        status: PaymentStatus.refunded,
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        completedAt: DateTime.now().subtract(const Duration(days: 7)),
        refundedAt: DateTime.now().subtract(const Duration(days: 5)),
        refundAmount: 40.0,
        transactionId: 'TXN_456789123',
      ),
      PaymentModel(
        id: 'pay_4',
        bookingId: 'book_4',
        userId: 'user_1',
        providerId: 'provider_4',
        amount: 30.0,
        currency: 'KWD',
        paymentMethod: PaymentMethod.cash,
        status: PaymentStatus.pending,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        transactionId: 'TXN_789123456',
      ),
    ];

    _filteredPayments = List.from(_payments);
  }

  List<PaymentModel> get _filteredByStatus {
    switch (_selectedFilter) {
      case 'Completed':
        return _filteredPayments.where((p) => p.status == PaymentStatus.completed).toList();
      case 'Pending':
        return _filteredPayments.where((p) => p.status == PaymentStatus.pending).toList();
      case 'Refunded':
        return _filteredPayments.where((p) => p.isRefunded).toList();
      default:
        return _filteredPayments;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment History'),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            onPressed: _showFilterSheet,
            icon: const Icon(Icons.filter_list),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'help',
                child: Row(
                  children: [
                    Icon(Icons.help_outline),
                    SizedBox(width: 8),
                    Text('Help'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Cards
          _buildSummaryCards(),
          
          // Filter Tabs
          Container(
            color: AppColors.backgroundWhite,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.primaryPurple,
              unselectedLabelColor: AppColors.textLight,
              indicatorColor: AppColors.primaryPurple,
              onTap: (index) {
                setState(() {
                  switch (index) {
                    case 0:
                      _selectedFilter = 'All';
                      break;
                    case 1:
                      _selectedFilter = 'Completed';
                      break;
                    case 2:
                      _selectedFilter = 'Pending';
                      break;
                    case 3:
                      _selectedFilter = 'Refunded';
                      break;
                  }
                });
              },
              tabs: [
                Tab(
                  child: _buildTabWithCount('All', _filteredPayments.length),
                ),
                Tab(
                  child: _buildTabWithCount('Completed', _filteredPayments.where((p) => p.status == PaymentStatus.completed).length),
                ),
                Tab(
                  child: _buildTabWithCount('Pending', _filteredPayments.where((p) => p.status == PaymentStatus.pending).length),
                ),
                Tab(
                  child: _buildTabWithCount('Refunded', _filteredPayments.where((p) => p.isRefunded).length),
                ),
              ],
            ),
          ),

          // Payments List
          Expanded(
            child: _buildPaymentsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalSpent = _payments
        .where((p) => p.status == PaymentStatus.completed)
        .fold(0.0, (sum, p) => sum + p.totalAmount);
    
    final thisMonthSpent = _payments
        .where((p) => p.status == PaymentStatus.completed && 
                     p.completedAt != null &&
                     p.completedAt!.month == DateTime.now().month)
        .fold(0.0, (sum, p) => sum + p.totalAmount);

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'Total Spent',
              '${totalSpent.toStringAsFixed(3)} KWD',
              Icons.account_balance_wallet,
              AppColors.primaryPurple,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'This Month',
              '${thisMonthSpent.toStringAsFixed(3)} KWD',
              Icons.calendar_today,
              AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textLight,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabWithCount(String title, int count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(title),
        if (count > 0) ...[
          const SizedBox(width: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.primaryPurple,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                color: AppColors.textWhite,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPaymentsList() {
    final payments = _filteredByStatus;

    if (payments.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadPayments();
        setState(() {});
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: payments.length,
        itemBuilder: (context, index) {
          final payment = payments[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: PaymentHistoryCard(
              payment: payment,
              onTap: () => _viewPaymentDetails(payment),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    String message;
    IconData icon;

    switch (_selectedFilter) {
      case 'Completed':
        message = 'No completed payments';
        icon = Icons.check_circle_outline;
        break;
      case 'Pending':
        message = 'No pending payments';
        icon = Icons.pending_outlined;
        break;
      case 'Refunded':
        message = 'No refunded payments';
        icon = Icons.undo;
        break;
      default:
        message = 'No payments yet';
        icon = Icons.payment;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: AppColors.textLight.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textLight,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your payment history will appear here.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => PaymentFilterSheet(
        selectedDateRange: _selectedDateRange,
        onFiltersApplied: _applyFilters,
      ),
    );
  }

  void _applyFilters({
    DateTimeRange? dateRange,
    List<PaymentMethod>? methods,
    List<PaymentStatus>? statuses,
  }) {
    setState(() {
      _selectedDateRange = dateRange;
      _filteredPayments = _payments.where((payment) {
        // Date filter
        if (dateRange != null) {
          if (payment.createdAt.isBefore(dateRange.start) ||
              payment.createdAt.isAfter(dateRange.end)) {
            return false;
          }
        }

        // Method filter
        if (methods != null && methods.isNotEmpty) {
          if (!methods.contains(payment.paymentMethod)) {
            return false;
          }
        }

        // Status filter
        if (statuses != null && statuses.isNotEmpty) {
          if (!statuses.contains(payment.status)) {
            return false;
          }
        }

        return true;
      }).toList();
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportPayments();
        break;
      case 'help':
        _showHelp();
        break;
    }
  }

  void _exportPayments() {
    // Implement payment export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Payment history exported'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment History Help'),
        content: const Text(
          'Here you can view all your payment transactions, filter by status, '
          'date range, and payment method. Tap on any payment to view detailed information.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _viewPaymentDetails(PaymentModel payment) {
    Navigator.pushNamed(
      context,
      '/payment-details',
      arguments: {'paymentId': payment.id},
    );
  }
}
