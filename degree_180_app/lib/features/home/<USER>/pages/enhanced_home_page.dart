import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/models/location_model.dart';
import '../../../../core/enums/service_type.dart';
import '../../../services/presentation/bloc/service_provider_bloc.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../widgets/service_category_grid.dart';
import '../widgets/featured_providers_section.dart';
import '../widgets/nearby_providers_section.dart';
import '../widgets/home_app_bar.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/quick_actions_section.dart';

class EnhancedHomePage extends StatefulWidget {
  const EnhancedHomePage({super.key});

  @override
  State<EnhancedHomePage> createState() => _EnhancedHomePageState();
}

class _EnhancedHomePageState extends State<EnhancedHomePage> {
  final ScrollController _scrollController = ScrollController();
  LocationModel? _userLocation;
  bool _isLoadingLocation = false;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _isLoadingLocation = false;
        });
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _isLoadingLocation = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _isLoadingLocation = false;
        });
        return;
      }

      final position = await Geolocator.getCurrentPosition();
      setState(() {
        _userLocation = LocationModel(
          latitude: position.latitude,
          longitude: position.longitude,
          address: 'Current Location',
        );
        _isLoadingLocation = false;
      });

      // Load nearby providers after getting location
      if (mounted) {
        context.read<ServiceProviderBloc>().add(
          LoadNearbyProvidersEvent(
            userLocation: _userLocation!,
            radiusKm: 10.0,
            limit: 10,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  void _loadInitialData() {
    // Load featured providers
    context.read<ServiceProviderBloc>().add(
      const LoadFeaturedProvidersEvent(limit: 10),
    );
  }

  Future<void> _onRefresh() async {
    _loadInitialData();
    if (_userLocation != null) {
      context.read<ServiceProviderBloc>().add(
        LoadNearbyProvidersEvent(
          userLocation: _userLocation!,
          radiusKm: 10.0,
          limit: 10,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // App Bar
            SliverToBoxAdapter(
              child: HomeAppBar(
                userLocation: _userLocation,
                isLoadingLocation: _isLoadingLocation,
                onLocationTap: _getCurrentLocation,
              ),
            ),

            // Search Bar
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(AppConstants.defaultPadding),
                child: SearchBarWidget(),
              ),
            ),

            // Quick Actions
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                child: QuickActionsSection(),
              ),
            ),

            // Service Categories
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(AppConstants.defaultPadding),
                child: ServiceCategoryGrid(),
              ),
            ),

            // Featured Providers
            SliverToBoxAdapter(
              child: BlocBuilder<ServiceProviderBloc, ServiceProviderState>(
                builder: (context, state) {
                  return FeaturedProvidersSection(
                    state: state,
                    onSeeAll: () => _navigateToProvidersList(featured: true),
                  );
                },
              ),
            ),

            // Nearby Providers
            if (_userLocation != null)
              SliverToBoxAdapter(
                child: BlocBuilder<ServiceProviderBloc, ServiceProviderState>(
                  builder: (context, state) {
                    return NearbyProvidersSection(
                      state: state,
                      userLocation: _userLocation!,
                      onSeeAll: () => _navigateToProvidersList(nearby: true),
                    );
                  },
                ),
              ),

            // Recent Bookings Section
            SliverToBoxAdapter(
              child: BlocBuilder<AuthBloc, AuthState>(
                builder: (context, authState) {
                  if (authState is AuthAuthenticated) {
                    return _buildRecentBookingsSection(authState.user.uid);
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),

            // Bottom Spacing
            const SliverToBoxAdapter(
              child: SizedBox(height: 100),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentBookingsSection(String userId) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Bookings',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () => _navigateToBookings(),
                child: const Text('See All'),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Recent bookings list would go here
          // For now, show a placeholder
          EnhancedCard(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 48,
                    color: AppColors.primaryPurple.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No recent bookings',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textLight,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () => _navigateToBooking(),
                    child: const Text('Book a Service'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToProvidersList({bool featured = false, bool nearby = false}) {
    // Navigate to providers list page
    Navigator.pushNamed(
      context,
      '/providers',
      arguments: {
        'featured': featured,
        'nearby': nearby,
        'userLocation': _userLocation,
      },
    );
  }

  void _navigateToBookings() {
    Navigator.pushNamed(context, '/bookings');
  }

  void _navigateToBooking() {
    Navigator.pushNamed(context, '/booking');
  }
}
