import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/theme_manager.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';
import '../../../../core/services/payment_service.dart';
import '../widgets/payment_method_card.dart';
import '../widgets/payment_summary_card.dart';
import '../widgets/tip_selection_widget.dart';

class PaymentPage extends StatefulWidget {
  final String bookingId;
  final double amount;
  final String providerId;

  const PaymentPage({
    super.key,
    required this.bookingId,
    required this.amount,
    required this.providerId,
  });

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String? _selectedPaymentMethod;
  double _tipAmount = 0.0;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Payment Summary
            Container(
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: PaymentSummaryCard(
                  amount: widget.amount,
                  tipAmount: _tipAmount,
                  taxAmount: _calculateTax(),
                  totalAmount: _calculateTotal(),
                ),
              ),
            ),
            
            // Payment Content
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: AppColors.backgroundWhite,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.largeBorderRadius),
                    topRight: Radius.circular(AppConstants.largeBorderRadius),
                  ),
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Tip Selection
                      Text(
                        'Add Tip (Optional)',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      TipSelectionWidget(
                        baseAmount: widget.amount,
                        onTipChanged: (tip) {
                          setState(() {
                            _tipAmount = tip;
                          });
                        },
                      ),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Payment Methods
                      Text(
                        'Payment Method',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      PaymentMethodCard(
                        title: 'Credit/Debit Card',
                        subtitle: 'Visa, Mastercard, American Express',
                        icon: Icons.credit_card,
                        isSelected: _selectedPaymentMethod == 'card',
                        onTap: () => _selectPaymentMethod('card'),
                      ),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      PaymentMethodCard(
                        title: 'Apple Pay',
                        subtitle: 'Pay with Touch ID or Face ID',
                        icon: Icons.apple,
                        isSelected: _selectedPaymentMethod == 'apple_pay',
                        onTap: () => _selectPaymentMethod('apple_pay'),
                      ),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      PaymentMethodCard(
                        title: 'Google Pay',
                        subtitle: 'Pay with your Google account',
                        icon: Icons.g_mobiledata,
                        isSelected: _selectedPaymentMethod == 'google_pay',
                        onTap: () => _selectPaymentMethod('google_pay'),
                      ),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      PaymentMethodCard(
                        title: 'Cash',
                        subtitle: 'Pay in cash after service',
                        icon: Icons.money,
                        isSelected: _selectedPaymentMethod == 'cash',
                        onTap: () => _selectPaymentMethod('cash'),
                      ),
                      
                      const SizedBox(height: AppConstants.extraLargePadding),
                    ],
                  ),
                ),
              ),
            ),
            
            // Pay Button
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.backgroundWhite,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight,
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _selectedPaymentMethod != null && !_isProcessing
                      ? _processPayment
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryPurple,
                    foregroundColor: AppColors.textWhite,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppConstants.defaultPadding,
                    ),
                  ),
                  child: _isProcessing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.textWhite,
                            ),
                          ),
                        )
                      : Text(
                          'Pay ${_calculateTotal().toStringAsFixed(2)} KD',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeLarge,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _selectPaymentMethod(String method) {
    setState(() {
      _selectedPaymentMethod = method;
    });
  }

  double _calculateTax() {
    return widget.amount * 0.05; // 5% tax
  }

  double _calculateTotal() {
    return widget.amount + _tipAmount + _calculateTax();
  }

  Future<void> _processPayment() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 3));

      if (_selectedPaymentMethod == 'card') {
        await _processCardPayment();
      } else if (_selectedPaymentMethod == 'apple_pay') {
        await _processApplePayment();
      } else if (_selectedPaymentMethod == 'google_pay') {
        await _processGooglePayment();
      } else if (_selectedPaymentMethod == 'cash') {
        await _processCashPayment();
      }

      // Show success and navigate
      _showPaymentSuccess();
    } catch (e) {
      _showPaymentError(e.toString());
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _processCardPayment() async {
    try {
      setState(() {
        _isProcessing = true;
      });

      // Simulate payment processing
      await Future.delayed(const Duration(seconds: 2));

      // For demo purposes, assume payment is successful
      _showPaymentSuccess();
    } catch (e) {
      _showPaymentError('حدث خطأ أثناء عملية الدفع');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _processApplePayment() async {
    try {
      setState(() {
        _isProcessing = true;
      });

      // Simulate Apple Pay processing
      await Future.delayed(const Duration(seconds: 1));

      // For demo purposes, assume payment is successful
      _showPaymentSuccess();
    } catch (e) {
      _showPaymentError('حدث خطأ أثناء عملية الدفع');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _processGooglePayment() async {
    try {
      setState(() {
        _isProcessing = true;
      });

      // Simulate Google Pay processing
      await Future.delayed(const Duration(seconds: 1));

      // For demo purposes, assume payment is successful
      _showPaymentSuccess();
    } catch (e) {
      _showPaymentError('حدث خطأ أثناء عملية الدفع');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }



  Future<void> _processCashPayment() async {
    // Cash payment - just mark as pending
    await Future.delayed(const Duration(milliseconds: 500));
  }

  void _showPaymentSuccess() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: 28,
            ),
            const SizedBox(width: AppConstants.smallPadding),
            const Text('Payment Successful'),
          ],
        ),
        content: Text(
          _selectedPaymentMethod == 'cash'
              ? 'Your booking is confirmed. Please pay in cash after the service.'
              : 'Your payment has been processed successfully. Your booking is confirmed.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to previous screen
              Navigator.pushNamed(context, '/booking-confirmation');
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showPaymentError(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.error,
              color: AppColors.error,
              size: 28,
            ),
            const SizedBox(width: AppConstants.smallPadding),
            const Text('Payment Failed'),
          ],
        ),
        content: Text('Payment could not be processed. Please try again.\n\nError: $error'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
