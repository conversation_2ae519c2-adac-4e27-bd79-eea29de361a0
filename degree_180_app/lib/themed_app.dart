import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/constants/app_strings.dart';
import 'core/router/app_router.dart';
import 'core/services/theme_service.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/auth/domain/usecases/sign_in_with_email_usecase.dart';
import 'features/auth/domain/usecases/sign_up_with_email_usecase.dart';
import 'features/auth/domain/usecases/sign_in_with_google_usecase.dart';
import 'features/auth/domain/usecases/sign_out_usecase.dart';
import 'features/auth/domain/usecases/get_current_user_usecase.dart';
import 'features/services/presentation/bloc/service_provider_bloc.dart';
import 'features/services/domain/usecases/get_service_providers_usecase.dart';
import 'features/services/domain/usecases/get_service_provider_details_usecase.dart';
import 'features/booking/presentation/bloc/booking_bloc.dart';
import 'features/booking/domain/usecases/get_available_time_slots_usecase.dart';
import 'features/booking/domain/usecases/create_advanced_booking_usecase.dart';
import 'features/booking/domain/usecases/reschedule_booking_usecase.dart';
import 'features/booking/domain/usecases/get_booking_statistics_usecase.dart';
import 'package:get_it/get_it.dart';

final sl = GetIt.instance;

class ThemedDegree180App extends StatefulWidget {
  const ThemedDegree180App({super.key});

  @override
  State<ThemedDegree180App> createState() => _ThemedDegree180AppState();
}

class _ThemedDegree180AppState extends State<ThemedDegree180App> {
  final ThemeService _themeService = ThemeService();

  @override
  void initState() {
    super.initState();
    _initializeTheme();
  }

  Future<void> _initializeTheme() async {
    await _themeService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _themeService,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider<AuthBloc>(
              create: (context) => AuthBloc(
                signInWithEmailUseCase: sl<SignInWithEmailUseCase>(),
                signUpWithEmailUseCase: sl<SignUpWithEmailUseCase>(),
                signInWithGoogleUseCase: sl<SignInWithGoogleUseCase>(),
                signOutUseCase: sl<SignOutUseCase>(),
                getCurrentUserUseCase: sl<GetCurrentUserUseCase>(),
              ),
            ),
            BlocProvider<ServiceProviderBloc>(
              create: (context) => ServiceProviderBloc(
                getServiceProvidersUseCase: sl<GetServiceProvidersUseCase>(),
                getServiceProviderDetailsUseCase: sl<GetServiceProviderDetailsUseCase>(),
              ),
            ),
            BlocProvider<BookingBloc>(
              create: (context) => BookingBloc(
                getAvailableTimeSlotsUseCase: sl<GetAvailableTimeSlotsUseCase>(),
                createAdvancedBookingUseCase: sl<CreateAdvancedBookingUseCase>(),
                rescheduleBookingUseCase: sl<RescheduleBookingUseCase>(),
                getBookingStatisticsUseCase: sl<GetBookingStatisticsUseCase>(),
              ),
            ),
          ],
          child: MaterialApp.router(
            title: AppStrings.appName,
            debugShowCheckedModeBanner: false,
            
            // Use dynamic theme based on gender
            theme: _themeService.currentTheme,
            
            // Router configuration
            routerConfig: AppRouter.router,
            
            // Localization
            locale: const Locale('ar', 'SA'),
            supportedLocales: const [
              Locale('ar', 'SA'),
              Locale('en', 'US'),
            ],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            
            // Theme mode
            themeMode: _themeService.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            
            // Builder for additional theme customization
            builder: (context, child) {
              return _ThemeWrapper(
                themeService: _themeService,
                child: child ?? const SizedBox.shrink(),
              );
            },
          ),
        );
      },
    );
  }
}

// Wrapper widget to provide theme context
class _ThemeWrapper extends StatelessWidget {
  final ThemeService themeService;
  final Widget child;

  const _ThemeWrapper({
    required this.themeService,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: themeService.currentTheme.copyWith(
        // Additional theme customizations can be added here
        extensions: [
          CustomThemeExtension(
            primaryGradient: LinearGradient(
              colors: themeService.gradientColors,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            genderTheme: themeService.currentGender,
          ),
        ],
      ),
      child: child,
    );
  }
}

// Custom theme extension for additional theme data
@immutable
class CustomThemeExtension extends ThemeExtension<CustomThemeExtension> {
  final LinearGradient primaryGradient;
  final dynamic genderTheme;

  const CustomThemeExtension({
    required this.primaryGradient,
    required this.genderTheme,
  });

  @override
  CustomThemeExtension copyWith({
    LinearGradient? primaryGradient,
    dynamic genderTheme,
  }) {
    return CustomThemeExtension(
      primaryGradient: primaryGradient ?? this.primaryGradient,
      genderTheme: genderTheme ?? this.genderTheme,
    );
  }

  @override
  CustomThemeExtension lerp(
    ThemeExtension<CustomThemeExtension>? other,
    double t,
  ) {
    if (other is! CustomThemeExtension) {
      return this;
    }
    return CustomThemeExtension(
      primaryGradient: LinearGradient.lerp(primaryGradient, other.primaryGradient, t) ?? primaryGradient,
      genderTheme: genderTheme,
    );
  }
}

// Helper extension to access custom theme
extension CustomThemeExtensionHelper on ThemeData {
  CustomThemeExtension? get customTheme => extension<CustomThemeExtension>();
}

// Theme provider widget for easy access throughout the app
class ThemeProvider extends InheritedWidget {
  final ThemeService themeService;

  const ThemeProvider({
    super.key,
    required this.themeService,
    required super.child,
  });

  static ThemeProvider? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<ThemeProvider>();
  }

  @override
  bool updateShouldNotify(ThemeProvider oldWidget) {
    return themeService != oldWidget.themeService;
  }
}

// Helper methods for accessing theme service
class ThemeHelper {
  static ThemeService of(BuildContext context) {
    final provider = ThemeProvider.of(context);
    if (provider != null) {
      return provider.themeService;
    }
    return ThemeService(); // Fallback to singleton
  }

  static Color getPrimaryColor(BuildContext context) {
    return of(context).primaryColor;
  }

  static List<Color> getGradientColors(BuildContext context) {
    return of(context).gradientColors;
  }

  static bool isFemaleTheme(BuildContext context) {
    return of(context).isFemaleTheme;
  }

  static bool isMaleTheme(BuildContext context) {
    return of(context).isMaleTheme;
  }

  static BoxDecoration getPrimaryGradientDecoration(
    BuildContext context, {
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
  }) {
    return of(context).getPrimaryGradientDecoration(
      borderRadius: borderRadius,
      boxShadow: boxShadow,
    );
  }

  static ButtonStyle getPrimaryButtonStyle(BuildContext context) {
    return of(context).getPrimaryButtonStyle();
  }

  static InputDecoration getInputDecoration(
    BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return of(context).getInputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
    );
  }

  static AppBar getThemedAppBar(
    BuildContext context, {
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
  }) {
    return of(context).getThemedAppBar(
      title: title,
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
    );
  }
}
