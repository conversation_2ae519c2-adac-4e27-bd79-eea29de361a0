import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/payment_model.dart' hide PaymentMethod;
import '../../../../core/enums/payment_method.dart';

class PaymentFilterSheet extends StatefulWidget {
  final DateTimeRange? selectedDateRange;
  final Function({
    DateTimeRange? dateRange,
    List<PaymentMethod>? methods,
    List<PaymentStatus>? statuses,
  }) onFiltersApplied;

  const PaymentFilterSheet({
    super.key,
    this.selectedDateRange,
    required this.onFiltersApplied,
  });

  @override
  State<PaymentFilterSheet> createState() => _PaymentFilterSheetState();
}

class _PaymentFilterSheetState extends State<PaymentFilterSheet> {
  DateTimeRange? _selectedDateRange;
  Set<PaymentMethod> _selectedMethods = {};
  Set<PaymentStatus> _selectedStatuses = {};

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.selectedDateRange;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.borderGray,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Payments',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: _clearFilters,
                  child: const Text('Clear All'),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Filter content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date Range Filter
                  _buildDateRangeFilter(),
                  
                  const SizedBox(height: 24),
                  
                  // Payment Methods Filter
                  _buildPaymentMethodsFilter(),
                  
                  const SizedBox(height: 24),
                  
                  // Status Filter
                  _buildStatusFilter(),
                  
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
          
          // Apply button
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppColors.backgroundWhite,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _applyFilters,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryPurple,
                    foregroundColor: AppColors.textWhite,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Apply Filters',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Range',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        // Quick date options
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickDateChip('Last 7 days', () {
              setState(() {
                _selectedDateRange = DateTimeRange(
                  start: DateTime.now().subtract(const Duration(days: 7)),
                  end: DateTime.now(),
                );
              });
            }),
            _buildQuickDateChip('Last 30 days', () {
              setState(() {
                _selectedDateRange = DateTimeRange(
                  start: DateTime.now().subtract(const Duration(days: 30)),
                  end: DateTime.now(),
                );
              });
            }),
            _buildQuickDateChip('Last 3 months', () {
              setState(() {
                _selectedDateRange = DateTimeRange(
                  start: DateTime.now().subtract(const Duration(days: 90)),
                  end: DateTime.now(),
                );
              });
            }),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Custom date range
        OutlinedButton.icon(
          onPressed: _selectCustomDateRange,
          icon: const Icon(Icons.date_range),
          label: Text(
            _selectedDateRange != null
                ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                : 'Select Custom Range',
          ),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            side: BorderSide(
              color: _selectedDateRange != null 
                  ? AppColors.primaryPurple 
                  : AppColors.borderGray,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickDateChip(String label, VoidCallback onTap) {
    final isSelected = _isQuickDateSelected(label);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.primaryPurple.withValues(alpha: 0.1)
              : AppColors.backgroundGray,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected 
                ? AppColors.primaryPurple 
                : AppColors.borderGray,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected 
                ? AppColors.primaryPurple 
                : AppColors.textDark,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentMethodsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Methods',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: PaymentMethod.values.map((method) {
            final isSelected = _selectedMethods.contains(method);
            
            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedMethods.remove(method);
                  } else {
                    _selectedMethods.add(method);
                  }
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppColors.primaryPurple.withValues(alpha: 0.1)
                      : AppColors.backgroundGray,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected 
                        ? AppColors.primaryPurple 
                        : AppColors.borderGray,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getPaymentMethodIcon(method),
                      size: 16,
                      color: isSelected 
                          ? AppColors.primaryPurple 
                          : AppColors.textLight,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      method.paymentMethodDisplayName,
                      style: TextStyle(
                        color: isSelected 
                            ? AppColors.primaryPurple 
                            : AppColors.textDark,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStatusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Status',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: PaymentStatus.values.map((status) {
            final isSelected = _selectedStatuses.contains(status);
            
            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedStatuses.remove(status);
                  } else {
                    _selectedStatuses.add(status);
                  }
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? _getStatusColor(status).withValues(alpha: 0.1)
                      : AppColors.backgroundGray,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected 
                        ? _getStatusColor(status)
                        : AppColors.borderGray,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getStatusIcon(status),
                      size: 16,
                      color: isSelected 
                          ? _getStatusColor(status)
                          : AppColors.textLight,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      status.statusDisplayName,
                      style: TextStyle(
                        color: isSelected 
                            ? _getStatusColor(status)
                            : AppColors.textDark,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  bool _isQuickDateSelected(String label) {
    if (_selectedDateRange == null) return false;
    
    final now = DateTime.now();
    final start = _selectedDateRange!.start;
    final end = _selectedDateRange!.end;
    
    switch (label) {
      case 'Last 7 days':
        final expectedStart = now.subtract(const Duration(days: 7));
        return _isSameDay(start, expectedStart) && _isSameDay(end, now);
      case 'Last 30 days':
        final expectedStart = now.subtract(const Duration(days: 30));
        return _isSameDay(start, expectedStart) && _isSameDay(end, now);
      case 'Last 3 months':
        final expectedStart = now.subtract(const Duration(days: 90));
        return _isSameDay(start, expectedStart) && _isSameDay(end, now);
      default:
        return false;
    }
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  Future<void> _selectCustomDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primaryPurple,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedDateRange = null;
      _selectedMethods.clear();
      _selectedStatuses.clear();
    });
  }

  void _applyFilters() {
    widget.onFiltersApplied(
      dateRange: _selectedDateRange,
      methods: _selectedMethods.isNotEmpty ? _selectedMethods.toList() : null,
      statuses: _selectedStatuses.isNotEmpty ? _selectedStatuses.toList() : null,
    );
    Navigator.pop(context);
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return Icons.credit_card;
      case PaymentMethod.knet:
        return Icons.payment;
      case PaymentMethod.applePay:
        return Icons.apple;
      case PaymentMethod.googlePay:
        return Icons.g_mobiledata;
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.wallet:
        return Icons.account_balance_wallet;
      case PaymentMethod.paypal:
        return Icons.payment;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
    }
  }

  IconData _getStatusIcon(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.completed:
        return Icons.check_circle;
      case PaymentStatus.pending:
      case PaymentStatus.processing:
        return Icons.pending;
      case PaymentStatus.failed:
        return Icons.error;
      case PaymentStatus.cancelled:
        return Icons.cancel;
      case PaymentStatus.refunded:
      case PaymentStatus.partiallyRefunded:
        return Icons.undo;
    }
  }

  Color _getStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.completed:
        return AppColors.success;
      case PaymentStatus.pending:
      case PaymentStatus.processing:
        return AppColors.warning;
      case PaymentStatus.failed:
        return AppColors.error;
      case PaymentStatus.cancelled:
        return AppColors.textLight;
      case PaymentStatus.refunded:
      case PaymentStatus.partiallyRefunded:
        return AppColors.primaryPurple;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// Extension to add display names
extension PaymentStatusExtension on PaymentStatus {
  String get statusDisplayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.cancelled:
        return 'Cancelled';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.partiallyRefunded:
        return 'Partially Refunded';
    }
  }
}
