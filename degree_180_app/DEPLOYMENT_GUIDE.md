# 🚀 دليل النشر والإعداد - 180 Degree App

## 📋 متطلبات النشر

### 🛠️ البيئة التطويرية
- Flutter SDK 3.0+
- Dart SDK 3.0+
- Android Studio / VS Code
- Xcode (للـ iOS)
- Firebase CLI
- Google Cloud Console

### 🔑 المفاتيح والشهادات المطلوبة
- Firebase Project Configuration
- Google Maps API Key
- Apple Developer Account (iOS)
- Google Play Console Account (Android)
- Payment Gateway Keys (K-Net, Visa, Mastercard)

## 🔥 إعداد Firebase

### 1. إنشاء مشروع Firebase
```bash
# تسجيل الدخول إلى Firebase
firebase login

# إنشاء مشروع جديد
firebase projects:create degree-180-app

# تهيئة المشروع
firebase init
```

### 2. تكوين الخدمات
```javascript
// Firebase Configuration
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "degree-180-app.firebaseapp.com",
  projectId: "degree-180-app",
  storageBucket: "degree-180-app.appspot.com",
  messagingSenderId: "*********",
  appId: "1:*********:web:abcdef123456"
};
```

### 3. قواعد الأمان
```javascript
// Firestore Security Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == userId || hasRole('admin'));
      
      match /private/{document=**} {
        allow read, write: if request.auth != null && 
          request.auth.uid == userId;
      }
    }
    
    // Service providers collection
    match /service_providers/{providerId} {
      allow read: if true;
      allow write: if request.auth != null && 
        (request.auth.uid == providerId || hasRole('admin')) &&
        isValidProviderData(request.resource.data);
    }
    
    // Bookings collection
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.customerId || 
         request.auth.uid == resource.data.providerId ||
         hasRole('admin'));
    }
    
    // Chat messages
    match /chats/{chatId}/messages/{messageId} {
      allow read, write: if request.auth != null && 
        isParticipant(chatId, request.auth.uid);
    }
    
    // Helper functions
    function hasRole(role) {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }
    
    function isValidProviderData(data) {
      return data.keys().hasAll(['name', 'serviceType', 'location']) &&
             data.name is string &&
             data.serviceType in ['salon', 'spa', 'barbershop', 'nails'];
    }
    
    function isParticipant(chatId, userId) {
      return chatId.split('_').hasAny([userId]);
    }
  }
}

// Storage Security Rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.uid == userId;
    }
    
    match /providers/{providerId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.uid == providerId;
    }
    
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

## 🗺️ إعداد Google Maps

### 1. الحصول على API Key
```bash
# في Google Cloud Console
1. إنشاء مشروع جديد أو اختيار مشروع موجود
2. تفعيل APIs المطلوبة:
   - Maps SDK for Android
   - Maps SDK for iOS
   - Places API
   - Geocoding API
   - Directions API
3. إنشاء API Key مع القيود المناسبة
```

### 2. تكوين Android
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<application>
    <meta-data
        android:name="com.google.android.geo.API_KEY"
        android:value="YOUR_GOOGLE_MAPS_API_KEY" />
</application>
```

### 3. تكوين iOS
```swift
// ios/Runner/AppDelegate.swift
import GoogleMaps

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("YOUR_GOOGLE_MAPS_API_KEY")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
```

## 💳 إعداد بوابات الدفع

### 1. K-Net Gateway
```dart
// lib/core/config/payment_config.dart
class PaymentConfig {
  static const String knetGatewayUrl = 'https://api.knet.com.kw/v1';
  static const String knetMerchantId = 'YOUR_MERCHANT_ID';
  static const String knetSecretKey = 'YOUR_SECRET_KEY';
}
```

### 2. Visa/Mastercard Gateway
```dart
class CreditCardConfig {
  static const String visaGatewayUrl = 'https://api.visa.com/v1';
  static const String mastercardGatewayUrl = 'https://api.mastercard.com/v1';
  static const String merchantId = 'YOUR_MERCHANT_ID';
  static const String apiKey = 'YOUR_API_KEY';
}
```

## 📱 إعداد الإشعارات

### 1. Firebase Cloud Messaging
```dart
// lib/core/services/notification_config.dart
class NotificationConfig {
  static const String vapidKey = 'YOUR_VAPID_KEY';
  static const String senderId = 'YOUR_SENDER_ID';
  
  static const Map<String, String> channels = {
    'booking': 'Booking Notifications',
    'promotion': 'Promotion Notifications',
    'chat': 'Chat Notifications',
    'general': 'General Notifications',
  };
}
```

### 2. iOS Push Notifications
```swift
// ios/Runner/Info.plist
<key>UIBackgroundModes</key>
<array>
    <string>remote-notification</string>
    <string>background-fetch</string>
</array>
```

## 🔧 متغيرات البيئة

### إنشاء ملف .env
```env
# Firebase Configuration
FIREBASE_PROJECT_ID=degree-180-app
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=degree-180-app.firebaseapp.com
FIREBASE_STORAGE_BUCKET=degree-180-app.appspot.com

# Google Maps
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Payment Gateways
KNET_MERCHANT_ID=your_knet_merchant_id
KNET_SECRET_KEY=your_knet_secret_key
VISA_API_KEY=your_visa_api_key
MASTERCARD_API_KEY=your_mastercard_api_key

# App Configuration
APP_NAME=180 Degree
APP_VERSION=1.0.0
APP_BUILD_NUMBER=1
ENVIRONMENT=production

# Analytics
GOOGLE_ANALYTICS_ID=your_analytics_id
MIXPANEL_TOKEN=your_mixpanel_token

# Social Media
FACEBOOK_APP_ID=your_facebook_app_id
TWITTER_API_KEY=your_twitter_api_key
INSTAGRAM_CLIENT_ID=your_instagram_client_id
```

## 📦 بناء التطبيق للنشر

### Android (Google Play Store)
```bash
# تنظيف المشروع
flutter clean
flutter pub get

# بناء App Bundle
flutter build appbundle --release

# بناء APK
flutter build apk --release --split-per-abi

# التحقق من التوقيع
jarsigner -verify -verbose -certs app-release.aab
```

### iOS (App Store)
```bash
# تنظيف المشروع
flutter clean
flutter pub get

# بناء للـ iOS
flutter build ios --release

# أرشفة التطبيق في Xcode
# Product > Archive
# Distribute App > App Store Connect
```

## 🌐 النشر على الويب

### إعداد Firebase Hosting
```bash
# تهيئة Hosting
firebase init hosting

# بناء التطبيق للويب
flutter build web --release

# نشر على Firebase
firebase deploy --only hosting
```

### تكوين PWA
```html
<!-- web/manifest.json -->
{
  "name": "180 Degree",
  "short_name": "180 Degree",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#6C63FF",
  "theme_color": "#6C63FF",
  "description": "Premium Beauty & Personal Care Services",
  "orientation": "portrait-primary",
  "prefer_related_applications": false,
  "icons": [
    {
      "src": "icons/Icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "icons/Icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

## 🔍 اختبار ما قبل النشر

### 1. اختبارات الوحدة
```bash
flutter test
```

### 2. اختبارات التكامل
```bash
flutter test integration_test/
```

### 3. اختبارات الأداء
```bash
flutter drive --target=test_driver/perf_test.dart
```

### 4. اختبار الأمان
```bash
# فحص التبعيات
flutter pub deps
dart pub audit

# فحص الكود
flutter analyze
dart fix --dry-run
```

## 📊 مراقبة ما بعد النشر

### 1. Firebase Analytics
```dart
// تتبع الأحداث المهمة
FirebaseAnalytics.instance.logEvent(
  name: 'booking_completed',
  parameters: {
    'provider_id': providerId,
    'service_type': serviceType,
    'amount': amount,
  },
);
```

### 2. Crashlytics
```dart
// تسجيل الأخطاء
FirebaseCrashlytics.instance.recordError(
  error,
  stackTrace,
  fatal: false,
);
```

### 3. Performance Monitoring
```dart
// مراقبة الأداء
final trace = FirebasePerformance.instance.newTrace('booking_flow');
trace.start();
// ... عملية الحجز
trace.stop();
```

## 🔄 التحديثات والصيانة

### 1. تحديثات OTA
```dart
// فحص التحديثات
final updateInfo = await InAppUpdate.checkForUpdate();
if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
  await InAppUpdate.performImmediateUpdate();
}
```

### 2. صيانة قاعدة البيانات
```javascript
// Cloud Functions للصيانة الدورية
exports.dailyMaintenance = functions.pubsub
  .schedule('0 2 * * *')
  .timeZone('Asia/Kuwait')
  .onRun(async (context) => {
    // تنظيف البيانات القديمة
    // تحديث الإحصائيات
    // إرسال التقارير
  });
```

## ✅ قائمة التحقق النهائية

- [ ] تكوين Firebase مكتمل
- [ ] Google Maps يعمل بشكل صحيح
- [ ] بوابات الدفع مختبرة
- [ ] الإشعارات تعمل
- [ ] اختبارات شاملة مكتملة
- [ ] أيقونات التطبيق محدثة
- [ ] وصف المتجر جاهز
- [ ] لقطات الشاشة محضرة
- [ ] سياسة الخصوصية منشورة
- [ ] شروط الاستخدام جاهزة
- [ ] دعم العملاء مُعد
- [ ] نظام المراقبة فعال

---

## 📞 الدعم التقني

للحصول على المساعدة في النشر:
- البريد الإلكتروني: <EMAIL>
- الوثائق التقنية: https://docs.180degree.com
- مجتمع المطورين: https://community.180degree.com

**التطبيق جاهز للنشر والإنتاج! 🚀**
