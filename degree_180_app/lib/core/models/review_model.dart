import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

class ReviewModel extends Equatable {
  final String id;
  final String customerId;
  final String customerName;
  final String? customerAvatar;
  final String providerId;
  final String? bookingId;
  final double overallRating;
  final Map<String, double> detailedRatings; // quality, time, cleanliness, professionalism
  final String comment;
  final List<String> images;
  final bool isAnonymous;
  final int helpfulCount;
  final int notHelpfulCount;
  final List<String> helpfulBy;
  final List<String> notHelpfulBy;
  final String? providerResponse;
  final DateTime? providerResponseDate;
  final bool isVerified;
  final bool isReported;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ReviewModel({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.customerAvatar,
    required this.providerId,
    this.bookingId,
    required this.overallRating,
    this.detailedRatings = const {},
    required this.comment,
    this.images = const [],
    this.isAnonymous = false,
    this.helpfulCount = 0,
    this.notHelpfulCount = 0,
    this.helpfulBy = const [],
    this.notHelpfulBy = const [],
    this.providerResponse,
    this.providerResponseDate,
    this.isVerified = false,
    this.isReported = false,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] ?? '',
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerAvatar: json['customerAvatar'],
      providerId: json['providerId'] ?? '',
      bookingId: json['bookingId'],
      overallRating: (json['overallRating'] ?? 0.0).toDouble(),
      detailedRatings: Map<String, double>.from(json['detailedRatings'] ?? {}),
      comment: json['comment'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      isAnonymous: json['isAnonymous'] ?? false,
      helpfulCount: json['helpfulCount'] ?? 0,
      notHelpfulCount: json['notHelpfulCount'] ?? 0,
      helpfulBy: List<String>.from(json['helpfulBy'] ?? []),
      notHelpfulBy: List<String>.from(json['notHelpfulBy'] ?? []),
      providerResponse: json['providerResponse'],
      providerResponseDate: json['providerResponseDate'] != null
          ? _parseDateTime(json['providerResponseDate'])
          : null,
      isVerified: json['isVerified'] ?? false,
      isReported: json['isReported'] ?? false,
      metadata: json['metadata'],
      createdAt: _parseDateTime(json['createdAt']),
      updatedAt: _parseDateTime(json['updatedAt']),
    );
  }

  factory ReviewModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    data['id'] = doc.id;
    return ReviewModel.fromJson(data);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customerId': customerId,
      'customerName': customerName,
      'customerAvatar': customerAvatar,
      'providerId': providerId,
      'bookingId': bookingId,
      'overallRating': overallRating,
      'detailedRatings': detailedRatings,
      'comment': comment,
      'images': images,
      'isAnonymous': isAnonymous,
      'helpfulCount': helpfulCount,
      'notHelpfulCount': notHelpfulCount,
      'helpfulBy': helpfulBy,
      'notHelpfulBy': notHelpfulBy,
      'providerResponse': providerResponse,
      'providerResponseDate': providerResponseDate != null
          ? Timestamp.fromDate(providerResponseDate!)
          : null,
      'isVerified': isVerified,
      'isReported': isReported,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id');
    return json;
  }

  ReviewModel copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerAvatar,
    String? providerId,
    String? bookingId,
    double? overallRating,
    Map<String, double>? detailedRatings,
    String? comment,
    List<String>? images,
    bool? isAnonymous,
    int? helpfulCount,
    int? notHelpfulCount,
    List<String>? helpfulBy,
    List<String>? notHelpfulBy,
    String? providerResponse,
    DateTime? providerResponseDate,
    bool? isVerified,
    bool? isReported,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerAvatar: customerAvatar ?? this.customerAvatar,
      providerId: providerId ?? this.providerId,
      bookingId: bookingId ?? this.bookingId,
      overallRating: overallRating ?? this.overallRating,
      detailedRatings: detailedRatings ?? this.detailedRatings,
      comment: comment ?? this.comment,
      images: images ?? this.images,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      notHelpfulCount: notHelpfulCount ?? this.notHelpfulCount,
      helpfulBy: helpfulBy ?? this.helpfulBy,
      notHelpfulBy: notHelpfulBy ?? this.notHelpfulBy,
      providerResponse: providerResponse ?? this.providerResponse,
      providerResponseDate: providerResponseDate ?? this.providerResponseDate,
      isVerified: isVerified ?? this.isVerified,
      isReported: isReported ?? this.isReported,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName {
    return isAnonymous ? 'Anonymous User' : customerName;
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 30) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  bool get hasProviderResponse {
    return providerResponse != null && providerResponse!.isNotEmpty;
  }

  bool get hasImages {
    return images.isNotEmpty;
  }

  double get qualityRating {
    return detailedRatings['quality'] ?? overallRating;
  }

  double get timeRating {
    return detailedRatings['time'] ?? overallRating;
  }

  double get cleanlinessRating {
    return detailedRatings['cleanliness'] ?? overallRating;
  }

  double get professionalismRating {
    return detailedRatings['professionalism'] ?? overallRating;
  }

  int get totalVotes {
    return helpfulCount + notHelpfulCount;
  }

  double get helpfulPercentage {
    if (totalVotes == 0) return 0.0;
    return (helpfulCount / totalVotes) * 100;
  }

  bool isHelpfulBy(String userId) {
    return helpfulBy.contains(userId);
  }

  bool isNotHelpfulBy(String userId) {
    return notHelpfulBy.contains(userId);
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is String) return DateTime.parse(value);
    if (value is DateTime) return value;
    return DateTime.now();
  }

  @override
  List<Object?> get props => [
        id,
        customerId,
        customerName,
        customerAvatar,
        providerId,
        bookingId,
        overallRating,
        detailedRatings,
        comment,
        images,
        isAnonymous,
        helpfulCount,
        notHelpfulCount,
        helpfulBy,
        notHelpfulBy,
        providerResponse,
        providerResponseDate,
        isVerified,
        isReported,
        metadata,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'ReviewModel(id: $id, rating: $overallRating, customer: $displayName)';
  }
}
