import 'package:equatable/equatable.dart';

enum BookingStatus {
  pending,
  confirmed,
  inProgress,
  completed,
  cancelled,
  noShow,
}

enum PaymentStatus {
  pending,
  paid,
  refunded,
  failed,
}

class BookingEntity extends Equatable {
  final String id;
  final String customerId;
  final String providerId;
  final String serviceId;
  final DateTime bookingDate;
  final DateTime startTime;
  final DateTime endTime;
  final BookingStatus status;
  final PaymentStatus paymentStatus;
  final double totalAmount;
  final double? discountAmount;
  final String? notes;
  final String? cancellationReason;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalData;

  const BookingEntity({
    required this.id,
    required this.customerId,
    required this.providerId,
    required this.serviceId,
    required this.bookingDate,
    required this.startTime,
    required this.endTime,
    required this.status,
    required this.paymentStatus,
    required this.totalAmount,
    this.discountAmount,
    this.notes,
    this.cancellationReason,
    required this.createdAt,
    required this.updatedAt,
    this.additionalData,
  });

  @override
  List<Object?> get props => [
        id,
        customerId,
        providerId,
        serviceId,
        bookingDate,
        startTime,
        endTime,
        status,
        paymentStatus,
        totalAmount,
        discountAmount,
        notes,
        cancellationReason,
        createdAt,
        updatedAt,
        additionalData,
      ];

  BookingEntity copyWith({
    String? id,
    String? customerId,
    String? providerId,
    String? serviceId,
    DateTime? bookingDate,
    DateTime? startTime,
    DateTime? endTime,
    BookingStatus? status,
    PaymentStatus? paymentStatus,
    double? totalAmount,
    double? discountAmount,
    String? notes,
    String? cancellationReason,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return BookingEntity(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      providerId: providerId ?? this.providerId,
      serviceId: serviceId ?? this.serviceId,
      bookingDate: bookingDate ?? this.bookingDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      totalAmount: totalAmount ?? this.totalAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      notes: notes ?? this.notes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  Duration get duration => endTime.difference(startTime);

  String get statusString {
    switch (status) {
      case BookingStatus.pending:
        return 'pending';
      case BookingStatus.confirmed:
        return 'confirmed';
      case BookingStatus.inProgress:
        return 'in_progress';
      case BookingStatus.completed:
        return 'completed';
      case BookingStatus.cancelled:
        return 'cancelled';
      case BookingStatus.noShow:
        return 'no_show';
    }
  }

  String get paymentStatusString {
    switch (paymentStatus) {
      case PaymentStatus.pending:
        return 'pending';
      case PaymentStatus.paid:
        return 'paid';
      case PaymentStatus.refunded:
        return 'refunded';
      case PaymentStatus.failed:
        return 'failed';
    }
  }

  bool get canBeCancelled {
    return status == BookingStatus.pending || status == BookingStatus.confirmed;
  }

  bool get canBeRescheduled {
    return status == BookingStatus.pending || status == BookingStatus.confirmed;
  }

  bool get isUpcoming {
    return startTime.isAfter(DateTime.now()) && 
           (status == BookingStatus.pending || status == BookingStatus.confirmed);
  }

  bool get isPast {
    return endTime.isBefore(DateTime.now());
  }

  double get finalAmount {
    return totalAmount - (discountAmount ?? 0);
  }
}
