import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../domain/repositories/booking_repository.dart';
import '../../domain/usecases/get_available_time_slots_usecase.dart';
import '../../domain/usecases/create_advanced_booking_usecase.dart';
import '../../domain/usecases/reschedule_booking_usecase.dart';
import '../../domain/usecases/get_booking_statistics_usecase.dart';

import 'advanced_booking_event.dart';
import 'advanced_booking_state.dart';

class AdvancedBookingBloc extends Bloc<AdvancedBookingEvent, AdvancedBookingState> {
  final GetAvailableTimeSlotsUseCase getAvailableTimeSlotsUseCase;
  final CreateAdvancedBookingUseCase createAdvancedBookingUseCase;
  final RescheduleBookingUseCase rescheduleBookingUseCase;
  final GetBookingStatisticsUseCase getBookingStatisticsUseCase;

  AdvancedBookingBloc({
    required this.getAvailableTimeSlotsUseCase,
    required this.createAdvancedBookingUseCase,
    required this.rescheduleBookingUseCase,
    required this.getBookingStatisticsUseCase,
  }) : super(AdvancedBookingInitial()) {
    on<LoadAvailableTimeSlotsEvent>(_onLoadAvailableTimeSlots);
    on<CreateAdvancedBookingEvent>(_onCreateAdvancedBooking);
    on<RescheduleBookingEvent>(_onRescheduleBooking);
    on<LoadBookingStatisticsEvent>(_onLoadBookingStatistics);
    on<ResetBookingStateEvent>(_onResetBookingState);
  }

  Future<void> _onLoadAvailableTimeSlots(
    LoadAvailableTimeSlotsEvent event,
    Emitter<AdvancedBookingState> emit,
  ) async {
    emit(TimeSlotsLoading());

    final result = await getAvailableTimeSlotsUseCase(
      GetAvailableTimeSlotsParams(
        providerId: event.providerId,
        date: event.date,
        serviceDurationMinutes: event.serviceDurationMinutes,
      ),
    );

    result.fold(
      (failure) => emit(TimeSlotsError(message: failure.message)),
      (timeSlots) {
        if (timeSlots.isEmpty) {
          emit(const TimeSlotsEmpty(message: 'No available time slots for this date'));
        } else {
          emit(TimeSlotsLoaded(timeSlots: timeSlots));
        }
      },
    );
  }

  Future<void> _onCreateAdvancedBooking(
    CreateAdvancedBookingEvent event,
    Emitter<AdvancedBookingState> emit,
  ) async {
    emit(BookingCreating());

    final result = await createAdvancedBookingUseCase(
      CreateAdvancedBookingParams(
        customerId: event.customerId,
        customerName: 'Customer', // Default value
        providerId: event.providerId,
        providerName: 'Provider', // Default value
        serviceId: event.serviceId,
        serviceName: 'Service', // Default value
        service: null, // Will be set later
        bookingDate: (event.timeSlot as TimeSlotModel).startTime,
        appointmentDateTime: (event.timeSlot as TimeSlotModel).startTime,
        timeSlot: (event.timeSlot as TimeSlotModel).formattedTime,
        totalAmount: 0.0, // Will be calculated
        currency: 'KD',
        notes: event.notes,
      ),
    );

    result.fold(
      (failure) => emit(BookingCreationError(message: failure.message)),
      (booking) => emit(BookingCreated(booking: booking)),
    );
  }

  Future<void> _onRescheduleBooking(
    RescheduleBookingEvent event,
    Emitter<AdvancedBookingState> emit,
  ) async {
    emit(BookingRescheduling());

    final result = await rescheduleBookingUseCase(
      RescheduleBookingParams(
        bookingId: event.bookingId,
        newDate: event.newDate,
        newTimeSlot: event.newTimeSlot,
      ),
    );

    result.fold(
      (failure) => emit(BookingRescheduleError(message: failure.message)),
      (booking) => emit(BookingRescheduled(booking: booking)),
    );
  }

  Future<void> _onLoadBookingStatistics(
    LoadBookingStatisticsEvent event,
    Emitter<AdvancedBookingState> emit,
  ) async {
    emit(const BookingStatisticsLoading());

    final result = await getBookingStatisticsUseCase(
      GetBookingStatisticsParams(
        providerId: event.providerId,
        startDate: event.startDate,
        endDate: event.endDate,
      ),
    );

    result.fold(
      (failure) => emit(BookingStatisticsError(message: failure.message)),
      (statistics) => emit(BookingStatisticsLoaded(statistics: statistics)),
    );
  }

  void _onResetBookingState(
    ResetBookingStateEvent event,
    Emitter<AdvancedBookingState> emit,
  ) {
    emit(AdvancedBookingInitial());
  }
}

// Events
abstract class AdvancedBookingEvent extends Equatable {
  const AdvancedBookingEvent();

  @override
  List<Object?> get props => [];
}

class LoadAvailableTimeSlotsEvent extends AdvancedBookingEvent {
  final String providerId;
  final DateTime date;
  final int serviceDurationMinutes;

  const LoadAvailableTimeSlotsEvent({
    required this.providerId,
    required this.date,
    required this.serviceDurationMinutes,
  });

  @override
  List<Object> get props => [providerId, date, serviceDurationMinutes];
}

class CreateAdvancedBookingEvent extends AdvancedBookingEvent {
  final String customerId;
  final String customerName;
  final String? customerPhone;
  final String? customerEmail;
  final String providerId;
  final String providerName;
  final String serviceId;
  final String timeSlotId;
  final DateTime bookingDate;
  final String timeSlot;
  final double totalAmount;
  final String currency;
  final double? discountAmount;
  final String? couponCode;
  final String? notes;
  final Map<String, dynamic>? paymentInfo;
  final int serviceDurationMinutes;

  const CreateAdvancedBookingEvent({
    required this.customerId,
    required this.customerName,
    this.customerPhone,
    this.customerEmail,
    required this.providerId,
    required this.providerName,
    required this.serviceId,
    required this.timeSlotId,
    required this.bookingDate,
    required this.timeSlot,
    required this.totalAmount,
    this.currency = 'KD',
    this.discountAmount,
    this.couponCode,
    this.notes,
    this.paymentInfo,
    required this.serviceDurationMinutes,
  });

  @override
  List<Object?> get props => [
        customerId,
        customerName,
        customerPhone,
        customerEmail,
        providerId,
        providerName,
        serviceId,
        timeSlotId,
        bookingDate,
        timeSlot,
        totalAmount,
        currency,
        discountAmount,
        couponCode,
        notes,
        paymentInfo,
        serviceDurationMinutes,
      ];
}

class RescheduleBookingEvent extends AdvancedBookingEvent {
  final String bookingId;
  final DateTime newDate;
  final String newTimeSlot;

  const RescheduleBookingEvent({
    required this.bookingId,
    required this.newDate,
    required this.newTimeSlot,
  });

  @override
  List<Object> get props => [bookingId, newDate, newTimeSlot];
}

class LoadBookingStatisticsEvent extends AdvancedBookingEvent {
  final String providerId;
  final DateTime? startDate;
  final DateTime? endDate;

  const LoadBookingStatisticsEvent({
    required this.providerId,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [providerId, startDate, endDate];
}

class ResetBookingStateEvent extends AdvancedBookingEvent {
  const ResetBookingStateEvent();
}

// States
abstract class AdvancedBookingState extends Equatable {
  const AdvancedBookingState();

  @override
  List<Object?> get props => [];
}

class AdvancedBookingInitial extends AdvancedBookingState {}

// Time Slots States
class TimeSlotsLoading extends AdvancedBookingState {}

class TimeSlotsLoaded extends AdvancedBookingState {
  final List<TimeSlotModel> timeSlots;

  const TimeSlotsLoaded({required this.timeSlots});

  @override
  List<Object> get props => [timeSlots];
}

class TimeSlotsEmpty extends AdvancedBookingState {
  final String message;

  const TimeSlotsEmpty({required this.message});

  @override
  List<Object> get props => [message];
}

class TimeSlotsError extends AdvancedBookingState {
  final String message;

  const TimeSlotsError({required this.message});

  @override
  List<Object> get props => [message];
}

// Booking Creation States
class BookingCreating extends AdvancedBookingState {}

class BookingCreated extends AdvancedBookingState {
  final BookingModel booking;

  const BookingCreated({required this.booking});

  @override
  List<Object> get props => [booking];
}

class BookingCreationError extends AdvancedBookingState {
  final String message;

  const BookingCreationError({required this.message});

  @override
  List<Object> get props => [message];
}

// Booking Reschedule States
class BookingRescheduling extends AdvancedBookingState {}

class BookingRescheduled extends AdvancedBookingState {
  final BookingModel booking;

  const BookingRescheduled({required this.booking});

  @override
  List<Object> get props => [booking];
}

class BookingRescheduleError extends AdvancedBookingState {
  final String message;

  const BookingRescheduleError({required this.message});

  @override
  List<Object> get props => [message];
}


