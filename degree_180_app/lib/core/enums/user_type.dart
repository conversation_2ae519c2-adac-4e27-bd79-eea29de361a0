enum UserType {
  customer,
  serviceProvider,
  admin,
  moderator,
  guest,
}

extension UserTypeExtension on UserType {
  String get displayName {
    switch (this) {
      case UserType.customer:
        return 'عميل';
      case UserType.serviceProvider:
        return 'مقدم خدمة';
      case UserType.admin:
        return 'مدير';
      case UserType.moderator:
        return 'مشرف';
      case UserType.guest:
        return 'زائر';
    }
  }

  String get displayNameEn {
    switch (this) {
      case UserType.customer:
        return 'Customer';
      case UserType.serviceProvider:
        return 'Service Provider';
      case UserType.admin:
        return 'Admin';
      case UserType.moderator:
        return 'Moderator';
      case UserType.guest:
        return 'Guest';
    }
  }

  String get description {
    switch (this) {
      case UserType.customer:
        return 'يمكنني حجز الخدمات وكتابة المراجعات';
      case UserType.serviceProvider:
        return 'أقدم خدمات الجمال والعناية الشخصية';
      case UserType.admin:
        return 'إدارة كاملة للتطبيق والمستخدمين';
      case UserType.moderator:
        return 'مراجعة المحتوى والإشراف على الخدمات';
      case UserType.guest:
        return 'تصفح الخدمات بدون حساب';
    }
  }

  String get descriptionEn {
    switch (this) {
      case UserType.customer:
        return 'I can book services and write reviews';
      case UserType.serviceProvider:
        return 'I provide beauty and personal care services';
      case UserType.admin:
        return 'Full management of app and users';
      case UserType.moderator:
        return 'Content review and service moderation';
      case UserType.guest:
        return 'Browse services without account';
    }
  }

  List<String> get permissions {
    switch (this) {
      case UserType.customer:
        return [
          'book_services',
          'write_reviews',
          'chat_with_providers',
          'view_services',
          'manage_profile',
          'view_bookings',
          'cancel_bookings',
          'rate_services',
        ];
      case UserType.serviceProvider:
        return [
          'manage_services',
          'view_bookings',
          'accept_bookings',
          'reject_bookings',
          'chat_with_customers',
          'manage_schedule',
          'view_reviews',
          'respond_to_reviews',
          'manage_profile',
          'view_analytics',
          'manage_promotions',
        ];
      case UserType.admin:
        return [
          'manage_all_users',
          'manage_all_services',
          'manage_all_bookings',
          'view_all_analytics',
          'manage_app_settings',
          'moderate_content',
          'manage_payments',
          'send_notifications',
          'manage_promotions',
          'export_data',
          'manage_categories',
          'manage_locations',
        ];
      case UserType.moderator:
        return [
          'moderate_reviews',
          'moderate_services',
          'moderate_users',
          'view_reports',
          'manage_content',
          'send_warnings',
          'view_analytics',
        ];
      case UserType.guest:
        return [
          'view_services',
          'view_providers',
          'view_reviews',
          'search_services',
        ];
    }
  }

  bool get canBookServices => this == UserType.customer;
  bool get canProvideServices => this == UserType.serviceProvider;
  bool get canModerateContent => this == UserType.admin || this == UserType.moderator;
  bool get canManageApp => this == UserType.admin;
  bool get requiresVerification => this == UserType.serviceProvider;
  bool get canAccessAdminPanel => this == UserType.admin || this == UserType.moderator;
}
