import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';

/// مؤشر الكتابة المتحرك
class TypingIndicator extends StatefulWidget {
  final String text;
  final Duration duration;
  final TextStyle? textStyle;
  final bool showCursor;
  final String cursorChar;
  final Color? cursorColor;

  const TypingIndicator({
    super.key,
    required this.text,
    this.duration = const Duration(milliseconds: 100),
    this.textStyle,
    this.showCursor = true,
    this.cursorChar = '|',
    this.cursorColor,
  });

  @override
  State<TypingIndicator> createState() => _TypingIndicatorState();
}

class _TypingIndicatorState extends State<TypingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _typingController;
  late AnimationController _cursorController;
  late Animation<int> _typingAnimation;
  late Animation<double> _cursorAnimation;

  @override
  void initState() {
    super.initState();
    
    // تحكم في الكتابة
    _typingController = AnimationController(
      duration: Duration(milliseconds: widget.text.length * widget.duration.inMilliseconds),
      vsync: this,
    );
    
    _typingAnimation = IntTween(
      begin: 0,
      end: widget.text.length,
    ).animate(CurvedAnimation(
      parent: _typingController,
      curve: Curves.easeInOut,
    ));

    // تحكم في المؤشر
    _cursorController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _cursorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cursorController,
      curve: Curves.easeInOut,
    ));

    // بدء الرسوم المتحركة
    _startAnimations();
  }

  void _startAnimations() {
    _typingController.forward();
    if (widget.showCursor) {
      _cursorController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _typingController.dispose();
    _cursorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_typingAnimation, _cursorAnimation]),
      builder: (context, child) {
        final displayedText = widget.text.substring(0, _typingAnimation.value);
        final showCursor = widget.showCursor && 
                          (_typingController.isCompleted || _cursorAnimation.value > 0.5);

        return RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: displayedText,
                style: widget.textStyle ?? GoogleFonts.cairo(
                  fontSize: 16,
                  color: AppColors.textDark,
                ),
              ),
              if (showCursor)
                TextSpan(
                  text: widget.cursorChar,
                  style: (widget.textStyle ?? GoogleFonts.cairo(
                    fontSize: 16,
                    color: AppColors.textDark,
                  )).copyWith(
                    color: widget.cursorColor ?? AppColors.primaryPurple,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  /// إعادة تشغيل الرسوم المتحركة
  void restart() {
    _typingController.reset();
    _cursorController.reset();
    _startAnimations();
  }

  /// إيقاف الرسوم المتحركة
  void stop() {
    _typingController.stop();
    _cursorController.stop();
  }

  /// إكمال الرسوم المتحركة فوراً
  void complete() {
    _typingController.forward();
  }
}

/// مؤشر كتابة بسيط مع نقاط متحركة
class SimpleTypingIndicator extends StatefulWidget {
  final Color? color;
  final double size;
  final Duration duration;

  const SimpleTypingIndicator({
    super.key,
    this.color,
    this.size = 8.0,
    this.duration = const Duration(milliseconds: 600),
  });

  @override
  State<SimpleTypingIndicator> createState() => _SimpleTypingIndicatorState();
}

class _SimpleTypingIndicatorState extends State<SimpleTypingIndicator>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    _controllers = List.generate(3, (index) {
      return AnimationController(
        duration: widget.duration,
        vsync: this,
      );
    });

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();

    _startAnimations();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: widget.size * 0.2),
              child: Opacity(
                opacity: 0.3 + (0.7 * _animations[index].value),
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.color ?? AppColors.primaryPurple,
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

/// مؤشر كتابة مع رسالة
class TypingIndicatorWithMessage extends StatelessWidget {
  final String message;
  final Color? color;
  final TextStyle? textStyle;

  const TypingIndicatorWithMessage({
    super.key,
    this.message = 'يكتب...',
    this.color,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SimpleTypingIndicator(
          color: color,
          size: 6.0,
        ),
        const SizedBox(width: 8),
        Text(
          message,
          style: textStyle ?? GoogleFonts.cairo(
            fontSize: 14,
            color: color ?? AppColors.textGray,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }
}

/// مؤشر كتابة للدردشة
class ChatTypingIndicator extends StatelessWidget {
  final String? userName;
  final Color? backgroundColor;
  final EdgeInsets? padding;

  const ChatTypingIndicator({
    super.key,
    this.userName,
    this.backgroundColor,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.backgroundGray,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (userName != null) ...[
            Text(
              userName!,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(width: 8),
          ],
          SimpleTypingIndicator(
            color: AppColors.primaryPurple,
            size: 6.0,
          ),
        ],
      ),
    );
  }
}
