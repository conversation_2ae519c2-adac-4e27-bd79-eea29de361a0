import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'injection_container.dart' as di;
import 'themed_app.dart';
import 'core/services/memory_manager.dart';
import 'core/services/network_optimizer.dart';
import 'core/state/app_state_manager.dart';
import 'core/analytics/performance_analytics.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize core systems
  await _initializeCoreServices();

  // Initialize dependency injection
  await di.init();

  runApp(const ThemedDegree180App());
}

/// تهيئة الخدمات الأساسية للتطبيق
Future<void> _initializeCoreServices() async {
  try {
    // تهيئة نظام إدارة الذاكرة
    await MemoryManager.instance.initialize();

    // تهيئة نظام تحسين الشبكة
    await NetworkOptimizer.instance.initialize();

    // تهيئة نظام إدارة الحالة
    await AppStateManager.instance.initialize();

    // تهيئة نظام تحليلات الأداء
    await PerformanceAnalytics.instance.initialize();

    debugPrint('✅ تم تهيئة جميع الأنظمة الأساسية بنجاح');
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة الأنظمة الأساسية: $e');
    // يمكن إضافة معالجة أخطاء إضافية هنا
  }
}




