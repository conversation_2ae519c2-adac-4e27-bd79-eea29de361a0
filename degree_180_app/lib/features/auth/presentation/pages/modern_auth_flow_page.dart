import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/enums/service_type.dart';
import '../../../../core/theme/theme_manager.dart';
import '../widgets/auth_step_indicator.dart';
import '../bloc/auth_flow_bloc.dart';

/// صفحة تدفق المصادقة العصرية والموحدة
class ModernAuthFlowPage extends StatefulWidget {
  const ModernAuthFlowPage({super.key});

  @override
  State<ModernAuthFlowPage> createState() => _ModernAuthFlowPageState();
}

class _ModernAuthFlowPageState extends State<ModernAuthFlowPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    // بدء تدفق المصادقة
    context.read<AuthFlowBloc>().add(const AuthFlowStarted());
  }

  void _initializeControllers() {
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
  }

  void _initializeAnimations() {
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onStepChanged(int step) {
    _pageController.animateToPage(
      step,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AuthFlowBloc(),
      child: BlocConsumer<AuthFlowBloc, AuthFlowState>(
        listener: (context, state) {
          if (state is AuthFlowInProgress) {
            _onStepChanged(state.currentStep);
          } else if (state is AuthFlowSuccess) {
            // الانتقال للصفحة الرئيسية
            if (state.selectedUserType == 'customer') {
              context.go('/customer-home');
            } else {
              context.go('/worker-dashboard');
            }
          } else if (state is AuthFlowError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Theme(
            data: ThemeManager.currentTheme,
            child: Scaffold(
              backgroundColor: AppColors.backgroundWhite,
              body: SafeArea(
                child: Column(
                  children: [
                    // Header مع مؤشر التقدم
                    _buildHeader(state),
                    
                    // محتوى الخطوات
                    Expanded(
                      child: _buildStepContent(state),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(AuthFlowState state) {
    final currentStep = state is AuthFlowInProgress ? state.currentStep : 0;
    final totalSteps = _getTotalSteps(state);
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: ThemeManager.currentColors.gradient,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppConstants.largeBorderRadius),
          bottomRight: Radius.circular(AppConstants.largeBorderRadius),
        ),
      ),
      child: Column(
        children: [
          // زر الرجوع والعنوان
          Row(
            children: [
              if (currentStep > 0)
                IconButton(
                  onPressed: () {
                    context.read<AuthFlowBloc>().add(
                      AuthFlowStepChanged(currentStep - 1),
                    );
                  },
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: AppColors.textWhite,
                  ),
                ),
              Expanded(
                child: Text(
                  AppStrings.appName,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.cairo(
                    fontSize: AppConstants.fontSizeExtraLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textWhite,
                  ),
                ),
              ),
              const SizedBox(width: 48), // للتوازن
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // مؤشر التقدم
          AuthStepIndicator(
            currentStep: currentStep,
            totalSteps: totalSteps,
            completedSteps: currentStep,
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(AuthFlowState state) {
    if (state is! AuthFlowInProgress) {
      return const Center(child: CircularProgressIndicator());
    }

    return PageView(
      controller: _pageController,
      physics: const NeverScrollableScrollPhysics(),
      children: _buildSteps(state),
    );
  }

  List<Widget> _buildSteps(AuthFlowInProgress state) {
    final steps = <Widget>[
      // خطوة اختيار الجنس
      _buildGenderSelectionStep(state),
      
      // خطوة اختيار نوع المستخدم
      _buildUserTypeSelectionStep(state),
    ];

    // إضافة خطوة اختيار نوع الخدمة للعامل
    if (state.selectedUserType == 'worker') {
      steps.add(_buildServiceTypeSelectionStep(state));
    }

    // خطوة المصادقة
    steps.add(_buildAuthenticationStep(state));

    return steps;
  }

  Widget _buildGenderSelectionStep(AuthFlowInProgress state) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              _buildStepHeader(
                'اختر الثيم المفضل',
                'اختر الثيم الذي يعكس شخصيتك ويجعل تجربتك أكثر متعة',
              ),
              
              const SizedBox(height: AppConstants.extraLargePadding),
              
              Expanded(
                child: _buildGenderOptions(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserTypeSelectionStep(AuthFlowInProgress state) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              _buildStepHeader(
                'نوع الحساب',
                'هل أنت عميل تبحث عن خدمات أم مقدم خدمة؟',
              ),
              
              const SizedBox(height: AppConstants.extraLargePadding),
              
              Expanded(
                child: _buildUserTypeOptions(state),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServiceTypeSelectionStep(AuthFlowInProgress state) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              _buildStepHeader(
                'نوع الخدمة',
                'ما نوع الخدمة التي تقدمها؟',
              ),
              
              const SizedBox(height: AppConstants.extraLargePadding),
              
              Expanded(
                child: _buildServiceTypeOptions(state),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAuthenticationStep(AuthFlowInProgress state) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              _buildStepHeader(
                state.isLoginMode ? 'تسجيل الدخول' : 'إنشاء حساب',
                state.isLoginMode 
                    ? 'أدخل بياناتك لتسجيل الدخول'
                    : 'أدخل بياناتك لإنشاء حساب جديد',
              ),
              
              const SizedBox(height: AppConstants.extraLargePadding),
              
              Expanded(
                child: _buildAuthForm(state),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepHeader(String title, String subtitle) {
    return Column(
      children: [
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: AppConstants.fontSizeExtraLarge,
            fontWeight: FontWeight.bold,
            color: ThemeManager.currentColors.primary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        Text(
          subtitle,
          style: GoogleFonts.cairo(
            fontSize: AppConstants.fontSizeMedium,
            color: AppColors.textGray,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildGenderOptions() {
    // سيتم إضافة المحتوى في الملف التالي
    return const Placeholder(fallbackHeight: 200);
  }

  Widget _buildUserTypeOptions(AuthFlowInProgress state) {
    // سيتم إضافة المحتوى في الملف التالي
    return const Placeholder(fallbackHeight: 200);
  }

  Widget _buildServiceTypeOptions(AuthFlowInProgress state) {
    // سيتم إضافة المحتوى في الملف التالي
    return const Placeholder(fallbackHeight: 200);
  }

  Widget _buildAuthForm(AuthFlowInProgress state) {
    // سيتم إضافة المحتوى في الملف التالي
    return const Placeholder(fallbackHeight: 300);
  }

  int _getTotalSteps(AuthFlowState state) {
    if (state is AuthFlowInProgress && state.selectedUserType == 'worker') {
      return 4; // جنس، نوع مستخدم، نوع خدمة، مصادقة
    }
    return 3; // جنس، نوع مستخدم، مصادقة
  }
}
