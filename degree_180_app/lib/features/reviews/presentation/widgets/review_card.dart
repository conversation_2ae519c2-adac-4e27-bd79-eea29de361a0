import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import 'star_rating_display.dart';
import 'review_images_grid.dart';

class ReviewCard extends StatelessWidget {
  final Map<String, dynamic> review;
  final VoidCallback? onHelpful;
  final VoidCallback? onNotHelpful;
  final VoidCallback? onReport;

  const ReviewCard({
    super.key,
    required this.review,
    this.onHelpful,
    this.onNotHelpful,
    this.onReport,
  });

  @override
  Widget build(BuildContext context) {
    final customerName = review['customerName'] as String;
    final rating = review['rating'] as double;
    final date = review['date'] as DateTime;
    final comment = review['comment'] as String?;
    final images = review['images'] as List<String>;
    final isAnonymous = review['isAnonymous'] as bool;
    final helpfulCount = review['helpfulCount'] as int;
    final notHelpfulCount = review['notHelpfulCount'] as int;
    final providerResponse = review['providerResponse'] as String?;
    final providerResponseDate = review['providerResponseDate'] as DateTime?;

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(color: AppColors.borderGray),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              // Avatar
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.primaryPurple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  isAnonymous ? Icons.person_outline : Icons.person,
                  color: AppColors.primaryPurple,
                  size: 20,
                ),
              ),
              
              const SizedBox(width: AppConstants.defaultPadding),
              
              // Name and Rating
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isAnonymous ? 'Anonymous' : customerName,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: AppColors.textDark,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        StarRatingDisplay(
                          rating: rating,
                          size: 16,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Text(
                          rating.toString(),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textGray,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Date and Menu
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _formatDate(date),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textGray,
                    ),
                  ),
                  IconButton(
                    onPressed: onReport,
                    icon: const Icon(
                      Icons.more_vert,
                      size: 16,
                      color: AppColors.textGray,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ],
          ),
          
          if (comment != null && comment.isNotEmpty) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              comment,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textDark,
                height: 1.4,
              ),
            ),
          ],
          
          // Images
          if (images.isNotEmpty) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            ReviewImagesGrid(images: images),
          ],
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Helpful buttons
          Row(
            children: [
              _buildHelpfulButton(
                icon: Icons.thumb_up_outlined,
                count: helpfulCount,
                onPressed: onHelpful,
                isPositive: true,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              _buildHelpfulButton(
                icon: Icons.thumb_down_outlined,
                count: notHelpfulCount,
                onPressed: onNotHelpful,
                isPositive: false,
              ),
              const Spacer(),
              if (helpfulCount + notHelpfulCount > 0)
                Text(
                  '${helpfulCount + notHelpfulCount} people found this helpful',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textGray,
                  ),
                ),
            ],
          ),
          
          // Provider Response
          if (providerResponse != null && providerResponse.isNotEmpty) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.backgroundGray.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.store,
                        size: 16,
                        color: AppColors.primaryPurple,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      Text(
                        'Response from provider',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.primaryPurple,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      if (providerResponseDate != null)
                        Text(
                          _formatDate(providerResponseDate),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textGray,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    providerResponse,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textDark,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHelpfulButton({
    required IconData icon,
    required int count,
    required VoidCallback? onPressed,
    required bool isPositive,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.smallPadding,
            vertical: 4,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 16,
                color: AppColors.textGray,
              ),
              if (count > 0) ...[
                const SizedBox(width: 4),
                Text(
                  count.toString(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textGray,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '${months}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return 'Just now';
    }
  }
}
