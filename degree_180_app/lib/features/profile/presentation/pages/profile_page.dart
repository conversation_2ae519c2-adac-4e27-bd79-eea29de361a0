import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/profile_menu_item.dart';
import '../widgets/profile_header.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Header
                const ProfileHeader(
                  name: 'Ahmed Hassan',
                  email: '<EMAIL>',
                  userType: 'Worker',
                  rating: 4.8,
                  reviewCount: 127,
                ),
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Menu Items
                Expanded(
                  child: Container(
                    decoration: const BoxDecoration(
                      color: AppColors.backgroundWhite,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(AppConstants.largeBorderRadius),
                        topRight: Radius.circular(AppConstants.largeBorderRadius),
                      ),
                    ),
                    child: ListView(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      children: [
                        const SizedBox(height: AppConstants.defaultPadding),
                        
                        // Account Section
                        _buildSectionTitle(AppStrings.profile),
                        ProfileMenuItem(
                          icon: Icons.person_outline,
                          title: AppStrings.editProfile,
                          onTap: () {
                            // TODO: Navigate to edit profile
                          },
                        ),
                        ProfileMenuItem(
                          icon: Icons.payment,
                          title: AppStrings.paymentMethod,
                          onTap: () {
                            // TODO: Navigate to payment methods
                          },
                        ),
                        ProfileMenuItem(
                          icon: Icons.notifications,
                          title: AppStrings.notification,
                          onTap: () {
                            // TODO: Navigate to notifications settings
                          },
                        ),
                        
                        const SizedBox(height: AppConstants.largePadding),
                        
                        // General Section
                        _buildSectionTitle(AppStrings.general),
                        ProfileMenuItem(
                          icon: Icons.fingerprint,
                          title: AppStrings.quickLogin,
                          trailing: Switch(
                            value: true,
                            onChanged: (value) {
                              // TODO: Handle quick login toggle
                            },
                            activeColor: AppColors.primaryPurple,
                          ),
                        ),
                        ProfileMenuItem(
                          icon: Icons.privacy_tip,
                          title: AppStrings.privacyPolicy,
                          onTap: () {
                            // TODO: Navigate to privacy policy
                          },
                        ),
                        ProfileMenuItem(
                          icon: Icons.star_outline,
                          title: AppStrings.rateApp,
                          onTap: () {
                            // TODO: Show rate app dialog
                          },
                        ),
                        ProfileMenuItem(
                          icon: Icons.lock_outline,
                          title: AppStrings.changePassword,
                          onTap: () {
                            // TODO: Navigate to change password
                          },
                        ),
                        ProfileMenuItem(
                          icon: Icons.location_on,
                          title: AppStrings.changeLocation,
                          onTap: () {
                            // TODO: Navigate to change location
                          },
                        ),
                        
                        const SizedBox(height: AppConstants.largePadding),
                        
                        // Logout Button
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(
                            horizontal: AppConstants.defaultPadding,
                          ),
                          child: ElevatedButton(
                            onPressed: _handleLogout,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.error,
                              foregroundColor: AppColors.textWhite,
                              padding: const EdgeInsets.symmetric(
                                vertical: AppConstants.defaultPadding,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                  AppConstants.defaultBorderRadius,
                                ),
                              ),
                            ),
                            child: const Text(
                              'Logout',
                              style: TextStyle(
                                fontSize: AppConstants.fontSizeRegular,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: AppConstants.largePadding),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppConstants.defaultPadding,
        bottom: AppConstants.smallPadding,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: AppColors.textDark,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement logout logic
              context.go('/');
            },
            child: const Text(
              'Logout',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
