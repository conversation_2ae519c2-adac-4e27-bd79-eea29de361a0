import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../widgets/search_filter_section.dart';
import '../widgets/price_range_slider.dart';
import '../widgets/distance_slider.dart';
import '../widgets/service_type_chips.dart';
import '../widgets/availability_filters.dart';

class AdvancedSearchPage extends StatefulWidget {
  const AdvancedSearchPage({super.key});

  @override
  State<AdvancedSearchPage> createState() => _AdvancedSearchPageState();
}

class _AdvancedSearchPageState extends State<AdvancedSearchPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final TextEditingController _searchController = TextEditingController();
  
  // Filter values
  String _selectedServiceType = 'all';
  RangeValues _priceRange = const RangeValues(0, 100);
  double _minRating = 0;
  double _maxDistance = 50;
  bool _isOpenNow = false;
  bool _hasAvailableSlots = false;
  String _sortBy = 'relevance';
  List<String> _selectedServices = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Advanced Search'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.primaryGradient,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _clearAllFilters,
            child: const Text(
              'Clear All',
              style: TextStyle(color: AppColors.textWhite),
            ),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Search Bar
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: TextField(
                controller: _searchController,
                style: const TextStyle(color: AppColors.textWhite),
                decoration: InputDecoration(
                  hintText: 'Search for services, providers...',
                  hintStyle: TextStyle(
                    color: AppColors.textWhite.withValues(alpha: 0.7),
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: AppColors.textWhite.withValues(alpha: 0.7),
                  ),
                  filled: true,
                  fillColor: AppColors.buttonWhite.withValues(alpha: 0.2),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),
            
            // Filters
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: AppColors.backgroundWhite,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.largeBorderRadius),
                    topRight: Radius.circular(AppConstants.largeBorderRadius),
                  ),
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Service Type
                      SearchFilterSection(
                        title: 'Service Type',
                        child: ServiceTypeChips(
                          selectedType: _selectedServiceType,
                          onTypeSelected: (type) {
                            setState(() {
                              _selectedServiceType = type;
                            });
                          },
                        ),
                      ),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Price Range
                      SearchFilterSection(
                        title: 'Price Range',
                        child: PriceRangeSlider(
                          values: _priceRange,
                          onChanged: (values) {
                            setState(() {
                              _priceRange = values;
                            });
                          },
                        ),
                      ),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Rating
                      SearchFilterSection(
                        title: 'Minimum Rating',
                        child: Column(
                          children: [
                            Slider(
                              value: _minRating,
                              min: 0,
                              max: 5,
                              divisions: 10,
                              activeColor: AppColors.primaryPurple,
                              onChanged: (value) {
                                setState(() {
                                  _minRating = value;
                                });
                              },
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('Any'),
                                Text('${_minRating.toStringAsFixed(1)} ⭐'),
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Distance
                      SearchFilterSection(
                        title: 'Maximum Distance',
                        child: DistanceSlider(
                          value: _maxDistance,
                          onChanged: (value) {
                            setState(() {
                              _maxDistance = value;
                            });
                          },
                        ),
                      ),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Availability
                      SearchFilterSection(
                        title: 'Availability',
                        child: AvailabilityFilters(
                          isOpenNow: _isOpenNow,
                          hasAvailableSlots: _hasAvailableSlots,
                          onOpenNowChanged: (value) {
                            setState(() {
                              _isOpenNow = value;
                            });
                          },
                          onAvailableSlotsChanged: (value) {
                            setState(() {
                              _hasAvailableSlots = value;
                            });
                          },
                        ),
                      ),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Sort By
                      SearchFilterSection(
                        title: 'Sort By',
                        child: Column(
                          children: [
                            _buildSortOption('Relevance', 'relevance'),
                            _buildSortOption('Rating', 'rating'),
                            _buildSortOption('Distance', 'distance'),
                            _buildSortOption('Price: Low to High', 'price_asc'),
                            _buildSortOption('Price: High to Low', 'price_desc'),
                            _buildSortOption('Newest', 'newest'),
                            _buildSortOption('Most Popular', 'popularity'),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: AppConstants.extraLargePadding),
                    ],
                  ),
                ),
              ),
            ),
            
            // Search Button
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.backgroundWhite,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight,
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _performSearch,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryPurple,
                    foregroundColor: AppColors.textWhite,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppConstants.defaultPadding,
                    ),
                  ),
                  child: Text(
                    'Search (${_getActiveFilterCount()} filters)',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(String title, String value) {
    return RadioListTile<String>(
      title: Text(title),
      value: value,
      groupValue: _sortBy,
      activeColor: AppColors.primaryPurple,
      onChanged: (value) {
        setState(() {
          _sortBy = value!;
        });
      },
    );
  }

  int _getActiveFilterCount() {
    int count = 0;
    if (_selectedServiceType != 'all') count++;
    if (_priceRange.start > 0 || _priceRange.end < 100) count++;
    if (_minRating > 0) count++;
    if (_maxDistance < 50) count++;
    if (_isOpenNow) count++;
    if (_hasAvailableSlots) count++;
    if (_sortBy != 'relevance') count++;
    return count;
  }

  void _clearAllFilters() {
    setState(() {
      _searchController.clear();
      _selectedServiceType = 'all';
      _priceRange = const RangeValues(0, 100);
      _minRating = 0;
      _maxDistance = 50;
      _isOpenNow = false;
      _hasAvailableSlots = false;
      _sortBy = 'relevance';
      _selectedServices.clear();
    });
  }

  void _performSearch() {
    // Create search filter object
    final searchFilter = {
      'query': _searchController.text,
      'serviceType': _selectedServiceType,
      'minPrice': _priceRange.start,
      'maxPrice': _priceRange.end,
      'minRating': _minRating,
      'maxDistance': _maxDistance,
      'isOpenNow': _isOpenNow,
      'hasAvailableSlots': _hasAvailableSlots,
      'sortBy': _sortBy,
      'selectedServices': _selectedServices,
    };

    // Navigate to search results
    Navigator.pushNamed(
      context,
      '/search-results',
      arguments: searchFilter,
    );
  }
}
