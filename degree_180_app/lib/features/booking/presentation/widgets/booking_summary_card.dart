import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import 'package:intl/intl.dart';

class BookingSummaryCard extends StatelessWidget {
  final String? serviceId;
  final DateTime selectedDate;
  final String? timeSlotId;
  final String providerId;

  const BookingSummaryCard({
    super.key,
    this.serviceId,
    required this.selectedDate,
    this.timeSlotId,
    required this.providerId,
  });

  @override
  Widget build(BuildContext context) {
    final serviceDetails = _getServiceDetails(serviceId);
    final timeDetails = _getTimeDetails(timeSlotId);
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Provider Info
          _buildSectionHeader('Service Provider'),
          _buildInfoRow(
            icon: Icons.person,
            label: 'Provider',
            value: 'Ahmed Hassan',
          ),
          _buildInfoRow(
            icon: Icons.location_on,
            label: 'Location',
            value: 'Kuwait City',
          ),
          _buildInfoRow(
            icon: Icons.star,
            label: 'Rating',
            value: '4.8 (127 reviews)',
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Service Info
          _buildSectionHeader('Service Details'),
          _buildInfoRow(
            icon: Icons.room_service,
            label: 'Service',
            value: serviceDetails['name'] ?? 'Not selected',
          ),
          _buildInfoRow(
            icon: Icons.access_time,
            label: 'Duration',
            value: serviceDetails['duration'] ?? '-',
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Date & Time Info
          _buildSectionHeader('Appointment Details'),
          _buildInfoRow(
            icon: Icons.calendar_today,
            label: 'Date',
            value: DateFormat('EEEE, MMM dd, yyyy').format(selectedDate),
          ),
          _buildInfoRow(
            icon: Icons.schedule,
            label: 'Time',
            value: timeDetails['time'] ?? 'Not selected',
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Pricing
          _buildSectionHeader('Pricing'),
          _buildPricingRow('Service Fee', serviceDetails['price'] ?? '0 KD'),
          _buildPricingRow('Tax', '1.5 KD'),
          const Divider(),
          _buildPricingRow(
            'Total',
            _calculateTotal(serviceDetails['price']),
            isTotal: true,
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Notes Section
          _buildSectionHeader('Additional Notes'),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppColors.backgroundGray.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
            ),
            child: Text(
              'Please arrive 5 minutes before your appointment time.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textGray,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: AppConstants.fontSizeLarge,
          fontWeight: FontWeight.w600,
          color: AppColors.textDark,
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppColors.textGray,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            '$label: ',
            style: const TextStyle(
              color: AppColors.textGray,
              fontSize: AppConstants.fontSizeMedium,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: AppColors.textDark,
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingRow(String label, String amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: AppColors.textDark,
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              color: isTotal ? AppColors.primaryPurple : AppColors.textDark,
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Map<String, String> _getServiceDetails(String? serviceId) {
    switch (serviceId) {
      case 'haircut':
        return {
          'name': 'Hair Cut',
          'duration': '30 min',
          'price': '15 KD',
        };
      case 'styling':
        return {
          'name': 'Hair Styling',
          'duration': '60 min',
          'price': '25 KD',
        };
      case 'beard':
        return {
          'name': 'Beard Trim',
          'duration': '20 min',
          'price': '10 KD',
        };
      default:
        return {
          'name': 'Service not selected',
          'duration': '-',
          'price': '0 KD',
        };
    }
  }

  Map<String, String> _getTimeDetails(String? timeSlotId) {
    if (timeSlotId == null) {
      return {'time': 'Time not selected'};
    }
    
    // Extract time from slot ID (mock implementation)
    final parts = timeSlotId.split('_');
    if (parts.length >= 3) {
      final hour = parts[1].padLeft(2, '0');
      final minute = parts[2].padLeft(2, '0');
      return {'time': '$hour:$minute'};
    }
    
    return {'time': 'Invalid time'};
  }

  String _calculateTotal(String? servicePrice) {
    if (servicePrice == null) return '1.5 KD';
    
    final price = double.tryParse(servicePrice.replaceAll(' KD', '')) ?? 0;
    final tax = 1.5;
    final total = price + tax;
    
    return '${total.toStringAsFixed(1)} KD';
  }
}
