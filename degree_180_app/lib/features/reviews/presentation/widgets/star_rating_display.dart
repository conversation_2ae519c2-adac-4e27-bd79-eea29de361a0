import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';

class StarRatingDisplay extends StatelessWidget {
  final double rating;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool showRating;

  const StarRatingDisplay({
    super.key,
    required this.rating,
    this.size = 20,
    this.activeColor,
    this.inactiveColor,
    this.showRating = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(5, (index) {
          return Icon(
            _getStarIcon(index + 1, rating),
            size: size,
            color: _getStarColor(index + 1, rating),
          );
        }),
        if (showRating) ...[
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: TextStyle(
              fontSize: size * 0.8,
              fontWeight: FontWeight.w500,
              color: activeColor ?? AppColors.starYellow,
            ),
          ),
        ],
      ],
    );
  }

  IconData _getStarIcon(int position, double rating) {
    if (rating >= position) {
      return Icons.star;
    } else if (rating >= position - 0.5) {
      return Icons.star_half;
    } else {
      return Icons.star_border;
    }
  }

  Color _getStarColor(int position, double rating) {
    if (rating >= position - 0.5) {
      return activeColor ?? AppColors.starYellow;
    } else {
      return inactiveColor ?? AppColors.textGray.withValues(alpha: 0.3);
    }
  }
}

class StarRatingInput extends StatefulWidget {
  final double initialRating;
  final Function(double) onRatingChanged;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;

  const StarRatingInput({
    super.key,
    this.initialRating = 0,
    required this.onRatingChanged,
    this.size = 32,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  State<StarRatingInput> createState() => _StarRatingInputState();
}

class _StarRatingInputState extends State<StarRatingInput> {
  late double _rating;

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _rating = index + 1.0;
            });
            widget.onRatingChanged(_rating);
          },
          child: Icon(
            _rating > index ? Icons.star : Icons.star_border,
            size: widget.size,
            color: _rating > index
                ? (widget.activeColor ?? AppColors.starYellow)
                : (widget.inactiveColor ?? AppColors.textGray.withValues(alpha: 0.3)),
          ),
        );
      }),
    );
  }
}
