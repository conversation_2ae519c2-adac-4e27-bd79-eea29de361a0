import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';

class EnhancedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isEnabled;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final double? width;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isEnabled = true,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.width,
    this.padding,
    this.borderRadius,
  });

  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildButton(),
        );
      },
    );
  }

  Widget _buildButton() {
    final isDisabled = !widget.isEnabled || widget.isLoading || widget.onPressed == null;
    
    switch (widget.type) {
      case ButtonType.primary:
        return _buildElevatedButton(isDisabled);
      case ButtonType.secondary:
        return _buildOutlinedButton(isDisabled);
      case ButtonType.text:
        return _buildTextButton(isDisabled);
      case ButtonType.icon:
        return _buildIconButton(isDisabled);
    }
  }

  Widget _buildElevatedButton(bool isDisabled) {
    return SizedBox(
      width: widget.width,
      child: ElevatedButton(
        onPressed: isDisabled ? null : _handlePress,
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.backgroundColor ?? AppColors.primaryPurple,
          foregroundColor: widget.textColor ?? AppColors.textWhite,
          disabledBackgroundColor: AppColors.borderGray,
          disabledForegroundColor: AppColors.textLight,
          elevation: isDisabled ? 0 : 2,
          shadowColor: Colors.black.withValues(alpha: 0.2),
          shape: RoundedRectangleBorder(
            borderRadius: widget.borderRadius ?? 
                BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          padding: widget.padding ?? _getPadding(),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildOutlinedButton(bool isDisabled) {
    return SizedBox(
      width: widget.width,
      child: OutlinedButton(
        onPressed: isDisabled ? null : _handlePress,
        style: OutlinedButton.styleFrom(
          foregroundColor: widget.textColor ?? AppColors.primaryPurple,
          disabledForegroundColor: AppColors.textLight,
          side: BorderSide(
            color: isDisabled 
                ? AppColors.borderGray 
                : (widget.borderColor ?? AppColors.primaryPurple),
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: widget.borderRadius ?? 
                BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          padding: widget.padding ?? _getPadding(),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildTextButton(bool isDisabled) {
    return SizedBox(
      width: widget.width,
      child: TextButton(
        onPressed: isDisabled ? null : _handlePress,
        style: TextButton.styleFrom(
          foregroundColor: widget.textColor ?? AppColors.primaryPurple,
          disabledForegroundColor: AppColors.textLight,
          shape: RoundedRectangleBorder(
            borderRadius: widget.borderRadius ?? 
                BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          padding: widget.padding ?? _getPadding(),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildIconButton(bool isDisabled) {
    return IconButton(
      onPressed: isDisabled ? null : _handlePress,
      icon: widget.isLoading 
          ? SizedBox(
              width: _getIconSize(),
              height: _getIconSize(),
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  widget.textColor ?? AppColors.primaryPurple,
                ),
              ),
            )
          : Icon(
              widget.icon,
              size: _getIconSize(),
              color: isDisabled 
                  ? AppColors.textLight 
                  : (widget.textColor ?? AppColors.primaryPurple),
            ),
      style: IconButton.styleFrom(
        backgroundColor: widget.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: widget.borderRadius ?? 
              BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (widget.isLoading) {
      return SizedBox(
        height: _getTextSize(),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: _getTextSize(),
              height: _getTextSize(),
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  widget.textColor ?? AppColors.textWhite,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Loading...',
              style: TextStyle(
                fontSize: _getTextSize(),
                fontWeight: FontWeight.w600,
                fontFamily: 'Inter',
              ),
            ),
          ],
        ),
      );
    }

    if (widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.icon,
            size: _getIconSize(),
          ),
          const SizedBox(width: 8),
          Text(
            widget.text,
            style: TextStyle(
              fontSize: _getTextSize(),
              fontWeight: FontWeight.w600,
              fontFamily: 'Inter',
            ),
          ),
        ],
      );
    }

    return Text(
      widget.text,
      style: TextStyle(
        fontSize: _getTextSize(),
        fontWeight: FontWeight.w600,
        fontFamily: 'Inter',
      ),
    );
  }

  EdgeInsets _getPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    }
  }

  double _getTextSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 12;
      case ButtonSize.medium:
        return 14;
      case ButtonSize.large:
        return 16;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 18;
      case ButtonSize.large:
        return 20;
    }
  }

  void _handlePress() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    widget.onPressed?.call();
  }
}

enum ButtonType {
  primary,
  secondary,
  text,
  icon,
}

enum ButtonSize {
  small,
  medium,
  large,
}
