import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/theme_indicator.dart';
import '../../../../core/theme/theme_manager.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/enums/service_type.dart';
import '../../../services/presentation/bloc/service_provider_bloc.dart';
import '../widgets/service_provider_card.dart';
import '../widgets/service_category_chip.dart';

class CustomerHomePage extends StatefulWidget {
  const CustomerHomePage({super.key});

  @override
  State<CustomerHomePage> createState() => _CustomerHomePageState();
}

class _CustomerHomePageState extends State<CustomerHomePage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String _selectedCategory = 'all';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    // Load service providers from Firebase
    context.read<ServiceProviderBloc>().add(LoadServiceProvidersEvent());
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Header
                _buildHeader(),
                
                // Search Bar
                _buildSearchBar(),
                
                // Category Filters
                _buildCategoryFilters(),
                
                // Service Providers List
                Expanded(
                  child: _buildServiceProvidersList(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          // Profile Avatar
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppColors.buttonWhite,
              borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
            ),
            child: const Icon(
              Icons.person,
              color: AppColors.primaryPurple,
              size: 24,
            ),
          ),
          
          const SizedBox(width: AppConstants.defaultPadding),
          
          // Welcome Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back!',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textWhite.withValues(alpha: 0.8),
                  ),
                ),
                Text(
                  'Sara Ahmed',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.textWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          
          // Theme Indicator and Icons
          Row(
            children: [
              // Theme Indicator
              ThemeIndicator(
                showLabel: false,
                size: 32,
                onTap: () {
                  // Navigate to theme settings
                  Navigator.pushNamed(context, '/theme-settings');
                },
              ),

              const SizedBox(width: 8),

              IconButton(
                onPressed: () {},
                icon: const Icon(
                  Icons.card_giftcard_outlined,
                  color: AppColors.textWhite,
                  size: 24,
                ),
              ),
              IconButton(
                onPressed: () {},
                icon: const Icon(
                  Icons.people_outline,
                  color: AppColors.textWhite,
                  size: 24,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.buttonWhite,
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        child: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search for services...',
            prefixIcon: const Icon(
              Icons.search,
              color: AppColors.textGray,
            ),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
              vertical: AppConstants.defaultPadding,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryFilters() {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(vertical: AppConstants.defaultPadding),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
        children: [
          ServiceCategoryChip(
            label: 'All',
            isSelected: _selectedCategory == 'all',
            onTap: () => setState(() => _selectedCategory = 'all'),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          ServiceCategoryChip(
            label: AppStrings.salon,
            isSelected: _selectedCategory == 'salon',
            onTap: () => setState(() => _selectedCategory = 'salon'),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          ServiceCategoryChip(
            label: AppStrings.barbershop,
            isSelected: _selectedCategory == 'barbershop',
            onTap: () => setState(() => _selectedCategory = 'barbershop'),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          ServiceCategoryChip(
            label: AppStrings.spa,
            isSelected: _selectedCategory == 'spa',
            onTap: () => setState(() => _selectedCategory = 'spa'),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceProvidersList() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.largeBorderRadius),
          topRight: Radius.circular(AppConstants.largeBorderRadius),
        ),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: _getFilteredProviders().length,
        itemBuilder: (context, index) {
          final provider = _getFilteredProviders()[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: ServiceProviderCard(
              name: provider['name'],
              serviceType: provider['serviceType'],
              rating: provider['rating'],
              reviewCount: provider['reviewCount'],
              imageUrl: provider['imageUrl'],
              isAvailable: provider['isAvailable'],
              onTap: () {
                // TODO: Navigate to provider details
              },
            ),
          );
        },
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredProviders() {
    final allProviders = [
      {
        'name': 'Ahmed Hassan',
        'serviceType': 'barbershop',
        'rating': 4.8,
        'reviewCount': 127,
        'imageUrl': null,
        'isAvailable': true,
      },
      {
        'name': 'Fatima Al-Zahra',
        'serviceType': 'salon',
        'rating': 4.9,
        'reviewCount': 89,
        'imageUrl': null,
        'isAvailable': true,
      },
      {
        'name': 'Omar Khalil',
        'serviceType': 'spa',
        'rating': 4.7,
        'reviewCount': 156,
        'imageUrl': null,
        'isAvailable': false,
      },
      {
        'name': 'Layla Mohammed',
        'serviceType': 'salon',
        'rating': 4.6,
        'reviewCount': 203,
        'imageUrl': null,
        'isAvailable': true,
      },
      {
        'name': 'Khalid Al-Ahmad',
        'serviceType': 'barbershop',
        'rating': 4.9,
        'reviewCount': 78,
        'imageUrl': null,
        'isAvailable': true,
      },
    ];

    if (_selectedCategory == 'all') {
      return allProviders;
    }

    return allProviders
        .where((provider) => provider['serviceType'] == _selectedCategory)
        .toList();
  }
}
