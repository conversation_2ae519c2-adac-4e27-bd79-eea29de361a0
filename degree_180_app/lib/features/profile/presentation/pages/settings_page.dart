import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/widgets/theme_indicator.dart';
import '../../../../core/services/cache_service.dart';
import '../../../../core/services/connectivity_service.dart';
import '../../../../core/services/error_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/services/theme_service.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_tile.dart';
import '../widgets/settings_switch_tile.dart';
import 'theme_settings_page.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final CacheService _cacheService = CacheService();
  final ConnectivityService _connectivityService = ConnectivityService();
  final ErrorService _errorService = ErrorService();
  final AnalyticsService _analyticsService = AnalyticsService();
  final ThemeService _themeService = ThemeService();

  // Settings values
  bool _notificationsEnabled = true;
  bool _pushNotificationsEnabled = true;
  bool _emailNotificationsEnabled = false;
  bool _smsNotificationsEnabled = true;
  bool _locationServicesEnabled = true;
  bool _analyticsEnabled = true;
  bool _crashReportingEnabled = true;
  bool _biometricAuthEnabled = false;
  bool _autoBackupEnabled = true;
  bool _dataSaverMode = false;
  bool _highQualityImages = true;
  String _selectedLanguage = 'العربية';
  String _selectedTheme = 'System';
  String _selectedCurrency = 'KWD';

  int _cacheSize = 0;
  String _appVersion = '1.0.0';
  String _buildNumber = '1';

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadCacheSize();
    _trackScreenView();
  }

  void _trackScreenView() {
    _analyticsService.logScreenView('Settings', 'SettingsPage');
  }

  Future<void> _loadSettings() async {
    // Load settings from cache
    _notificationsEnabled = await _cacheService.getSetting<bool>('notifications_enabled') ?? true;
    _pushNotificationsEnabled = await _cacheService.getSetting<bool>('push_notifications_enabled') ?? true;
    _emailNotificationsEnabled = await _cacheService.getSetting<bool>('email_notifications_enabled') ?? false;
    _smsNotificationsEnabled = await _cacheService.getSetting<bool>('sms_notifications_enabled') ?? true;
    _locationServicesEnabled = await _cacheService.getSetting<bool>('location_services_enabled') ?? true;
    _analyticsEnabled = await _cacheService.getSetting<bool>('analytics_enabled') ?? true;
    _crashReportingEnabled = await _cacheService.getSetting<bool>('crash_reporting_enabled') ?? true;
    _biometricAuthEnabled = await _cacheService.getSetting<bool>('biometric_auth_enabled') ?? false;
    _autoBackupEnabled = await _cacheService.getSetting<bool>('auto_backup_enabled') ?? true;
    _dataSaverMode = await _cacheService.getSetting<bool>('data_saver_mode') ?? false;
    _highQualityImages = await _cacheService.getSetting<bool>('high_quality_images') ?? true;
    _selectedLanguage = await _cacheService.getSetting<String>('selected_language') ?? 'العربية';
    _selectedTheme = await _cacheService.getSetting<String>('selected_theme') ?? 'System';
    _selectedCurrency = await _cacheService.getSetting<String>('selected_currency') ?? 'KWD';

    setState(() {});
  }

  Future<void> _loadCacheSize() async {
    final size = await _cacheService.getCacheSize();
    setState(() {
      _cacheSize = size;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // Notifications Section
            SettingsSection(
              title: 'Notifications',
              icon: Icons.notifications,
              children: [
                SettingsSwitchTile(
                  title: 'Enable Notifications',
                  subtitle: 'Receive app notifications',
                  value: _notificationsEnabled,
                  onChanged: (value) => _updateSetting('notifications_enabled', value, () {
                    setState(() => _notificationsEnabled = value);
                  }),
                ),
                if (_notificationsEnabled) ...[
                  SettingsSwitchTile(
                    title: 'Push Notifications',
                    subtitle: 'Booking updates and reminders',
                    value: _pushNotificationsEnabled,
                    onChanged: (value) => _updateSetting('push_notifications_enabled', value, () {
                      setState(() => _pushNotificationsEnabled = value);
                    }),
                  ),
                  SettingsSwitchTile(
                    title: 'Email Notifications',
                    subtitle: 'Receive notifications via email',
                    value: _emailNotificationsEnabled,
                    onChanged: (value) => _updateSetting('email_notifications_enabled', value, () {
                      setState(() => _emailNotificationsEnabled = value);
                    }),
                  ),
                  SettingsSwitchTile(
                    title: 'SMS Notifications',
                    subtitle: 'Receive notifications via SMS',
                    value: _smsNotificationsEnabled,
                    onChanged: (value) => _updateSetting('sms_notifications_enabled', value, () {
                      setState(() => _smsNotificationsEnabled = value);
                    }),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 20),

            // Theme & Appearance Section
            SettingsSection(
              title: 'المظهر والثيم',
              icon: Icons.palette,
              children: [
                // Current Theme Display
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: AppColors.backgroundGray,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      ThemeIndicator(
                        showLabel: true,
                        size: 40,
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ThemeSettingsPage(),
                            ),
                          );
                        },
                        child: const Text('تخصيص'),
                      ),
                    ],
                  ),
                ),

                // Quick Theme Switcher
                ThemeQuickSwitcher(),

                SettingsTile(
                  title: 'إعدادات المظهر المتقدمة',
                  subtitle: 'تخصيص كامل للألوان والثيم',
                  icon: Icons.tune,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ThemeSettingsPage(),
                      ),
                    );
                  },
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Privacy & Security Section
            SettingsSection(
              title: 'Privacy & Security',
              icon: Icons.security,
              children: [
                SettingsSwitchTile(
                  title: 'Location Services',
                  subtitle: 'Allow app to access your location',
                  value: _locationServicesEnabled,
                  onChanged: (value) => _updateSetting('location_services_enabled', value, () {
                    setState(() => _locationServicesEnabled = value);
                  }),
                ),
                SettingsSwitchTile(
                  title: 'Biometric Authentication',
                  subtitle: 'Use fingerprint or face ID',
                  value: _biometricAuthEnabled,
                  onChanged: (value) => _updateSetting('biometric_auth_enabled', value, () {
                    setState(() => _biometricAuthEnabled = value);
                  }),
                ),
                SettingsTile(
                  title: 'Privacy Policy',
                  subtitle: 'Read our privacy policy',
                  icon: Icons.privacy_tip,
                  onTap: () => _openPrivacyPolicy(),
                ),
                SettingsTile(
                  title: 'Terms of Service',
                  subtitle: 'Read our terms of service',
                  icon: Icons.description,
                  onTap: () => _openTermsOfService(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // App Preferences Section
            SettingsSection(
              title: 'App Preferences',
              icon: Icons.tune,
              children: [
                SettingsTile(
                  title: 'Language',
                  subtitle: _selectedLanguage,
                  icon: Icons.language,
                  onTap: () => _showLanguageSelector(),
                ),
                SettingsTile(
                  title: 'Theme',
                  subtitle: _selectedTheme,
                  icon: Icons.palette,
                  onTap: () => _showThemeSelector(),
                ),
                SettingsTile(
                  title: 'Currency',
                  subtitle: _selectedCurrency,
                  icon: Icons.attach_money,
                  onTap: () => _showCurrencySelector(),
                ),
                SettingsSwitchTile(
                  title: 'Data Saver Mode',
                  subtitle: 'Reduce data usage',
                  value: _dataSaverMode,
                  onChanged: (value) => _updateSetting('data_saver_mode', value, () {
                    setState(() => _dataSaverMode = value);
                  }),
                ),
                SettingsSwitchTile(
                  title: 'High Quality Images',
                  subtitle: 'Download high resolution images',
                  value: _highQualityImages,
                  onChanged: (value) => _updateSetting('high_quality_images', value, () {
                    setState(() => _highQualityImages = value);
                  }),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Data & Storage Section
            SettingsSection(
              title: 'Data & Storage',
              icon: Icons.storage,
              children: [
                SettingsSwitchTile(
                  title: 'Auto Backup',
                  subtitle: 'Automatically backup your data',
                  value: _autoBackupEnabled,
                  onChanged: (value) => _updateSetting('auto_backup_enabled', value, () {
                    setState(() => _autoBackupEnabled = value);
                  }),
                ),
                SettingsTile(
                  title: 'Clear Cache',
                  subtitle: 'Cache size: ${_formatBytes(_cacheSize)}',
                  icon: Icons.cleaning_services,
                  onTap: () => _showClearCacheDialog(),
                ),
                SettingsTile(
                  title: 'Export Data',
                  subtitle: 'Export your personal data',
                  icon: Icons.download,
                  onTap: () => _exportData(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Analytics & Diagnostics Section
            SettingsSection(
              title: 'Analytics & Diagnostics',
              icon: Icons.analytics,
              children: [
                SettingsSwitchTile(
                  title: 'Analytics',
                  subtitle: 'Help improve the app',
                  value: _analyticsEnabled,
                  onChanged: (value) => _updateSetting('analytics_enabled', value, () {
                    setState(() => _analyticsEnabled = value);
                  }),
                ),
                SettingsSwitchTile(
                  title: 'Crash Reporting',
                  subtitle: 'Send crash reports to developers',
                  value: _crashReportingEnabled,
                  onChanged: (value) => _updateSetting('crash_reporting_enabled', value, () {
                    setState(() => _crashReportingEnabled = value);
                  }),
                ),
                SettingsTile(
                  title: 'Connection Status',
                  subtitle: _getConnectionStatusText(),
                  icon: Icons.wifi,
                  onTap: () => _showConnectionDetails(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // About Section
            SettingsSection(
              title: 'About',
              icon: Icons.info,
              children: [
                SettingsTile(
                  title: 'App Version',
                  subtitle: '$_appVersion ($_buildNumber)',
                  icon: Icons.info_outline,
                  onTap: () => _showVersionInfo(),
                ),
                SettingsTile(
                  title: 'Contact Support',
                  subtitle: 'Get help and support',
                  icon: Icons.support_agent,
                  onTap: () => _contactSupport(),
                ),
                SettingsTile(
                  title: 'Rate App',
                  subtitle: 'Rate us on the app store',
                  icon: Icons.star,
                  onTap: () => _rateApp(),
                ),
                SettingsTile(
                  title: 'Share App',
                  subtitle: 'Share with friends',
                  icon: Icons.share,
                  onTap: () => _shareApp(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Danger Zone
            SettingsSection(
              title: 'Danger Zone',
              icon: Icons.warning,
              children: [
                SettingsTile(
                  title: 'Reset Settings',
                  subtitle: 'Reset all settings to default',
                  icon: Icons.restore,
                  textColor: AppColors.warning,
                  onTap: () => _showResetSettingsDialog(),
                ),
                SettingsTile(
                  title: 'Delete Account',
                  subtitle: 'Permanently delete your account',
                  icon: Icons.delete_forever,
                  textColor: AppColors.error,
                  onTap: () => _showDeleteAccountDialog(),
                ),
              ],
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Future<void> _updateSetting(String key, dynamic value, VoidCallback updateUI) async {
    await _cacheService.saveSetting(key, value);
    updateUI();
    
    // Track setting change
    _analyticsService.logCustomEvent('setting_changed', {
      'setting_key': key,
      'setting_value': value.toString(),
    });
  }

  String _getConnectionStatusText() {
    final status = _connectivityService.currentStatus;
    if (!status.isConnected) return 'Disconnected';
    return '${status.type.name.toUpperCase()} - ${status.quality.name}';
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  void _showLanguageSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _LanguageSelectorSheet(
        selectedLanguage: _selectedLanguage,
        onLanguageSelected: (language) {
          _updateSetting('selected_language', language, () {
            setState(() => _selectedLanguage = language);
          });
        },
      ),
    );
  }

  void _showThemeSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _ThemeSelectorSheet(
        selectedTheme: _selectedTheme,
        onThemeSelected: (theme) {
          _updateSetting('selected_theme', theme, () {
            setState(() => _selectedTheme = theme);
          });
        },
      ),
    );
  }

  void _showCurrencySelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _CurrencySelectorSheet(
        selectedCurrency: _selectedCurrency,
        onCurrencySelected: (currency) {
          _updateSetting('selected_currency', currency, () {
            setState(() => _selectedCurrency = currency);
          });
        },
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: Text('This will clear ${_formatBytes(_cacheSize)} of cached data. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _clearCache();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  Future<void> _clearCache() async {
    try {
      await _cacheService.clearAllCache();
      await _loadCacheSize();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cache cleared successfully'),
          backgroundColor: AppColors.success,
        ),
      );
      
      _analyticsService.logCustomEvent('cache_cleared', {
        'cache_size_before': _cacheSize,
      });
    } catch (e) {
      _errorService.reportError(e, context: {'action': 'clear_cache'});
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to clear cache'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _showConnectionDetails() {
    final status = _connectivityService.currentStatus;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Connection Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${status.type.name.toUpperCase()}'),
            Text('Status: ${status.isConnected ? 'Connected' : 'Disconnected'}'),
            Text('Quality: ${status.quality.name}'),
            if (status.speed != null) Text('Speed: ${status.speed} Mbps'),
            Text('Latency: ${status.latency} ms'),
            Text('Last Updated: ${status.timestamp.toString()}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showVersionInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Version Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('App Version: $_appVersion'),
            Text('Build Number: $_buildNumber'),
            Text('Platform: ${Theme.of(context).platform.name}'),
            const Text('Framework: Flutter'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showResetSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('This will reset all settings to their default values. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _resetSettings();
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.warning),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text('This will permanently delete your account and all associated data. This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement account deletion
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetSettings() async {
    // TODO: Implement settings reset
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings reset successfully'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _openPrivacyPolicy() {
    // TODO: Open privacy policy
  }

  void _openTermsOfService() {
    // TODO: Open terms of service
  }

  void _exportData() {
    // TODO: Implement data export
  }

  void _contactSupport() {
    // TODO: Open support contact
  }

  void _rateApp() {
    // TODO: Open app store rating
  }

  void _shareApp() {
    // TODO: Share app
  }
}

// Language Selector Sheet
class _LanguageSelectorSheet extends StatelessWidget {
  final String selectedLanguage;
  final Function(String) onLanguageSelected;

  const _LanguageSelectorSheet({
    required this.selectedLanguage,
    required this.onLanguageSelected,
  });

  @override
  Widget build(BuildContext context) {
    final languages = ['العربية', 'English', 'Français'];
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Select Language', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 20),
          ...languages.map((language) => ListTile(
            title: Text(language),
            trailing: selectedLanguage == language ? const Icon(Icons.check, color: AppColors.primaryPurple) : null,
            onTap: () {
              onLanguageSelected(language);
              Navigator.pop(context);
            },
          )),
        ],
      ),
    );
  }
}

// Theme Selector Sheet
class _ThemeSelectorSheet extends StatelessWidget {
  final String selectedTheme;
  final Function(String) onThemeSelected;

  const _ThemeSelectorSheet({
    required this.selectedTheme,
    required this.onThemeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final themes = ['Light', 'Dark', 'System'];
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Select Theme', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 20),
          ...themes.map((theme) => ListTile(
            title: Text(theme),
            trailing: selectedTheme == theme ? const Icon(Icons.check, color: AppColors.primaryPurple) : null,
            onTap: () {
              onThemeSelected(theme);
              Navigator.pop(context);
            },
          )),
        ],
      ),
    );
  }
}

// Currency Selector Sheet
class _CurrencySelectorSheet extends StatelessWidget {
  final String selectedCurrency;
  final Function(String) onCurrencySelected;

  const _CurrencySelectorSheet({
    required this.selectedCurrency,
    required this.onCurrencySelected,
  });

  @override
  Widget build(BuildContext context) {
    final currencies = ['KWD', 'USD', 'EUR', 'SAR', 'AED'];
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Select Currency', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 20),
          ...currencies.map((currency) => ListTile(
            title: Text(currency),
            trailing: selectedCurrency == currency ? const Icon(Icons.check, color: AppColors.primaryPurple) : null,
            onTap: () {
              onCurrencySelected(currency);
              Navigator.pop(context);
            },
          )),
        ],
      ),
    );
  }
}
