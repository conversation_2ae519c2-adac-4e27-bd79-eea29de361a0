import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_loading_widget.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/service_model.dart';
import '../../../../core/enums/booking_status.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../bloc/booking_bloc.dart';
import '../widgets/booking_card.dart';
import '../widgets/booking_filter_tabs.dart';

class MyBookingsPage extends StatefulWidget {
  const MyBookingsPage({super.key});

  @override
  State<MyBookingsPage> createState() => _MyBookingsPageState();
}

class _MyBookingsPageState extends State<MyBookingsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  BookingStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadBookings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadBookings() {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      // Load user bookings
      // context.read<BookingBloc>().add(
      //   LoadUserBookingsEvent(
      //     userId: authState.user.uid,
      //     status: _selectedStatus,
      //   ),
      // );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Bookings'),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            onPressed: () => _showFilterOptions(),
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: Column(
        children: [
          // Status Tabs
          Container(
            color: AppColors.backgroundWhite,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.primaryPurple,
              unselectedLabelColor: AppColors.textLight,
              indicatorColor: AppColors.primaryPurple,
              onTap: (index) {
                setState(() {
                  switch (index) {
                    case 0:
                      _selectedStatus = null; // All
                      break;
                    case 1:
                      _selectedStatus = BookingStatus.pending;
                      break;
                    case 2:
                      _selectedStatus = BookingStatus.confirmed;
                      break;
                    case 3:
                      _selectedStatus = BookingStatus.completed;
                      break;
                  }
                });
                _loadBookings();
              },
              tabs: const [
                Tab(text: 'All'),
                Tab(text: 'Pending'),
                Tab(text: 'Confirmed'),
                Tab(text: 'Completed'),
              ],
            ),
          ),

          // Bookings List
          Expanded(
            child: BlocBuilder<BookingBloc, BookingState>(
              builder: (context, state) {
                if (state is BookingTimeSlotsLoading) {
                  return const EnhancedLoadingWidget(
                    message: 'Loading your bookings...',
                  );
                }

                // For demo purposes, show mock bookings
                return _buildMockBookingsList();
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToBooking(),
        backgroundColor: AppColors.primaryPurple,
        child: const Icon(
          Icons.add,
          color: AppColors.textWhite,
        ),
      ),
    );
  }

  Widget _buildMockBookingsList() {
    final mockBookings = _getMockBookings();
    
    if (mockBookings.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async => _loadBookings(),
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: mockBookings.length,
        itemBuilder: (context, index) {
          final booking = mockBookings[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
            child: BookingCard(
              booking: booking,
              onTap: () => _navigateToBookingDetails(booking.id),
              onCancel: () => _cancelBooking(booking.id),
              onReschedule: () => _rescheduleBooking(booking.id),
              onReview: () => _writeReview(booking.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today_outlined,
              size: 64,
              color: AppColors.textLight.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No bookings found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'You haven\'t made any bookings yet.\nStart by booking your first appointment!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _navigateToBooking(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryPurple,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Book Now',
                style: TextStyle(
                  color: AppColors.textWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<BookingModel> _getMockBookings() {
    // Return filtered mock bookings based on selected status
    final allBookings = [
      BookingModel(
        id: '1',
        customerId: 'user1',
        customerName: 'John Doe',
        customerPhone: '+965 1234 5678',
        customerEmail: '<EMAIL>',
        providerId: 'provider1',
        providerName: 'Glamour Salon',
        serviceId: 'service1',
        serviceName: 'قص شعر كلاسيكي',
        service: ServiceModel(
          id: 'service1',
          name: 'قص شعر كلاسيكي',
          description: 'قص شعر احترافي',
          price: 25.0,
          durationMinutes: 60,
          category: 'hair',
          images: const [],
          tags: const [],
          isActive: true,
          isPopular: false,
          bookingCount: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        bookingDate: DateTime.now().add(const Duration(days: 1)),
        appointmentDateTime: DateTime.now().add(const Duration(days: 1, hours: 14)),
        timeSlot: '2:00 PM - 3:00 PM',
        status: BookingStatus.confirmed,
        totalAmount: 25.0,
        currency: 'KWD',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      BookingModel(
        id: '2',
        customerId: 'user1',
        customerName: 'John Doe',
        customerPhone: '+965 1234 5678',
        customerEmail: '<EMAIL>',
        providerId: 'provider2',
        providerName: 'Elite Barbershop',
        serviceId: 'service2',
        serviceName: 'حلاقة كاملة',
        service: ServiceModel(
          id: 'service2',
          name: 'حلاقة كاملة',
          description: 'حلاقة احترافية',
          price: 15.0,
          durationMinutes: 45,
          category: 'barbershop',
          images: const [],
          tags: const [],
          isActive: true,
          isPopular: false,
          bookingCount: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        bookingDate: DateTime.now().subtract(const Duration(days: 7)),
        appointmentDateTime: DateTime.now().subtract(const Duration(days: 7, hours: -10)),
        timeSlot: '10:00 AM - 11:00 AM',
        status: BookingStatus.completed,
        totalAmount: 15.0,
        currency: 'KWD',
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        updatedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
      BookingModel(
        id: '3',
        customerId: 'user1',
        customerName: 'John Doe',
        customerPhone: '+965 1234 5678',
        customerEmail: '<EMAIL>',
        providerId: 'provider3',
        providerName: 'Relax Spa',
        serviceId: 'service3',
        serviceName: 'مساج استرخاء',
        service: ServiceModel(
          id: 'service3',
          name: 'مساج استرخاء',
          description: 'جلسة مساج مريحة',
          price: 40.0,
          durationMinutes: 90,
          category: 'spa',
          images: const [],
          tags: const [],
          isActive: true,
          isPopular: false,
          bookingCount: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        bookingDate: DateTime.now().add(const Duration(days: 3)),
        appointmentDateTime: DateTime.now().add(const Duration(days: 3, hours: 16)),
        timeSlot: '4:00 PM - 5:30 PM',
        status: BookingStatus.pending,
        totalAmount: 45.0,
        currency: 'KWD',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    if (_selectedStatus == null) {
      return allBookings;
    }

    return allBookings.where((booking) => booking.status == _selectedStatus).toList();
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: AppColors.backgroundWhite,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.borderGray,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filter Bookings',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Add filter options here
                  ListTile(
                    leading: const Icon(Icons.date_range),
                    title: const Text('Date Range'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showDateRangePicker(),
                  ),
                  ListTile(
                    leading: const Icon(Icons.sort),
                    title: const Text('Sort By'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showSortOptions(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDateRangePicker() {
    Navigator.pop(context);
    // Implement date range picker
  }

  void _showSortOptions() {
    Navigator.pop(context);
    // Implement sort options
  }

  void _navigateToBooking() {
    Navigator.pushNamed(context, '/booking');
  }

  void _navigateToBookingDetails(String bookingId) {
    Navigator.pushNamed(
      context,
      '/booking-details',
      arguments: {'bookingId': bookingId},
    );
  }

  void _cancelBooking(String bookingId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: const Text('Are you sure you want to cancel this booking?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement cancel booking
            },
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  void _rescheduleBooking(String bookingId) {
    Navigator.pushNamed(
      context,
      '/reschedule-booking',
      arguments: {'bookingId': bookingId},
    );
  }

  void _writeReview(String bookingId) {
    Navigator.pushNamed(
      context,
      '/write-review',
      arguments: {'bookingId': bookingId},
    );
  }
}
