part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class SignInWithEmailEvent extends AuthEvent {
  final String email;
  final String password;

  const SignInWithEmailEvent({
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [email, password];
}

class SignUpWithEmailEvent extends AuthEvent {
  final String email;
  final String password;
  final String name;
  final UserType userType;
  final ServiceType? serviceType;

  const SignUpWithEmailEvent({
    required this.email,
    required this.password,
    required this.name,
    required this.userType,
    this.serviceType,
  });

  @override
  List<Object> get props => [
    email,
    password,
    name,
    userType,
    if (serviceType != null) serviceType!
  ];
}

class SignInWithGoogleEvent extends AuthEvent {
  const SignInWithGoogleEvent();
}

class SignInWithAppleEvent extends AuthEvent {
  const SignInWithAppleEvent();
}

class SignOutEvent extends AuthEvent {
  const SignOutEvent();
}

class CheckAuthStatusEvent extends AuthEvent {
  const CheckAuthStatusEvent();
}

class SendPasswordResetEvent extends AuthEvent {
  final String email;

  const SendPasswordResetEvent({required this.email});

  @override
  List<Object> get props => [email];
}

class UpdateProfileEvent extends AuthEvent {
  final String? name;
  final String? phoneNumber;
  final String? profileImageUrl;

  const UpdateProfileEvent({
    this.name,
    this.phoneNumber,
    this.profileImageUrl,
  });

  @override
  List<Object> get props => [
    if (name != null) name!,
    if (phoneNumber != null) phoneNumber!,
    if (profileImageUrl != null) profileImageUrl!
  ];
}
