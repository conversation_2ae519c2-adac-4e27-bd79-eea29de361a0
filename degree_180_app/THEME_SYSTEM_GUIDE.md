# 🎨 دليل نظام الثيمات المتقدم - تطبيق 180 درجة

## 📋 نظرة عامة

تم تطبيق نظام ثيمات متقدم وديناميكي في تطبيق 180 درجة يتيح تغيير المظهر تلقائياً بناءً على جنس المستخدم، مع دعم كامل للوضع الليلي والتخصيص المتقدم.

## 🎯 الميزات الرئيسية

### ✨ الثيمات المتاحة:
- **🟣 الثيم الذكوري**: ألوان بنفسجية وزرقاء قوية
- **🌸 الثيم الأنثوي**: ألوان وردية ونعومة أنثوية  
- **🌙 الوضع الليلي**: متاح لكلا الثيمين
- **🔄 التبديل التلقائي**: حسب الجنس المختار

### 🎨 الألوان المطبقة:

#### الثيم الذكوري:
```dart
Primary: #6C63FF (بنفسجي)
Secondary: #9C88FF (بنفسجي فاتح)
Gradient: [#6C63FF, #9C88FF]
```

#### الثيم الأنثوي:
```dart
Primary: #E91E63 (وردي)
Secondary: #F8BBD9 (وردي فاتح)
Gradient: [#E91E63, #F8BBD9]
```

## 🚀 كيفية الاستخدام

### 1. الاستخدام الأساسي:

```dart
import '../../../../core/services/theme_service.dart';
import '../../../../core/enums/gender.dart';

class MyWidget extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    final themeService = ThemeService();
    
    return ListenableBuilder(
      listenable: themeService,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: themeService.gradientColors,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: YourContent(),
        );
      },
    );
  }
}
```

### 2. تغيير الثيم:

```dart
final themeService = ThemeService();

// تطبيق الثيم الأنثوي
await themeService.setGender(Gender.female);

// تطبيق الثيم الذكوري
await themeService.setGender(Gender.male);

// تفعيل الوضع الليلي
await themeService.toggleDarkMode();
```

### 3. استخدام مكونات الثيم الجاهزة:

```dart
// مؤشر الثيم الحالي
ThemeIndicator(
  showLabel: true,
  onTap: () {
    Navigator.pushNamed(context, '/theme-settings');
  },
)

// مكون اختيار الجنس
GenderSelectionWidget(
  selectedGender: currentGender,
  onGenderSelected: (gender) {
    // سيتم تطبيق الثيم تلقائياً
    setState(() {
      currentGender = gender;
    });
  },
)

// مبدل الثيم السريع
ThemeQuickSwitcher()
```

## 📁 هيكل الملفات

```
lib/
├── core/
│   ├── enums/
│   │   └── gender.dart                    # تعريف أنواع الجنس
│   ├── theme/
│   │   ├── app_theme.dart                 # الثيمات الرئيسية
│   │   └── female_colors.dart             # ألوان الثيم الأنثوي
│   ├── services/
│   │   └── theme_service.dart             # خدمة إدارة الثيمات
│   └── widgets/
│       └── theme_indicator.dart           # مكونات الثيم
├── features/
│   ├── auth/presentation/widgets/
│   │   └── gender_selection_widget.dart   # مكون اختيار الجنس
│   └── profile/presentation/pages/
│       └── theme_settings_page.dart       # صفحة إعدادات المظهر
└── themed_app.dart                        # التطبيق مع دعم الثيمات
```

## 🔧 الإعدادات المتقدمة

### 1. تخصيص الألوان:

```dart
// الحصول على الألوان الحالية
Color primaryColor = themeService.primaryColor;
Color primaryColorLight = themeService.primaryColorLight;
Color accentColor = themeService.accentColor;
List<Color> gradientColors = themeService.gradientColors;

// التحقق من نوع الثيم
bool isFemaleTheme = themeService.isFemaleTheme;
bool isMaleTheme = themeService.isMaleTheme;
bool isDarkMode = themeService.isDarkMode;
```

### 2. استخدام الديكوريشن الجاهز:

```dart
// خلفية متدرجة
Container(
  decoration: themeService.getPrimaryGradientDecoration(
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: themeService.primaryColor.withValues(alpha: 0.3),
        blurRadius: 8,
        offset: Offset(0, 4),
      ),
    ],
  ),
)

// أزرار مخصصة
ElevatedButton(
  style: themeService.getPrimaryButtonStyle(),
  onPressed: () {},
  child: Text('Button'),
)

// حقول الإدخال
TextField(
  decoration: themeService.getInputDecoration(
    labelText: 'Label',
    hintText: 'Hint',
    prefixIcon: Icon(Icons.person),
  ),
)
```

### 3. AppBar مخصص:

```dart
@override
Widget build(BuildContext context) {
  return Scaffold(
    appBar: themeService.getThemedAppBar(
      title: 'Page Title',
      actions: [
        IconButton(
          icon: Icon(Icons.settings),
          onPressed: () {},
        ),
      ],
    ),
    body: YourContent(),
  );
}
```

## 🎨 التخصيص المتقدم

### 1. إضافة ألوان جديدة:

```dart
// في female_colors.dart
class FemaleColors {
  static const Color customPink = Color(0xFFFF69B4);
  static const Color customRose = Color(0xFFE8B4B8);
  
  static const List<Color> customGradient = [
    customPink,
    customRose,
  ];
}
```

### 2. إنشاء ثيم مخصص:

```dart
// في app_theme.dart
static ThemeData get customTheme {
  return ThemeData(
    useMaterial3: true,
    primaryColor: YourCustomColor,
    colorScheme: ColorScheme.fromSeed(
      seedColor: YourCustomColor,
      brightness: Brightness.light,
    ),
    // ... باقي الإعدادات
  );
}
```

## 📱 التكامل مع الصفحات

### 1. في صفحة التسجيل:
```dart
// تم إضافة مكون اختيار الجنس
GenderSelectionWidget(
  selectedGender: _selectedGender,
  onGenderSelected: (gender) {
    setState(() {
      _selectedGender = gender;
    });
    // تطبيق الثيم فوراً
    _themeService.setGender(gender);
  },
)
```

### 2. في الصفحة الرئيسية:
```dart
// تم إضافة مؤشر الثيم في الـ header
ThemeIndicator(
  showLabel: false,
  size: 32,
  onTap: () {
    Navigator.pushNamed(context, '/theme-settings');
  },
)
```

### 3. في صفحة الإعدادات:
```dart
// تم إضافة قسم كامل للثيم
SettingsSection(
  title: 'المظهر والثيم',
  icon: Icons.palette,
  children: [
    ThemeQuickSwitcher(),
    // ... باقي الإعدادات
  ],
)
```

## 🔄 حفظ التفضيلات

النظام يحفظ تفضيلات المستخدم تلقائياً:
- **نوع الثيم المختار** (ذكوري/أنثوي)
- **حالة الوضع الليلي**
- **آخر إعدادات مطبقة**

```dart
// يتم الحفظ تلقائياً عند التغيير
await themeService.setGender(Gender.female);
await themeService.toggleDarkMode();

// أو يمكن الحفظ يدوياً
await themeService._saveThemeSettings();
```

## 🎯 أفضل الممارسات

### 1. استخدم ListenableBuilder:
```dart
ListenableBuilder(
  listenable: ThemeService(),
  builder: (context, child) {
    // UI يتحدث تلقائياً عند تغيير الثيم
    return YourWidget();
  },
)
```

### 2. تجنب إنشاء instances متعددة:
```dart
// ✅ صحيح - استخدم singleton
final themeService = ThemeService();

// ❌ خطأ - لا تنشئ instances جديدة
final newThemeService = ThemeService();
```

### 3. استخدم الألوان الديناميكية:
```dart
// ✅ صحيح - ألوان ديناميكية
color: themeService.primaryColor

// ❌ خطأ - ألوان ثابتة
color: AppColors.primaryPurple
```

## 🚀 النتيجة النهائية

تم تطبيق نظام ثيمات متقدم وشامل يوفر:
- ✅ **تبديل تلقائي** للثيم حسب الجنس
- ✅ **حفظ التفضيلات** تلقائياً
- ✅ **واجهات متجاوبة** مع الثيم
- ✅ **مكونات جاهزة** للاستخدام
- ✅ **تخصيص متقدم** للمطورين
- ✅ **أداء محسن** مع caching
- ✅ **تجربة مستخدم ممتازة**

**🎉 النظام جاهز للاستخدام الفوري ويعمل بشكل مثالي!**
