import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/enums/service_type.dart';
import '../../../../core/theme/theme_manager.dart';

/// BLoC لإدارة تدفق المصادقة الموحد
class AuthFlowBloc extends Bloc<AuthFlowEvent, AuthFlowState> {
  AuthFlowBloc() : super(const AuthFlowInitial()) {
    on<AuthFlowStarted>(_onAuthFlowStarted);
    on<AuthFlowGenderSelected>(_onGenderSelected);
    on<AuthFlowUserTypeSelected>(_onUserTypeSelected);
    on<AuthFlowServiceTypeSelected>(_onServiceTypeSelected);
    on<AuthFlowStepChanged>(_onStepChanged);
    on<AuthFlowModeChanged>(_onModeChanged);
    on<AuthFlowCompleted>(_onAuthFlowCompleted);
    on<AuthFlowReset>(_onAuthFlowReset);
  }

  void _onAuthFlowStarted(AuthFlowStarted event, Emitter<AuthFlowState> emit) {
    emit(const AuthFlowInProgress(
      currentStep: 0,
      selectedGender: null,
      selectedUserType: null,
      selectedServiceType: null,
      isLoginMode: true,
    ));
  }

  void _onGenderSelected(AuthFlowGenderSelected event, Emitter<AuthFlowState> emit) {
    if (state is AuthFlowInProgress) {
      final currentState = state as AuthFlowInProgress;
      
      // تطبيق الثيم المختار
      ThemeManager.setGender(event.gender);
      
      emit(currentState.copyWith(
        selectedGender: event.gender,
        currentStep: 1, // الانتقال للخطوة التالية
      ));
    }
  }

  void _onUserTypeSelected(AuthFlowUserTypeSelected event, Emitter<AuthFlowState> emit) {
    if (state is AuthFlowInProgress) {
      final currentState = state as AuthFlowInProgress;
      
      // تحديد الخطوة التالية حسب نوع المستخدم
      final nextStep = event.userType == 'worker' ? 2 : 3;
      
      emit(currentState.copyWith(
        selectedUserType: event.userType,
        currentStep: nextStep,
      ));
    }
  }

  void _onServiceTypeSelected(AuthFlowServiceTypeSelected event, Emitter<AuthFlowState> emit) {
    if (state is AuthFlowInProgress) {
      final currentState = state as AuthFlowInProgress;
      
      emit(currentState.copyWith(
        selectedServiceType: event.serviceType,
        currentStep: 3, // الانتقال لخطوة المصادقة
      ));
    }
  }

  void _onStepChanged(AuthFlowStepChanged event, Emitter<AuthFlowState> emit) {
    if (state is AuthFlowInProgress) {
      final currentState = state as AuthFlowInProgress;
      
      // التحقق من صحة الانتقال
      if (event.step >= 0 && event.step <= _getMaxSteps(currentState)) {
        emit(currentState.copyWith(currentStep: event.step));
      }
    }
  }

  void _onModeChanged(AuthFlowModeChanged event, Emitter<AuthFlowState> emit) {
    if (state is AuthFlowInProgress) {
      final currentState = state as AuthFlowInProgress;
      
      emit(currentState.copyWith(isLoginMode: event.isLoginMode));
    }
  }

  void _onAuthFlowCompleted(AuthFlowCompleted event, Emitter<AuthFlowState> emit) {
    if (state is AuthFlowInProgress) {
      final currentState = state as AuthFlowInProgress;
      
      emit(AuthFlowSuccess(
        selectedGender: currentState.selectedGender!,
        selectedUserType: currentState.selectedUserType!,
        selectedServiceType: currentState.selectedServiceType,
        isLoginMode: currentState.isLoginMode,
        userEmail: event.userEmail,
        userToken: event.userToken,
      ));
    }
  }

  void _onAuthFlowReset(AuthFlowReset event, Emitter<AuthFlowState> emit) {
    emit(const AuthFlowInitial());
  }

  int _getMaxSteps(AuthFlowInProgress state) {
    if (state.selectedUserType == 'worker') {
      return 3; // جنس، نوع مستخدم، نوع خدمة، مصادقة
    } else {
      return 2; // جنس، نوع مستخدم، مصادقة
    }
  }
}

/// أحداث تدفق المصادقة
abstract class AuthFlowEvent extends Equatable {
  const AuthFlowEvent();

  @override
  List<Object?> get props => [];
}

class AuthFlowStarted extends AuthFlowEvent {
  const AuthFlowStarted();
}

class AuthFlowGenderSelected extends AuthFlowEvent {
  final Gender gender;

  const AuthFlowGenderSelected(this.gender);

  @override
  List<Object> get props => [gender];
}

class AuthFlowUserTypeSelected extends AuthFlowEvent {
  final String userType;

  const AuthFlowUserTypeSelected(this.userType);

  @override
  List<Object> get props => [userType];
}

class AuthFlowServiceTypeSelected extends AuthFlowEvent {
  final ServiceType serviceType;

  const AuthFlowServiceTypeSelected(this.serviceType);

  @override
  List<Object> get props => [serviceType];
}

class AuthFlowStepChanged extends AuthFlowEvent {
  final int step;

  const AuthFlowStepChanged(this.step);

  @override
  List<Object> get props => [step];
}

class AuthFlowModeChanged extends AuthFlowEvent {
  final bool isLoginMode;

  const AuthFlowModeChanged(this.isLoginMode);

  @override
  List<Object> get props => [isLoginMode];
}

class AuthFlowCompleted extends AuthFlowEvent {
  final String userEmail;
  final String userToken;

  const AuthFlowCompleted({
    required this.userEmail,
    required this.userToken,
  });

  @override
  List<Object> get props => [userEmail, userToken];
}

class AuthFlowReset extends AuthFlowEvent {
  const AuthFlowReset();
}

/// حالات تدفق المصادقة
abstract class AuthFlowState extends Equatable {
  const AuthFlowState();

  @override
  List<Object?> get props => [];
}

class AuthFlowInitial extends AuthFlowState {
  const AuthFlowInitial();
}

class AuthFlowInProgress extends AuthFlowState {
  final int currentStep;
  final Gender? selectedGender;
  final String? selectedUserType;
  final ServiceType? selectedServiceType;
  final bool isLoginMode;

  const AuthFlowInProgress({
    required this.currentStep,
    this.selectedGender,
    this.selectedUserType,
    this.selectedServiceType,
    required this.isLoginMode,
  });

  AuthFlowInProgress copyWith({
    int? currentStep,
    Gender? selectedGender,
    String? selectedUserType,
    ServiceType? selectedServiceType,
    bool? isLoginMode,
  }) {
    return AuthFlowInProgress(
      currentStep: currentStep ?? this.currentStep,
      selectedGender: selectedGender ?? this.selectedGender,
      selectedUserType: selectedUserType ?? this.selectedUserType,
      selectedServiceType: selectedServiceType ?? this.selectedServiceType,
      isLoginMode: isLoginMode ?? this.isLoginMode,
    );
  }

  @override
  List<Object?> get props => [
        currentStep,
        selectedGender,
        selectedUserType,
        selectedServiceType,
        isLoginMode,
      ];
}

class AuthFlowSuccess extends AuthFlowState {
  final Gender selectedGender;
  final String selectedUserType;
  final ServiceType? selectedServiceType;
  final bool isLoginMode;
  final String userEmail;
  final String userToken;

  const AuthFlowSuccess({
    required this.selectedGender,
    required this.selectedUserType,
    this.selectedServiceType,
    required this.isLoginMode,
    required this.userEmail,
    required this.userToken,
  });

  @override
  List<Object?> get props => [
        selectedGender,
        selectedUserType,
        selectedServiceType,
        isLoginMode,
        userEmail,
        userToken,
      ];
}

class AuthFlowError extends AuthFlowState {
  final String message;

  const AuthFlowError(this.message);

  @override
  List<Object> get props => [message];
}
