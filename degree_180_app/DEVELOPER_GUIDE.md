# 📚 دليل المطور - تطبيق "180 درجة"

## 🎯 نظرة عامة

تطبيق "180 درجة" هو منصة شاملة لخدمات التجميل والعناية الشخصية مبني باستخدام Flutter مع تطبيق مبادئ Clean Architecture.

## 🏗️ البنية المعمارية

### Clean Architecture Layers

```
lib/
├── core/                    # الطبقة الأساسية
│   ├── constants/          # الثوابت والإعدادات
│   ├── enums/             # التعدادات
│   ├── errors/            # معالجة الأخطاء
│   ├── models/            # نماذج البيانات الأساسية
│   ├── network/           # إدارة الشبكة
│   ├── performance/       # مراقبة الأداء
│   ├── security/          # الأمان
│   ├── usecases/          # Use Cases الأساسية
│   └── widgets/           # المكونات المشتركة
├── features/              # الميزات
│   ├── auth/             # المصادقة
│   ├── booking/          # الحجوزات
│   ├── home/             # الصفحة الرئيسية
│   ├── maps/             # الخرائط
│   ├── notifications/    # الإشعارات
│   ├── payment/          # الدفع
│   ├── profile/          # الملف الشخصي
│   └── reviews/          # المراجعات
└── injection_container.dart # حقن التبعيات
```

### Feature Structure

كل ميزة تتبع بنية Clean Architecture:

```
feature/
├── data/
│   ├── datasources/       # مصادر البيانات
│   ├── models/           # نماذج البيانات
│   └── repositories/     # تطبيق Repository
├── domain/
│   ├── entities/         # الكيانات
│   ├── repositories/     # Repository Interfaces
│   └── usecases/         # Use Cases
└── presentation/
    ├── bloc/             # إدارة الحالة
    ├── pages/            # الصفحات
    └── widgets/          # المكونات
```

## 🔧 التقنيات المستخدمة

### Core Dependencies
- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة
- **Firebase**: الخدمات الخلفية
- **BLoC**: إدارة الحالة
- **GetIt**: حقن التبعيات
- **Dartz**: البرمجة الوظيفية
- **Equatable**: مقارنة الكائنات

### UI/UX Dependencies
- **Material Design 3**: نظام التصميم
- **Google Fonts**: الخطوط
- **Shimmer**: تأثيرات التحميل
- **Cached Network Image**: تحسين الصور

### Network & Data
- **Dio**: HTTP Client
- **Connectivity Plus**: مراقبة الاتصال
- **Shared Preferences**: التخزين المحلي
- **Cloud Firestore**: قاعدة البيانات

### Testing
- **Flutter Test**: الاختبارات الأساسية
- **Mockito**: Mock Objects
- **BLoC Test**: اختبار BLoC

## 🚀 البدء السريع

### 1. متطلبات النظام

```bash
Flutter SDK: >=3.0.0
Dart SDK: >=3.0.0
Android Studio / VS Code
Git
```

### 2. إعداد المشروع

```bash
# استنساخ المشروع
git clone <repository-url>
cd degree_180_app

# تثبيت التبعيات
flutter pub get

# تشغيل Code Generation
flutter packages pub run build_runner build

# تشغيل التطبيق
flutter run
```

### 3. إعداد Firebase

1. إنشاء مشروع Firebase جديد
2. إضافة تطبيقات Android و iOS
3. تحميل ملفات التكوين:
   - `android/app/google-services.json`
   - `ios/Runner/GoogleService-Info.plist`
4. تفعيل الخدمات المطلوبة:
   - Authentication
   - Cloud Firestore
   - Cloud Storage
   - Cloud Messaging

### 4. متغيرات البيئة

إنشاء ملف `.env` في الجذر:

```env
# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_API_KEY=your-api-key

# Google Maps
GOOGLE_MAPS_API_KEY=your-maps-api-key

# Stripe
STRIPE_PUBLISHABLE_KEY=your-stripe-key

# Environment
ENVIRONMENT=development
```

## 📱 إدارة الحالة

### BLoC Pattern

```dart
// Event
abstract class BookingEvent extends Equatable {}

class LoadBookingsEvent extends BookingEvent {
  final String userId;
  LoadBookingsEvent({required this.userId});
  
  @override
  List<Object> get props => [userId];
}

// State
abstract class BookingState extends Equatable {}

class BookingLoading extends BookingState {}

class BookingLoaded extends BookingState {
  final List<BookingModel> bookings;
  BookingLoaded({required this.bookings});
  
  @override
  List<Object> get props => [bookings];
}

// BLoC
class BookingBloc extends Bloc<BookingEvent, BookingState> {
  final GetBookingsUseCase getBookingsUseCase;
  
  BookingBloc({required this.getBookingsUseCase}) : super(BookingInitial()) {
    on<LoadBookingsEvent>(_onLoadBookings);
  }
  
  Future<void> _onLoadBookings(
    LoadBookingsEvent event,
    Emitter<BookingState> emit,
  ) async {
    emit(BookingLoading());
    
    final result = await getBookingsUseCase(GetBookingsParams(
      userId: event.userId,
    ));
    
    result.fold(
      (failure) => emit(BookingError(message: failure.message)),
      (bookings) => emit(BookingLoaded(bookings: bookings)),
    );
  }
}
```

### استخدام BLoC في الواجهة

```dart
class BookingsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<BookingBloc>()
        ..add(LoadBookingsEvent(userId: 'user-id')),
      child: BlocBuilder<BookingBloc, BookingState>(
        builder: (context, state) {
          if (state is BookingLoading) {
            return EnhancedLoadingWidget();
          } else if (state is BookingLoaded) {
            return BookingsList(bookings: state.bookings);
          } else if (state is BookingError) {
            return ErrorWidget(message: state.message);
          }
          return Container();
        },
      ),
    );
  }
}
```

## 🔐 الأمان

### SecurityManager

```dart
// تشفير البيانات الحساسة
final encryptedData = await SecurityManager.instance.encryptData(sensitiveData);

// تخزين آمن
await SecurityManager.instance.storeSecureData('key', data);

// التحقق من كلمة المرور
final isValid = await SecurityManager.instance.verifyPassword(password, hashedPassword);

// تتبع محاولات تسجيل الدخول
final canAttempt = await SecurityManager.instance.canAttemptLogin(email);
```

### Firebase Security Rules

```javascript
// قواعد Firestore
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /bookings/{bookingId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.customerId ||
         request.auth.uid == resource.data.providerId);
    }
  }
}
```

## ⚡ تحسين الأداء

### PerformanceManager

```dart
// مراقبة العمليات
PerformanceManager.instance.startOperation('load_bookings');
// ... تنفيذ العملية
PerformanceManager.instance.endOperation('load_bookings');

// أو استخدام timeOperation
final result = await PerformanceManager.instance.timeOperation(
  'api_call',
  () => apiService.getData(),
);

// تسجيل مقاييس مخصصة
PerformanceManager.instance.recordMetric('memory_usage', 75.5, unit: '%');
```

### تحسين الشبكة

```dart
// استخدام EnhancedNetworkManager
final response = await EnhancedNetworkManager.instance.get('/api/bookings');

// التحقق من حالة الاتصال
if (EnhancedNetworkManager.instance.isOnline) {
  // تنفيذ العملية
}
```

## 🧪 الاختبارات

### Unit Tests

```dart
void main() {
  group('BookingUseCase', () {
    late MockBookingRepository mockRepository;
    late GetBookingsUseCase useCase;

    setUp(() {
      mockRepository = MockBookingRepository();
      useCase = GetBookingsUseCase(mockRepository);
    });

    test('should return bookings when repository call is successful', () async {
      // Arrange
      final bookings = [testBooking];
      when(mockRepository.getBookings(any))
          .thenAnswer((_) async => Right(bookings));

      // Act
      final result = await useCase(GetBookingsParams(userId: 'user-id'));

      // Assert
      expect(result, Right(bookings));
      verify(mockRepository.getBookings('user-id'));
    });
  });
}
```

### Widget Tests

```dart
void main() {
  testWidgets('BookingCard should display booking information', (tester) async {
    // Arrange
    final booking = testBooking;

    // Act
    await tester.pumpWidget(MaterialApp(
      home: BookingCard(booking: booking),
    ));

    // Assert
    expect(find.text(booking.serviceName), findsOneWidget);
    expect(find.text(booking.providerName), findsOneWidget);
  });
}
```

### BLoC Tests

```dart
void main() {
  group('BookingBloc', () {
    late MockGetBookingsUseCase mockUseCase;
    late BookingBloc bloc;

    setUp(() {
      mockUseCase = MockGetBookingsUseCase();
      bloc = BookingBloc(getBookingsUseCase: mockUseCase);
    });

    blocTest<BookingBloc, BookingState>(
      'should emit [BookingLoading, BookingLoaded] when data is loaded successfully',
      build: () {
        when(mockUseCase(any)).thenAnswer((_) async => Right(testBookings));
        return bloc;
      },
      act: (bloc) => bloc.add(LoadBookingsEvent(userId: 'user-id')),
      expect: () => [
        BookingLoading(),
        BookingLoaded(bookings: testBookings),
      ],
    );
  });
}
```

## 🎨 التصميم والواجهات

### استخدام Design System

```dart
// الألوان
Container(
  color: AppColors.primaryPurple,
  child: Text(
    'مرحباً',
    style: TextStyle(color: AppColors.textWhite),
  ),
)

// المكونات المحسنة
EnhancedButton(
  text: 'احجز الآن',
  type: ButtonType.primary,
  onPressed: () => _handleBooking(),
)

EnhancedCard(
  child: ServiceProviderCard(
    name: provider.name,
    rating: provider.rating,
    onTap: () => _navigateToProvider(provider),
  ),
)
```

### الثيم المظلم

```dart
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system,
  home: HomePage(),
)
```

## 🔄 إدارة البيانات

### Repository Pattern

```dart
abstract class BookingRepository {
  Future<Either<Failure, List<BookingModel>>> getBookings(String userId);
  Future<Either<Failure, BookingModel>> createBooking(CreateBookingParams params);
}

class BookingRepositoryImpl implements BookingRepository {
  final BookingRemoteDataSource remoteDataSource;
  final BookingLocalDataSource localDataSource;
  
  @override
  Future<Either<Failure, List<BookingModel>>> getBookings(String userId) async {
    try {
      final bookings = await remoteDataSource.getBookings(userId);
      await localDataSource.cacheBookings(bookings);
      return Right(bookings);
    } catch (e) {
      try {
        final cachedBookings = await localDataSource.getCachedBookings(userId);
        return Right(cachedBookings);
      } catch (e) {
        return Left(CacheFailure());
      }
    }
  }
}
```

## 📊 المراقبة والتحليلات

### Firebase Analytics

```dart
// تتبع الأحداث
await FirebaseAnalytics.instance.logEvent(
  name: 'booking_created',
  parameters: {
    'service_type': serviceType,
    'provider_id': providerId,
    'amount': amount,
  },
);

// تتبع الشاشات
await FirebaseAnalytics.instance.logScreenView(
  screenName: 'BookingPage',
  screenClass: 'BookingPage',
);
```

### Crashlytics

```dart
// تسجيل الأخطاء
try {
  // كود قد يسبب خطأ
} catch (error, stackTrace) {
  await FirebaseCrashlytics.instance.recordError(
    error,
    stackTrace,
    fatal: false,
  );
}
```

## 🚀 النشر والإنتاج

### إعداد البيئات

```dart
// lib/app_config.dart
class AppConfig {
  static const bool isProduction = bool.fromEnvironment('PRODUCTION');
  static const String baseUrl = String.fromEnvironment('BASE_URL');
  static const String firebaseProjectId = String.fromEnvironment('FIREBASE_PROJECT_ID');
}
```

### Build Commands

```bash
# Development
flutter run --dart-define=ENVIRONMENT=development

# Staging
flutter build apk --dart-define=ENVIRONMENT=staging

# Production
flutter build apk --dart-define=ENVIRONMENT=production --dart-define=PRODUCTION=true
```

## 📋 قائمة المراجعة قبل النشر

### الأمان
- [ ] تحديث Firebase Security Rules
- [ ] إزالة البيانات الوهمية
- [ ] تشفير البيانات الحساسة
- [ ] تفعيل ProGuard/R8

### الأداء
- [ ] تحسين الصور
- [ ] تقليل حجم التطبيق
- [ ] اختبار الأداء على أجهزة مختلفة
- [ ] تفعيل Code Splitting

### الجودة
- [ ] تشغيل جميع الاختبارات
- [ ] مراجعة Code Coverage
- [ ] إصلاح جميع التحذيرات
- [ ] مراجعة الكود

### التوافق
- [ ] اختبار على Android/iOS
- [ ] اختبار أحجام شاشات مختلفة
- [ ] اختبار الوضع المظلم
- [ ] اختبار RTL/LTR

## 🆘 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ Firebase**: تحقق من ملفات التكوين
2. **مشاكل الشبكة**: تحقق من إعدادات Dio
3. **أخطاء BLoC**: تحقق من حقن التبعيات
4. **مشاكل التصميم**: تحقق من الثيم والألوان

### أدوات التشخيص

```dart
// تفعيل الـ Logging
if (kDebugMode) {
  print('Debug info: $data');
}

// مراقبة الأداء
PerformanceManager.instance.getPerformanceStats();

// إحصائيات الشبكة
EnhancedNetworkManager.instance.getNetworkStats();
```

## 📞 الدعم والمساهمة

للحصول على المساعدة أو المساهمة في المشروع:

1. راجع الوثائق أولاً
2. ابحث في Issues الموجودة
3. أنشئ Issue جديد مع تفاصيل المشكلة
4. اتبع معايير الكود المحددة

---

**تم إعداد هذا الدليل بواسطة فريق تطوير تطبيق "180 درجة"**
