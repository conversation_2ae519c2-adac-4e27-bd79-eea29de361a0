import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';

class EnhancedLoadingWidget extends StatelessWidget {
  final LoadingType type;
  final String? message;
  final double? size;
  final Color? color;

  const EnhancedLoadingWidget({
    super.key,
    this.type = LoadingType.circular,
    this.message,
    this.size,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildLoadingIndicator(),
          if (message != null) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: color ?? AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    switch (type) {
      case LoadingType.circular:
        return SizedBox(
          width: size ?? 40,
          height: size ?? 40,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primaryPurple,
            ),
          ),
        );
      
      case LoadingType.linear:
        return SizedBox(
          width: size ?? 200,
          child: LinearProgressIndicator(
            backgroundColor: AppColors.borderGray,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primaryPurple,
            ),
          ),
        );
      
      case LoadingType.dots:
        return _buildDotsLoading();
      
      case LoadingType.pulse:
        return _buildPulseLoading();
    }
  }

  Widget _buildDotsLoading() {
    return SizedBox(
      width: size ?? 60,
      height: size ?? 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(3, (index) {
          return AnimatedContainer(
            duration: Duration(milliseconds: 600 + (index * 200)),
            curve: Curves.easeInOut,
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color ?? AppColors.primaryPurple,
              shape: BoxShape.circle,
            ),
          );
        }),
      ),
    );
  }

  Widget _buildPulseLoading() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 1000),
      curve: Curves.easeInOut,
      width: size ?? 40,
      height: size ?? 40,
      decoration: BoxDecoration(
        color: (color ?? AppColors.primaryPurple).withValues(alpha: 0.3),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Container(
          width: (size ?? 40) * 0.6,
          height: (size ?? 40) * 0.6,
          decoration: BoxDecoration(
            color: color ?? AppColors.primaryPurple,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }
}

enum LoadingType {
  circular,
  linear,
  dots,
  pulse,
}

// Shimmer Loading Widgets
class ShimmerLoadingCard extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const ShimmerLoadingCard({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.borderGray,
      highlightColor: AppColors.backgroundWhite,
      child: Container(
        width: width ?? double.infinity,
        height: height ?? 120,
        decoration: BoxDecoration(
          color: AppColors.borderGray,
          borderRadius: borderRadius ?? 
              BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
      ),
    );
  }
}

class ShimmerLoadingList extends StatelessWidget {
  final int itemCount;
  final double itemHeight;
  final EdgeInsets? padding;

  const ShimmerLoadingList({
    super.key,
    this.itemCount = 5,
    this.itemHeight = 80,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: itemCount,
      separatorBuilder: (context, index) => 
          const SizedBox(height: AppConstants.defaultPadding),
      itemBuilder: (context, index) => ShimmerLoadingCard(
        height: itemHeight,
      ),
    );
  }
}

class ShimmerLoadingText extends StatelessWidget {
  final double? width;
  final double height;
  final int lines;

  const ShimmerLoadingText({
    super.key,
    this.width,
    this.height = 16,
    this.lines = 1,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.borderGray,
      highlightColor: AppColors.backgroundWhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(lines, (index) {
          return Container(
            width: index == lines - 1 ? (width ?? 200) * 0.7 : width ?? 200,
            height: height,
            margin: EdgeInsets.only(
              bottom: index < lines - 1 ? 8 : 0,
            ),
            decoration: BoxDecoration(
              color: AppColors.borderGray,
              borderRadius: BorderRadius.circular(4),
            ),
          );
        }),
      ),
    );
  }
}

class ShimmerLoadingAvatar extends StatelessWidget {
  final double size;
  final bool isCircular;

  const ShimmerLoadingAvatar({
    super.key,
    this.size = 40,
    this.isCircular = true,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.borderGray,
      highlightColor: AppColors.backgroundWhite,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: AppColors.borderGray,
          shape: isCircular ? BoxShape.circle : BoxShape.rectangle,
          borderRadius: isCircular ? null : BorderRadius.circular(8),
        ),
      ),
    );
  }
}

// Loading Overlay
class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? loadingMessage;
  final Color? overlayColor;

  const LoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.loadingMessage,
    this.overlayColor,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: overlayColor ?? Colors.black.withValues(alpha: 0.5),
            child: EnhancedLoadingWidget(
              message: loadingMessage,
              color: AppColors.textWhite,
            ),
          ),
      ],
    );
  }
}
