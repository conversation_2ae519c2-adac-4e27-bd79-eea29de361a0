import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/theme_manager.dart';
import '../../../../core/enums/gender.dart';
import '../widgets/custom_button.dart';
import '../widgets/gender_theme_selector.dart';

class WelcomePage extends StatefulWidget {
  const WelcomePage({super.key});

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  Gender? _selectedGender;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeManager.currentTheme,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: ThemeManager.currentColors.gradient,
          ),
          child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.largePadding),
            child: Column(
              children: [
                // Header
                Align(
                  alignment: Alignment.topLeft,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Text(
                      AppStrings.welcomeTo.split(' ').first,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textWhite,
                        fontSize: AppConstants.fontSizeLarge,
                      ),
                    ),
                  ),
                ),
                
                const Spacer(),
                
                // Main Content
                SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      children: [
                        // Logo
                        _buildLogo(),
                        
                        const SizedBox(height: AppConstants.extraLargePadding),
                        
                        // App Name
                        Text(
                          AppStrings.welcomeTo,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppColors.textWhite,
                            fontSize: AppConstants.fontSizeLarge,
                          ),
                        ),
                        
                        Text(
                          '180',
                          style: Theme.of(context).textTheme.displayLarge?.copyWith(
                            color: AppColors.textWhite,
                            fontSize: 80,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                        
                        const SizedBox(height: AppConstants.largePadding),
                        
                        // Slogan
                        Text(
                          AppStrings.appSlogan,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppColors.textWhite,
                            fontSize: AppConstants.fontSizeLarge,
                            height: 1.3,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const Spacer(),

                // Gender Theme Selector
                if (_selectedGender == null) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                    child: GenderThemeSelector(
                      selectedGender: _selectedGender,
                      onGenderChanged: (gender) {
                        setState(() {
                          _selectedGender = gender;
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: AppConstants.extraLargePadding),
                ] else ...[
                  // Compact gender selector
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'الثيم المحدد: ',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                      GenderThemeSelector(
                        selectedGender: _selectedGender,
                        onGenderChanged: (gender) {
                          setState(() {
                            _selectedGender = gender;
                          });
                        },
                        showTitle: false,
                        isCompact: true,
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                ],

                // Enter Button
                SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: ThemedButton(
                      text: AppStrings.enter,
                      onPressed: _selectedGender != null
                          ? () => context.go('/user-type-selection')
                          : null,
                      icon: Icons.arrow_forward,
                    ),
                  ),
                ),
                
                const SizedBox(height: AppConstants.largePadding),
              ],
            ),
          ),
        ),
      ),
    ));
  }

  Widget _buildLogo() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '180',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.w300,
                color: AppColors.textWhite,
                letterSpacing: 1,
              ),
            ),
            Container(
              width: 50,
              height: 1.5,
              color: AppColors.textWhite,
            ),
            const SizedBox(height: 4),
            Text(
              '°',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w300,
                color: AppColors.textWhite,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
