rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isProvider(providerId) {
      return isAuthenticated() && request.auth.uid == providerId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             request.auth.token.email_verified == true;
    }
    
    // Users collection
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow read: if isAuthenticated(); // Allow other users to read basic profile info
    }
    
    // Service providers collection
    match /service_providers/{providerId} {
      allow read: if true; // Public read access for browsing
      allow write: if isProvider(providerId);
      allow create: if isAuthenticated() && request.auth.uid == providerId;
      
      // Provider services subcollection
      match /services/{serviceId} {
        allow read: if true; // Public read access
        allow write: if isProvider(providerId);
      }
      
      // Provider availability subcollection
      match /availability/{availabilityId} {
        allow read: if true; // Public read access for booking
        allow write: if isProvider(providerId);
      }
      
      // Provider gallery subcollection
      match /gallery/{imageId} {
        allow read: if true; // Public read access
        allow write: if isProvider(providerId);
      }
    }
    
    // Bookings collection
    match /bookings/{bookingId} {
      allow read: if isAuthenticated() && 
                     (request.auth.uid == resource.data.customerId ||
                      request.auth.uid == resource.data.providerId);
      
      allow create: if isAuthenticated() && 
                       request.auth.uid == request.resource.data.customerId;
      
      allow update: if isAuthenticated() && 
                       (request.auth.uid == resource.data.customerId ||
                        request.auth.uid == resource.data.providerId) &&
                       // Only allow status updates and specific fields
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['status', 'updatedAt', 'cancellationReason', 
                                  'cancellationDate', 'cancellationBy', 'isReminderSent']);
      
      allow delete: if false; // No deletion allowed, only status updates
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      allow read: if true; // Public read access
      
      allow create: if isAuthenticated() && 
                       request.auth.uid == request.resource.data.customerId &&
                       // Ensure review is for a completed booking
                       exists(/databases/$(database)/documents/bookings/$(request.resource.data.bookingId)) &&
                       get(/databases/$(database)/documents/bookings/$(request.resource.data.bookingId)).data.status == 'completed';
      
      allow update: if isAuthenticated() && 
                       (request.auth.uid == resource.data.customerId ||
                        request.auth.uid == resource.data.providerId) &&
                       // Customers can update their review, providers can add response
                       ((request.auth.uid == resource.data.customerId &&
                         request.resource.data.diff(resource.data).affectedKeys()
                           .hasOnly(['comment', 'overallRating', 'detailedRatings', 'images', 'updatedAt'])) ||
                        (request.auth.uid == resource.data.providerId &&
                         request.resource.data.diff(resource.data).affectedKeys()
                           .hasOnly(['providerResponse', 'providerResponseDate', 'updatedAt'])));
      
      allow delete: if isOwner(resource.data.customerId); // Only customer can delete their review
    }
    
    // Categories collection (read-only for most users)
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if false; // Only admin can modify (handled via admin SDK)
    }
    
    // Promotions collection
    match /promotions/{promotionId} {
      allow read: if true; // Public read access
      allow write: if false; // Only admin/providers can modify (handled via admin SDK)
    }
    
    // Chat collections
    match /chats/{chatId} {
      allow read, write: if isAuthenticated() && 
                            (request.auth.uid in resource.data.participants);
      
      allow create: if isAuthenticated() && 
                       request.auth.uid in request.resource.data.participants;
      
      // Messages subcollection
      match /messages/{messageId} {
        allow read: if isAuthenticated() && 
                       request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
        
        allow create: if isAuthenticated() && 
                         request.auth.uid == request.resource.data.senderId &&
                         request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
        
        allow update: if isAuthenticated() && 
                         request.auth.uid == resource.data.senderId &&
                         // Only allow marking as read/delivered
                         request.resource.data.diff(resource.data).affectedKeys()
                           .hasOnly(['isRead', 'isDelivered', 'updatedAt']);
      }
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && 
                     request.auth.uid == resource.data.userId;
      
      allow update: if isAuthenticated() && 
                       request.auth.uid == resource.data.userId &&
                       // Only allow marking as read
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['isRead', 'readAt']);
      
      allow create, delete: if false; // Only server can create/delete notifications
    }
    
    // Analytics collection (write-only for tracking)
    match /analytics/{analyticsId} {
      allow read: if false; // No read access
      allow write: if isAuthenticated(); // Allow writing analytics data
    }
    
    // Reports collection
    match /reports/{reportId} {
      allow read: if false; // Only admin access
      allow create: if isAuthenticated() && 
                       request.auth.uid == request.resource.data.reportedBy;
      allow update, delete: if false; // Only admin can handle reports
    }
    
    // Admin collections (no public access)
    match /admin/{document=**} {
      allow read, write: if false; // Only admin SDK access
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
