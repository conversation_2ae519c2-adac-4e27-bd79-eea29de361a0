import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class ServiceSelectionCard extends StatelessWidget {
  final String title;
  final String description;
  final String duration;
  final String price;
  final bool isSelected;
  final VoidCallback onTap;

  const ServiceSelectionCard({
    super.key,
    required this.title,
    required this.description,
    required this.duration,
    required this.price,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.backgroundWhite,
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            border: Border.all(
              color: isSelected ? AppColors.primaryPurple : AppColors.borderGray,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Service Icon
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppColors.primaryPurple.withValues(alpha: 0.1)
                      : AppColors.backgroundGray,
                  borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                ),
                child: Icon(
                  _getServiceIcon(title),
                  color: isSelected ? AppColors.primaryPurple : AppColors.textGray,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: AppConstants.defaultPadding),
              
              // Service Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textDark,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textGray,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: AppColors.textGray,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          duration,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textGray,
                          ),
                        ),
                        const SizedBox(width: AppConstants.defaultPadding),
                        Icon(
                          Icons.attach_money,
                          size: 16,
                          color: AppColors.textGray,
                        ),
                        Text(
                          price,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.primaryPurple,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Selection Indicator
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primaryPurple : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? AppColors.primaryPurple : AppColors.borderGray,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: AppColors.textWhite,
                        size: 16,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getServiceIcon(String serviceName) {
    switch (serviceName.toLowerCase()) {
      case 'hair cut':
        return Icons.content_cut;
      case 'hair styling':
        return Icons.style;
      case 'beard trim':
        return Icons.face;
      case 'massage':
        return Icons.spa;
      case 'facial':
        return Icons.face_retouching_natural;
      case 'manicure':
        return Icons.back_hand;
      case 'pedicure':
        return Icons.accessibility_new;
      default:
        return Icons.room_service;
    }
  }
}
