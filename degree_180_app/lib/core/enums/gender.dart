enum Gender {
  male,
  female,
  other,
  notSpecified,
}

extension GenderExtension on Gender {
  String get displayName {
    switch (this) {
      case Gender.male:
        return 'ذكر';
      case Gender.female:
        return 'أنثى';
      case Gender.other:
        return 'آخر';
      case Gender.notSpecified:
        return 'غير محدد';
    }
  }

  String get displayNameEn {
    switch (this) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
      case Gender.other:
        return 'Other';
      case Gender.notSpecified:
        return 'Not Specified';
    }
  }

  String get code {
    switch (this) {
      case Gender.male:
        return 'M';
      case Gender.female:
        return 'F';
      case Gender.other:
        return 'O';
      case Gender.notSpecified:
        return 'N';
    }
  }

  static Gender fromCode(String code) {
    switch (code.toUpperCase()) {
      case 'M':
        return Gender.male;
      case 'F':
        return Gender.female;
      case 'O':
        return Gender.other;
      default:
        return Gender.notSpecified;
    }
  }

  static Gender fromString(String value) {
    switch (value.toLowerCase()) {
      case 'male':
      case 'ذكر':
        return Gender.male;
      case 'female':
      case 'أنثى':
        return Gender.female;
      case 'other':
      case 'آخر':
        return Gender.other;
      default:
        return Gender.notSpecified;
    }
  }


}
