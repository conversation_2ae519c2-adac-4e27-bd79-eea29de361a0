import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';

class ShiftCard extends StatelessWidget {
  final String fromTime;
  final String toTime;
  final String duration;
  final VoidCallback? onEditPressed;

  const ShiftCard({
    super.key,
    required this.fromTime,
    required this.toTime,
    required this.duration,
    this.onEditPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.backgroundWhite,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppStrings.shift,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.textDark,
                  fontWeight: FontWeight.w600,
                ),
              ),
              IconButton(
                onPressed: onEditPressed,
                icon: const Icon(
                  Icons.edit_outlined,
                  color: AppColors.primaryPurple,
                  size: 20,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            children: [
              Expanded(
                child: _buildTimeSection(
                  context,
                  AppStrings.from,
                  fromTime,
                ),
              ),
              Container(
                width: 2,
                height: 30,
                color: AppColors.borderGray,
                margin: const EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultPadding,
                ),
              ),
              Expanded(
                child: _buildTimeSection(
                  context,
                  AppStrings.to,
                  toTime,
                ),
              ),
              Container(
                width: 2,
                height: 30,
                color: AppColors.borderGray,
                margin: const EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultPadding,
                ),
              ),
              Expanded(
                child: _buildTimeSection(
                  context,
                  AppStrings.jobDuration,
                  duration,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSection(BuildContext context, String label, String time) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textGray,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          time,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: AppColors.textDark,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
