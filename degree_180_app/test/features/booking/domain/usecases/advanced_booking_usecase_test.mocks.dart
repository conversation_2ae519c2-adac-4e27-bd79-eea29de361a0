// Mocks generated by Mockito 5.4.4 from annotations
// in degree_180_app/test/features/booking/domain/usecases/advanced_booking_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:degree_180_app/core/errors/failures.dart' as _i5;
import 'package:degree_180_app/core/models/booking_model.dart' as _i6;
import 'package:degree_180_app/features/booking/domain/repositories/booking_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [BookingRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBookingRepository extends _i1.Mock implements _i3.BookingRepository {
  MockBookingRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>> createBooking(
          _i6.BookingModel? booking) =>
      (super.noSuchMethod(
        Invocation.method(
          #createBooking,
          [booking],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>>.value(
            _FakeEither_0<_i5.Failure, _i6.BookingModel>(
          this,
          Invocation.method(
            #createBooking,
            [booking],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>> updateBooking(
          _i6.BookingModel? booking) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBooking,
          [booking],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>>.value(
            _FakeEither_0<_i5.Failure, _i6.BookingModel>(
          this,
          Invocation.method(
            #updateBooking,
            [booking],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> deleteBooking(String? bookingId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteBooking,
          [bookingId],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
            _FakeEither_0<_i5.Failure, void>(
          this,
          Invocation.method(
            #deleteBooking,
            [bookingId],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>> getBooking(
          String? bookingId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBooking,
          [bookingId],
        ),
        returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>>.value(
            _FakeEither_0<_i5.Failure, _i6.BookingModel>(
          this,
          Invocation.method(
            #getBooking,
            [bookingId],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, _i6.BookingModel>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.BookingModel>>> getBookings({
    String? userId,
    String? providerId,
    String? status,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBookings,
          [],
          {
            #userId: userId,
            #providerId: providerId,
            #status: status,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i4.Future<_i2.Either<_i5.Failure, List<_i6.BookingModel>>>.value(
                _FakeEither_0<_i5.Failure, List<_i6.BookingModel>>(
          this,
          Invocation.method(
            #getBookings,
            [],
            {
              #userId: userId,
              #providerId: providerId,
              #status: status,
              #limit: limit,
              #offset: offset,
            },
          ),
        )),
      ) as _i4.Future<_i2.Either<_i5.Failure, List<_i6.BookingModel>>>);
}
