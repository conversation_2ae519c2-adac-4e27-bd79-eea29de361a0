import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class TimeSlotGrid extends StatelessWidget {
  final DateTime selectedDate;
  final String providerId;
  final String? selectedSlotId;
  final Function(String) onSlotSelected;

  const TimeSlotGrid({
    super.key,
    required this.selectedDate,
    required this.providerId,
    this.selectedSlotId,
    required this.onSlotSelected,
  });

  @override
  Widget build(BuildContext context) {
    // Mock time slots - in real app, this would come from a data source
    final timeSlots = _generateMockTimeSlots();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 2.5,
        crossAxisSpacing: AppConstants.smallPadding,
        mainAxisSpacing: AppConstants.smallPadding,
      ),
      itemCount: timeSlots.length,
      itemBuilder: (context, index) {
        final slot = timeSlots[index];
        return TimeSlotCard(
          timeSlot: slot,
          isSelected: selectedSlotId == slot['id'],
          onTap: slot['isAvailable'] 
              ? () => onSlotSelected(slot['id'])
              : null,
        );
      },
    );
  }

  List<Map<String, dynamic>> _generateMockTimeSlots() {
    final slots = <Map<String, dynamic>>[];
    final startHour = 9;
    final endHour = 18;
    
    for (int hour = startHour; hour < endHour; hour++) {
      for (int minute = 0; minute < 60; minute += 30) {
        final timeString = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
        final isAvailable = _isSlotAvailable(hour, minute);
        
        slots.add({
          'id': '${selectedDate.toIso8601String()}_${hour}_$minute',
          'time': timeString,
          'isAvailable': isAvailable,
          'price': isAvailable ? '15 KD' : null,
        });
      }
    }
    
    return slots;
  }

  bool _isSlotAvailable(int hour, int minute) {
    // Mock availability logic
    // In real app, this would check against actual bookings
    if (hour == 12 && minute == 0) return false; // Lunch break
    if (hour == 12 && minute == 30) return false; // Lunch break
    if (hour == 15 && minute == 0) return false; // Already booked
    if (hour == 16 && minute == 30) return false; // Already booked
    
    return true;
  }
}

class TimeSlotCard extends StatelessWidget {
  final Map<String, dynamic> timeSlot;
  final bool isSelected;
  final VoidCallback? onTap;

  const TimeSlotCard({
    super.key,
    required this.timeSlot,
    required this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isAvailable = timeSlot['isAvailable'] as bool;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        child: Container(
          decoration: BoxDecoration(
            color: _getBackgroundColor(isAvailable, isSelected),
            borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
            border: Border.all(
              color: _getBorderColor(isAvailable, isSelected),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                timeSlot['time'],
                style: TextStyle(
                  color: _getTextColor(isAvailable, isSelected),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  fontSize: AppConstants.fontSizeSmall,
                ),
              ),
              if (isAvailable && timeSlot['price'] != null) ...[
                const SizedBox(height: 2),
                Text(
                  timeSlot['price'],
                  style: TextStyle(
                    color: _getTextColor(isAvailable, isSelected).withValues(alpha: 0.7),
                    fontSize: 10,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor(bool isAvailable, bool isSelected) {
    if (!isAvailable) {
      return AppColors.backgroundGray;
    }
    if (isSelected) {
      return AppColors.primaryPurple;
    }
    return AppColors.backgroundWhite;
  }

  Color _getBorderColor(bool isAvailable, bool isSelected) {
    if (!isAvailable) {
      return AppColors.borderGray;
    }
    if (isSelected) {
      return AppColors.primaryPurple;
    }
    return AppColors.borderGray;
  }

  Color _getTextColor(bool isAvailable, bool isSelected) {
    if (!isAvailable) {
      return AppColors.textGray;
    }
    if (isSelected) {
      return AppColors.textWhite;
    }
    return AppColors.textDark;
  }
}
