import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
// import 'package:shared_preferences/shared_preferences.dart';

class PerformanceManager {
  static PerformanceManager? _instance;
  static PerformanceManager get instance => _instance ??= PerformanceManager._();
  
  PerformanceManager._();

  final Map<String, DateTime> _operationStartTimes = {};
  final Map<String, List<int>> _operationDurations = {};
  final List<PerformanceMetric> _metrics = [];
  
  Timer? _memoryMonitorTimer;
  Timer? _metricsCleanupTimer;

  // Initialize performance monitoring
  void initialize() {
    _startMemoryMonitoring();
    _startMetricsCleanup();
    
    if (kDebugMode) {
      debugPrint('Performance Manager initialized');
    }
  }

  // Start timing an operation
  void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
  }

  // End timing an operation and record duration
  void endOperation(String operationName) {
    final startTime = _operationStartTimes[operationName];
    if (startTime == null) return;

    final duration = DateTime.now().difference(startTime).inMilliseconds;
    _operationStartTimes.remove(operationName);

    // Store duration for analysis
    _operationDurations.putIfAbsent(operationName, () => []).add(duration);
    
    // Keep only last 100 measurements per operation
    if (_operationDurations[operationName]!.length > 100) {
      _operationDurations[operationName]!.removeAt(0);
    }

    // Record metric
    _recordMetric(PerformanceMetric(
      name: operationName,
      value: duration.toDouble(),
      unit: 'ms',
      timestamp: DateTime.now(),
      type: MetricType.duration,
    ));

    if (kDebugMode && duration > 1000) {
      debugPrint('⚠️ Slow operation: $operationName took ${duration}ms');
    }
  }

  // Time a future operation
  Future<T> timeOperation<T>(String operationName, Future<T> Function() operation) async {
    startOperation(operationName);
    try {
      final result = await operation();
      endOperation(operationName);
      return result;
    } catch (e) {
      endOperation(operationName);
      rethrow;
    }
  }

  // Time a synchronous operation
  T timeSync<T>(String operationName, T Function() operation) {
    startOperation(operationName);
    try {
      final result = operation();
      endOperation(operationName);
      return result;
    } catch (e) {
      endOperation(operationName);
      rethrow;
    }
  }

  // Get average duration for an operation
  double? getAverageDuration(String operationName) {
    final durations = _operationDurations[operationName];
    if (durations == null || durations.isEmpty) return null;

    final sum = durations.reduce((a, b) => a + b);
    return sum / durations.length;
  }

  // Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    final stats = <String, dynamic>{};
    
    for (final entry in _operationDurations.entries) {
      final durations = entry.value;
      if (durations.isNotEmpty) {
        final sorted = List<int>.from(durations)..sort();
        stats[entry.key] = {
          'count': durations.length,
          'average': durations.reduce((a, b) => a + b) / durations.length,
          'min': sorted.first,
          'max': sorted.last,
          'median': sorted[sorted.length ~/ 2],
          'p95': sorted[(sorted.length * 0.95).floor()],
        };
      }
    }
    
    return stats;
  }

  // Record custom metric
  void recordMetric(String name, double value, {String unit = '', MetricType type = MetricType.custom}) {
    _recordMetric(PerformanceMetric(
      name: name,
      value: value,
      unit: unit,
      timestamp: DateTime.now(),
      type: type,
    ));
  }

  void _recordMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    
    // Keep only last 1000 metrics
    if (_metrics.length > 1000) {
      _metrics.removeAt(0);
    }
  }

  // Monitor memory usage
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkMemoryUsage();
    });
  }

  void _checkMemoryUsage() {
    // This is a simplified memory check
    // In a real app, you might use more sophisticated memory monitoring
    if (Platform.isAndroid || Platform.isIOS) {
      SystemChannels.platform.invokeMethod('SystemChrome.getMemoryInfo').then((info) {
        if (info != null && info is Map) {
          final usedMemory = info['usedMemory'] as int? ?? 0;
          final totalMemory = info['totalMemory'] as int? ?? 1;
          final memoryUsagePercent = (usedMemory / totalMemory) * 100;
          
          recordMetric('memory_usage_percent', memoryUsagePercent, unit: '%', type: MetricType.memory);
          
          if (memoryUsagePercent > 80) {
            debugPrint('⚠️ High memory usage: ${memoryUsagePercent.toStringAsFixed(1)}%');
            _suggestMemoryOptimization();
          }
        }
      }).catchError((e) {
        // Memory info not available on this platform
      });
    }
  }

  void _suggestMemoryOptimization() {
    if (kDebugMode) {
      debugPrint('💡 Consider optimizing memory usage:');
      debugPrint('   - Clear unused caches');
      debugPrint('   - Dispose unused controllers');
      debugPrint('   - Optimize image loading');
    }
  }

  // Clean up old metrics
  void _startMetricsCleanup() {
    _metricsCleanupTimer = Timer.periodic(const Duration(hours: 1), (timer) {
      final cutoff = DateTime.now().subtract(const Duration(hours: 24));
      _metrics.removeWhere((metric) => metric.timestamp.isBefore(cutoff));
    });
  }

  // Get recent metrics
  List<PerformanceMetric> getRecentMetrics({Duration? since}) {
    final cutoff = since != null 
        ? DateTime.now().subtract(since)
        : DateTime.now().subtract(const Duration(hours: 1));
    
    return _metrics.where((metric) => metric.timestamp.isAfter(cutoff)).toList();
  }

  // Cache management
  final Map<String, CacheEntry> _cache = {};
  static const int _maxCacheSize = 100;
  static const Duration _defaultCacheExpiry = Duration(minutes: 30);

  // Store data in cache
  void cacheData(String key, dynamic data, {Duration? expiry}) {
    if (_cache.length >= _maxCacheSize) {
      _evictOldestCacheEntry();
    }

    _cache[key] = CacheEntry(
      data: data,
      timestamp: DateTime.now(),
      expiry: expiry ?? _defaultCacheExpiry,
    );
  }

  // Get data from cache
  T? getCachedData<T>(String key) {
    final entry = _cache[key];
    if (entry == null) return null;

    if (entry.isExpired) {
      _cache.remove(key);
      return null;
    }

    return entry.data as T?;
  }

  // Clear cache
  void clearCache() {
    _cache.clear();
  }

  // Remove expired cache entries
  void cleanupCache() {
    _cache.removeWhere((key, entry) => entry.isExpired);
  }

  void _evictOldestCacheEntry() {
    if (_cache.isEmpty) return;

    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _cache.entries) {
      if (oldestTime == null || entry.value.timestamp.isBefore(oldestTime)) {
        oldestTime = entry.value.timestamp;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _cache.remove(oldestKey);
    }
  }

  // Image optimization helpers
  static const int _maxImageCacheSize = 50;
  final Map<String, DateTime> _imageCache = {};

  void trackImageLoad(String imageUrl) {
    if (_imageCache.length >= _maxImageCacheSize) {
      final oldestUrl = _imageCache.entries
          .reduce((a, b) => a.value.isBefore(b.value) ? a : b)
          .key;
      _imageCache.remove(oldestUrl);
    }

    _imageCache[imageUrl] = DateTime.now();
  }

  // Performance recommendations
  List<String> getPerformanceRecommendations() {
    final recommendations = <String>[];
    final stats = getPerformanceStats();

    // Check for slow operations
    for (final entry in stats.entries) {
      final average = entry.value['average'] as double;
      if (average > 2000) {
        recommendations.add('Optimize ${entry.key} operation (avg: ${average.toStringAsFixed(0)}ms)');
      }
    }

    // Check cache hit rate
    if (_cache.length > _maxCacheSize * 0.8) {
      recommendations.add('Consider increasing cache size or reducing cache expiry time');
    }

    // Check memory usage
    final recentMemoryMetrics = getRecentMetrics(since: const Duration(minutes: 10))
        .where((m) => m.type == MetricType.memory)
        .toList();
    
    if (recentMemoryMetrics.isNotEmpty) {
      final avgMemory = recentMemoryMetrics
          .map((m) => m.value)
          .reduce((a, b) => a + b) / recentMemoryMetrics.length;
      
      if (avgMemory > 70) {
        recommendations.add('High memory usage detected (${avgMemory.toStringAsFixed(1)}%)');
      }
    }

    return recommendations;
  }

  // Dispose resources
  void dispose() {
    _memoryMonitorTimer?.cancel();
    _metricsCleanupTimer?.cancel();
    _cache.clear();
    _metrics.clear();
    _operationDurations.clear();
    _operationStartTimes.clear();
  }
}

class PerformanceMetric {
  final String name;
  final double value;
  final String unit;
  final DateTime timestamp;
  final MetricType type;

  const PerformanceMetric({
    required this.name,
    required this.value,
    required this.unit,
    required this.timestamp,
    required this.type,
  });
}

enum MetricType {
  duration,
  memory,
  network,
  custom,
}

class CacheEntry {
  final dynamic data;
  final DateTime timestamp;
  final Duration expiry;

  const CacheEntry({
    required this.data,
    required this.timestamp,
    required this.expiry,
  });

  bool get isExpired => DateTime.now().isAfter(timestamp.add(expiry));
}
