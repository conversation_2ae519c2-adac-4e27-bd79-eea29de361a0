// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:degree_180_app/themed_app.dart';
import 'package:degree_180_app/core/constants/app_strings.dart';

void main() {
  group('App Widget Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const ThemedDegree180App());

      // Verify that app builds successfully
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App should have correct title', (WidgetTester tester) async {
      await tester.pumpWidget(const ThemedDegree180App());

      // Get the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify app title
      expect(materialApp.title, equals(AppStrings.appName));
    });

    testWidgets('App should not show debug banner', (WidgetTester tester) async {
      await tester.pumpWidget(const ThemedDegree180App());

      // Get the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify debug banner is disabled
      expect(materialApp.debugShowCheckedModeBanner, isFalse);
    });
  });

  group('Basic Navigation Tests', () {
    testWidgets('App should have router configuration', (WidgetTester tester) async {
      await tester.pumpWidget(const ThemedDegree180App());

      // Get the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify router is configured
      expect(materialApp.routerConfig, isNotNull);
    });
  });

  group('Theme Tests', () {
    testWidgets('App should have light theme configured', (WidgetTester tester) async {
      await tester.pumpWidget(const ThemedDegree180App());

      // Get the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify theme is configured
      expect(materialApp.theme, isNotNull);
    });
  });
}
