import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/services/theme_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_switch_tile.dart';
import '../widgets/settings_tile.dart';

class ThemeSettingsPage extends StatefulWidget {
  const ThemeSettingsPage({super.key});

  @override
  State<ThemeSettingsPage> createState() => _ThemeSettingsPageState();
}

class _ThemeSettingsPageState extends State<ThemeSettingsPage>
    with TickerProviderStateMixin {
  final ThemeService _themeService = ThemeService();
  final AnalyticsService _analytics = AnalyticsService();
  
  late AnimationController _previewController;
  late Animation<double> _previewAnimation;

  @override
  void initState() {
    super.initState();
    _previewController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _previewAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _previewController,
      curve: Curves.easeInOut,
    ));
    
    _analytics.logScreenView('Theme Settings', 'ThemeSettingsPage');
  }

  @override
  void dispose() {
    _previewController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات المظهر'),
        backgroundColor: _themeService.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetToDefault,
            tooltip: 'إعادة تعيين',
          ),
        ],
      ),
      body: ListenableBuilder(
        listenable: _themeService,
        builder: (context, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Theme Preview
                _buildThemePreview(),
                
                const SizedBox(height: 24),
                
                // Gender Theme Selection
                _buildGenderThemeSelection(),
                
                const SizedBox(height: 20),
                
                // Dark Mode Toggle
                _buildDarkModeSettings(),
                
                const SizedBox(height: 20),
                
                // Theme Information
                _buildThemeInformation(),
                
                const SizedBox(height: 20),
                
                // Advanced Settings
                _buildAdvancedSettings(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildThemePreview() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معاينة المظهر',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Preview Container
            AnimatedBuilder(
              animation: _previewAnimation,
              builder: (context, child) {
                return Container(
                  width: double.infinity,
                  height: 200,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _themeService.gradientColors,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: _themeService.primaryColor.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      // Background Pattern
                      Positioned.fill(
                        child: CustomPaint(
                          painter: _ThemePatternPainter(
                            color: Colors.white.withValues(alpha: 0.1),
                          ),
                        ),
                      ),
                      
                      // Content
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  _themeService.getGenderIcon(),
                                  color: Colors.white,
                                  size: 32,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        _themeService.getThemeName(),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(
                                        _themeService.currentGender.displayName,
                                        style: TextStyle(
                                          color: Colors.white.withValues(alpha: 0.8),
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            
                            const Spacer(),
                            
                            // Sample UI Elements
                            Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Text(
                                      'عنصر تجريبي',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.favorite,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderThemeSelection() {
    return SettingsSection(
      title: 'نوع المظهر',
      icon: Icons.palette,
      children: [
        _buildGenderThemeCard(
          gender: Gender.male,
          title: 'مظهر ذكوري',
          subtitle: 'ألوان بنفسجية وزرقاء',
          icon: Icons.male,
          colors: [
            const Color(0xFF6C63FF),
            const Color(0xFF9C88FF),
          ],
        ),
        
        _buildGenderThemeCard(
          gender: Gender.female,
          title: 'مظهر أنثوي',
          subtitle: 'ألوان وردية ونعومة',
          icon: Icons.female,
          colors: [
            const Color(0xFFE91E63),
            const Color(0xFFF8BBD9),
          ],
        ),
        
        _buildGenderThemeCard(
          gender: Gender.notSpecified,
          title: 'مظهر افتراضي',
          subtitle: 'مظهر متوازن ومحايد',
          icon: Icons.auto_awesome,
          colors: [
            const Color(0xFF6C63FF),
            const Color(0xFF9C88FF),
          ],
        ),
      ],
    );
  }

  Widget _buildGenderThemeCard({
    required Gender gender,
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> colors,
  }) {
    final isSelected = _themeService.currentGender == gender;
    
    return GestureDetector(
      onTap: () => _selectGenderTheme(gender),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected 
              ? colors.first.withValues(alpha: 0.1)
              : AppColors.backgroundWhite,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? colors.first : AppColors.borderGray,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Color Preview
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: colors,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Title and Subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? colors.first : AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textLight,
                    ),
                  ),
                ],
              ),
            ),
            
            // Selection Indicator
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isSelected ? colors.first : Colors.transparent,
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? colors.first : AppColors.borderGray,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDarkModeSettings() {
    return SettingsSection(
      title: 'الوضع الليلي',
      icon: Icons.dark_mode,
      children: [
        SettingsSwitchTile(
          title: 'تفعيل الوضع الليلي',
          subtitle: 'مظهر داكن لراحة العينين',
          icon: _themeService.isDarkMode ? Icons.dark_mode : Icons.light_mode,
          value: _themeService.isDarkMode,
          onChanged: (value) {
            _themeService.toggleDarkMode();
            _previewController.forward().then((_) {
              _previewController.reverse();
            });
          },
        ),
      ],
    );
  }

  Widget _buildThemeInformation() {
    return SettingsSection(
      title: 'معلومات المظهر',
      icon: Icons.info,
      children: [
        SettingsTile(
          title: 'المظهر الحالي',
          subtitle: _themeService.getThemeName(),
          icon: _themeService.getGenderIcon(),
        ),
        SettingsTile(
          title: 'نوع الجنس',
          subtitle: _themeService.currentGender.displayName,
          icon: Icons.person,
        ),
        SettingsTile(
          title: 'اللون الأساسي',
          subtitle: _getColorHex(_themeService.primaryColor),
          icon: Icons.color_lens,
          trailing: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: _themeService.primaryColor,
              shape: BoxShape.circle,
              border: Border.all(color: AppColors.borderGray),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings() {
    return SettingsSection(
      title: 'إعدادات متقدمة',
      icon: Icons.tune,
      children: [
        SettingsTile(
          title: 'إعادة تعيين المظهر',
          subtitle: 'العودة للمظهر الافتراضي',
          icon: Icons.refresh,
          onTap: _resetToDefault,
        ),
        SettingsTile(
          title: 'تطبيق تلقائي',
          subtitle: 'تطبيق المظهر حسب الجنس المحدد',
          icon: Icons.auto_awesome,
          onTap: _showAutoApplyDialog,
        ),
      ],
    );
  }

  void _selectGenderTheme(Gender gender) {
    _themeService.setGender(gender);
    _previewController.forward().then((_) {
      _previewController.reverse();
    });
    
    _analytics.logCustomEvent('theme_gender_changed', {
      'new_gender': gender.name,
      'previous_gender': _themeService.currentGender.name,
    });
  }

  void _resetToDefault() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين المظهر'),
        content: const Text('هل تريد العودة للمظهر الافتراضي؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _themeService.resetToDefault();
              _previewController.forward().then((_) {
                _previewController.reverse();
              });
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  void _showAutoApplyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التطبيق التلقائي'),
        content: const Text(
          'سيتم تطبيق المظهر تلقائياً بناءً على الجنس المحدد في الملف الشخصي. هل تريد المتابعة؟'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Auto-detect theme based on user gender
              // This would typically get the gender from user profile
              _themeService.autoDetectTheme(Gender.notSpecified);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  String _getColorHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }
}

// Custom painter for theme preview pattern
class _ThemePatternPainter extends CustomPainter {
  final Color color;

  _ThemePatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Draw decorative circles
    for (int i = 0; i < 5; i++) {
      final radius = (i + 1) * 20.0;
      final center = Offset(size.width * 0.8, size.height * 0.2);
      
      canvas.drawCircle(center, radius, paint..color = color.withValues(alpha: 0.1 - (i * 0.02)));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
