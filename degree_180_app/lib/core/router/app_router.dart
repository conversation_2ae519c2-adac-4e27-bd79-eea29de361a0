import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/auth/presentation/pages/onboarding_page.dart';
import '../../features/auth/presentation/pages/welcome_page.dart';
import '../../features/auth/presentation/pages/user_type_selection_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../features/home/<USER>/pages/worker_dashboard_page.dart';
import '../../features/home/<USER>/pages/customer_home_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';
import '../../features/booking/presentation/pages/booking_page.dart';
import '../../features/booking/presentation/pages/my_bookings_page.dart';
import '../../features/payment/presentation/pages/payment_page.dart';
import '../../features/services/presentation/pages/service_provider_details_page.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';

class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: '/',
    routes: [
      // Splash Route
      GoRoute(
        path: '/',
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Onboarding Route
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // Welcome Route
      GoRoute(
        path: '/welcome',
        name: 'welcome',
        builder: (context, state) => const WelcomePage(),
      ),
      
      // User Type Selection Route
      GoRoute(
        path: '/user-type-selection',
        name: 'userTypeSelection',
        builder: (context, state) => const UserTypeSelectionPage(),
      ),
      
      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) {
          final userType = state.uri.queryParameters['userType'];
          final serviceType = state.uri.queryParameters['serviceType'];
          return LoginPage(
            userType: userType,
            serviceType: serviceType,
          );
        },
      ),
      
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) {
          final userType = state.uri.queryParameters['userType'];
          final serviceType = state.uri.queryParameters['serviceType'];
          return RegisterPage(
            userType: userType,
            serviceType: serviceType,
          );
        },
      ),

      // Forgot Password Route
      GoRoute(
        path: '/forgot-password',
        name: 'forgotPassword',
        builder: (context, state) => const ForgotPasswordPage(),
      ),
      
      // Main App Routes
      GoRoute(
        path: '/worker-dashboard',
        name: 'workerDashboard',
        builder: (context, state) => const WorkerDashboardPage(),
      ),

      GoRoute(
        path: '/customer-home',
        name: 'customerHome',
        builder: (context, state) => const CustomerHomePage(),
      ),

      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
      ),

      // Settings Route
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
      ),

      // Booking Routes
      GoRoute(
        path: '/booking/:providerId',
        name: 'booking',
        builder: (context, state) {
          final providerId = state.pathParameters['providerId']!;
          final serviceId = state.uri.queryParameters['serviceId'];
          return BookingPage(
            providerId: providerId,
            serviceId: serviceId,
          );
        },
      ),

      GoRoute(
        path: '/my-bookings',
        name: 'myBookings',
        builder: (context, state) => const MyBookingsPage(),
      ),

      // Payment Route
      GoRoute(
        path: '/payment',
        name: 'payment',
        builder: (context, state) {
          final bookingId = state.uri.queryParameters['bookingId'] ?? '';
          final amount = double.tryParse(state.uri.queryParameters['amount'] ?? '0') ?? 0.0;
          final providerId = state.uri.queryParameters['providerId'] ?? '';
          return PaymentPage(
            bookingId: bookingId,
            amount: amount,
            providerId: providerId,
          );
        },
      ),

      // Service Provider Details Route
      GoRoute(
        path: '/provider/:providerId',
        name: 'providerDetails',
        builder: (context, state) {
          final providerId = state.pathParameters['providerId']!;
          return ServiceProviderDetailsPage(providerId: providerId);
        },
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );

  static GoRouter get router => _router;
}

// Route Names for easy access
class AppRoutes {
  static const String splash = '/';
  static const String welcome = '/welcome';
  static const String userTypeSelection = '/user-type-selection';
  static const String login = '/login';
  static const String register = '/register';
  static const String home = '/home';
  static const String workerDashboard = '/worker-dashboard';
  static const String customerHome = '/customer-home';
}
