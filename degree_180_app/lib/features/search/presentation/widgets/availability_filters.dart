import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class AvailabilityFilters extends StatelessWidget {
  final bool isOpenNow;
  final bool hasAvailableSlots;
  final Function(bool) onOpenNowChanged;
  final Function(bool) onAvailableSlotsChanged;

  const AvailabilityFilters({
    super.key,
    required this.isOpenNow,
    required this.hasAvailableSlots,
    required this.onOpenNowChanged,
    required this.onAvailableSlotsChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CheckboxListTile(
          title: const Text('Open Now'),
          subtitle: const Text('Show only providers that are currently open'),
          value: isOpenNow,
          activeColor: AppColors.primaryPurple,
          onChanged: (value) => onOpenNowChanged(value ?? false),
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: const Text('Available Slots'),
          subtitle: const Text('Show only providers with available time slots'),
          value: hasAvailableSlots,
          activeColor: AppColors.primaryPurple,
          onChanged: (value) => onAvailableSlotsChanged(value ?? false),
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }
}
