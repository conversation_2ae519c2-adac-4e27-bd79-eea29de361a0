import 'package:equatable/equatable.dart';

class WorkingHoursModel extends Equatable {
  final Map<String, DaySchedule> schedule;
  final List<String> holidays;
  final Map<String, String>? specialHours; // Special hours for specific dates
  final String timezone;

  const WorkingHoursModel({
    required this.schedule,
    this.holidays = const [],
    this.specialHours,
    this.timezone = 'Asia/Kuwait',
  });

  factory WorkingHoursModel.fromJson(Map<String, dynamic> json) {
    final scheduleMap = <String, DaySchedule>{};
    final scheduleJson = json['schedule'] as Map<String, dynamic>? ?? {};
    
    for (final entry in scheduleJson.entries) {
      scheduleMap[entry.key] = DaySchedule.fromJson(entry.value);
    }

    return WorkingHoursModel(
      schedule: scheduleMap,
      holidays: List<String>.from(json['holidays'] ?? []),
      specialHours: json['specialHours'] != null 
          ? Map<String, String>.from(json['specialHours'])
          : null,
      timezone: json['timezone'] ?? 'Asia/Kuwait',
    );
  }

  Map<String, dynamic> toJson() {
    final scheduleJson = <String, dynamic>{};
    for (final entry in schedule.entries) {
      scheduleJson[entry.key] = entry.value.toJson();
    }

    return {
      'schedule': scheduleJson,
      'holidays': holidays,
      'specialHours': specialHours,
      'timezone': timezone,
    };
  }

  WorkingHoursModel copyWith({
    Map<String, DaySchedule>? schedule,
    List<String>? holidays,
    Map<String, String>? specialHours,
    String? timezone,
  }) {
    return WorkingHoursModel(
      schedule: schedule ?? this.schedule,
      holidays: holidays ?? this.holidays,
      specialHours: specialHours ?? this.specialHours,
      timezone: timezone ?? this.timezone,
    );
  }

  // Get schedule for a specific day
  DaySchedule? getScheduleForDay(String day) {
    return schedule[day.toLowerCase()];
  }

  // Check if open on a specific day
  bool isOpenOnDay(String day) {
    final daySchedule = getScheduleForDay(day);
    return daySchedule?.isOpen ?? false;
  }

  // Check if currently open
  bool isCurrentlyOpen() {
    final now = DateTime.now();
    final dayName = _getDayName(now.weekday);
    
    // Check if it's a holiday
    final dateString = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
    if (holidays.contains(dateString)) {
      return false;
    }

    // Check special hours
    if (specialHours?.containsKey(dateString) == true) {
      final specialHour = specialHours![dateString]!;
      if (specialHour.toLowerCase() == 'closed') {
        return false;
      }
      // Parse special hours format: "09:00-17:00"
      final parts = specialHour.split('-');
      if (parts.length == 2) {
        final openTime = _parseTime(parts[0]);
        final closeTime = _parseTime(parts[1]);
        final currentTime = now.hour * 60 + now.minute;
        return currentTime >= openTime && currentTime <= closeTime;
      }
    }

    // Check regular schedule
    final daySchedule = getScheduleForDay(dayName);
    if (daySchedule == null || !daySchedule.isOpen) {
      return false;
    }

    final currentTime = now.hour * 60 + now.minute;
    return currentTime >= daySchedule.openTimeMinutes && 
           currentTime <= daySchedule.closeTimeMinutes;
  }

  // Get next opening time
  DateTime? getNextOpeningTime() {
    final now = DateTime.now();
    
    for (int i = 0; i < 7; i++) {
      final checkDate = now.add(Duration(days: i));
      final dayName = _getDayName(checkDate.weekday);
      final daySchedule = getScheduleForDay(dayName);
      
      if (daySchedule != null && daySchedule.isOpen) {
        final openingDateTime = DateTime(
          checkDate.year,
          checkDate.month,
          checkDate.day,
          daySchedule.openTimeMinutes ~/ 60,
          daySchedule.openTimeMinutes % 60,
        );
        
        if (openingDateTime.isAfter(now)) {
          return openingDateTime;
        }
      }
    }
    
    return null;
  }

  String _getDayName(int weekday) {
    const days = [
      'monday', 'tuesday', 'wednesday', 'thursday', 
      'friday', 'saturday', 'sunday'
    ];
    return days[weekday - 1];
  }

  int _parseTime(String time) {
    final parts = time.split(':');
    if (parts.length == 2) {
      final hours = int.tryParse(parts[0]) ?? 0;
      final minutes = int.tryParse(parts[1]) ?? 0;
      return hours * 60 + minutes;
    }
    return 0;
  }

  @override
  List<Object?> get props => [schedule, holidays, specialHours, timezone];

  @override
  String toString() {
    return 'WorkingHoursModel(schedule: $schedule, timezone: $timezone)';
  }
}

class DaySchedule extends Equatable {
  final bool isOpen;
  final String? openTime; // Format: "09:00"
  final String? closeTime; // Format: "17:00"
  final List<BreakPeriod> breaks;

  const DaySchedule({
    required this.isOpen,
    this.openTime,
    this.closeTime,
    this.breaks = const [],
  });

  factory DaySchedule.fromJson(Map<String, dynamic> json) {
    return DaySchedule(
      isOpen: json['isOpen'] ?? false,
      openTime: json['openTime'],
      closeTime: json['closeTime'],
      breaks: (json['breaks'] as List<dynamic>?)
          ?.map((breakItem) => BreakPeriod.fromJson(breakItem))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isOpen': isOpen,
      'openTime': openTime,
      'closeTime': closeTime,
      'breaks': breaks.map((breakItem) => breakItem.toJson()).toList(),
    };
  }

  int get openTimeMinutes {
    if (openTime == null) return 0;
    final parts = openTime!.split(':');
    if (parts.length == 2) {
      final hours = int.tryParse(parts[0]) ?? 0;
      final minutes = int.tryParse(parts[1]) ?? 0;
      return hours * 60 + minutes;
    }
    return 0;
  }

  int get closeTimeMinutes {
    if (closeTime == null) return 0;
    final parts = closeTime!.split(':');
    if (parts.length == 2) {
      final hours = int.tryParse(parts[0]) ?? 0;
      final minutes = int.tryParse(parts[1]) ?? 0;
      return hours * 60 + minutes;
    }
    return 0;
  }

  String get formattedHours {
    if (!isOpen) return 'Closed';
    if (openTime == null || closeTime == null) return 'Hours not set';
    return '$openTime - $closeTime';
  }

  @override
  List<Object?> get props => [isOpen, openTime, closeTime, breaks];

  @override
  String toString() {
    return 'DaySchedule(isOpen: $isOpen, hours: $formattedHours)';
  }
}

class BreakPeriod extends Equatable {
  final String startTime;
  final String endTime;
  final String? description;

  const BreakPeriod({
    required this.startTime,
    required this.endTime,
    this.description,
  });

  factory BreakPeriod.fromJson(Map<String, dynamic> json) {
    return BreakPeriod(
      startTime: json['startTime'] ?? '',
      endTime: json['endTime'] ?? '',
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime,
      'endTime': endTime,
      'description': description,
    };
  }

  @override
  List<Object?> get props => [startTime, endTime, description];

  @override
  String toString() {
    return 'BreakPeriod($startTime - $endTime)';
  }
}
