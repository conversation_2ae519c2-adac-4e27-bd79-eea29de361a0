import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import 'rating_stars_widget.dart';

class ReviewCategoriesWidget extends StatelessWidget {
  final Map<String, double> categories;
  final Function(String, double) onRatingChanged;
  final bool isReadOnly;

  const ReviewCategoriesWidget({
    super.key,
    required this.categories,
    required this.onRatingChanged,
    this.isReadOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: categories.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _CategoryRatingItem(
            category: entry.key,
            rating: entry.value,
            onRatingChanged: (rating) => onRatingChanged(entry.key, rating),
            isReadOnly: isReadOnly,
          ),
        );
      }).toList(),
    );
  }
}

class _CategoryRatingItem extends StatelessWidget {
  final String category;
  final double rating;
  final Function(double) onRatingChanged;
  final bool isReadOnly;

  const _CategoryRatingItem({
    required this.category,
    required this.rating,
    required this.onRatingChanged,
    required this.isReadOnly,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                category,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _getRatingDescription(rating),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _getRatingColor(rating),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(width: 16),
        
        RatingStarsWidget(
          rating: rating,
          size: 24,
          isInteractive: !isReadOnly,
          onRatingChanged: isReadOnly ? null : onRatingChanged,
        ),
      ],
    );
  }

  String _getRatingDescription(double rating) {
    if (rating == 0) return 'Not rated';
    if (rating <= 1) return 'Poor';
    if (rating <= 2) return 'Fair';
    if (rating <= 3) return 'Good';
    if (rating <= 4) return 'Very Good';
    return 'Excellent';
  }

  Color _getRatingColor(double rating) {
    if (rating == 0) return AppColors.textLight;
    if (rating <= 2) return AppColors.error;
    if (rating <= 3) return AppColors.warning;
    return AppColors.success;
  }
}

// Compact category ratings display
class CompactCategoryRatingsWidget extends StatelessWidget {
  final Map<String, double> categories;
  final double maxWidth;

  const CompactCategoryRatingsWidget({
    super.key,
    required this.categories,
    this.maxWidth = 300,
  });

  @override
  Widget build(BuildContext context) {
    if (categories.isEmpty) return const SizedBox.shrink();

    return Container(
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: Wrap(
        spacing: 8,
        runSpacing: 4,
        children: categories.entries.map((entry) {
          return _CompactCategoryChip(
            category: entry.key,
            rating: entry.value,
          );
        }).toList(),
      ),
    );
  }
}

class _CompactCategoryChip extends StatelessWidget {
  final String category;
  final double rating;

  const _CompactCategoryChip({
    required this.category,
    required this.rating,
  });

  @override
  Widget build(BuildContext context) {
    final color = _getRatingColor(rating);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            category,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 4),
          Icon(
            Icons.star,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 2),
          Text(
            rating.toStringAsFixed(1),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getRatingColor(double rating) {
    if (rating <= 2) return AppColors.error;
    if (rating <= 3) return AppColors.warning;
    return AppColors.success;
  }
}

// Category averages display for provider details
class CategoryAveragesWidget extends StatelessWidget {
  final Map<String, double> categoryAverages;
  final int totalReviews;

  const CategoryAveragesWidget({
    super.key,
    required this.categoryAverages,
    required this.totalReviews,
  });

  @override
  Widget build(BuildContext context) {
    if (categoryAverages.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category Ratings',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...categoryAverages.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _CategoryAverageItem(
              category: entry.key,
              average: entry.value,
              totalReviews: totalReviews,
            ),
          );
        }).toList(),
      ],
    );
  }
}

class _CategoryAverageItem extends StatelessWidget {
  final String category;
  final double average;
  final int totalReviews;

  const _CategoryAverageItem({
    required this.category,
    required this.average,
    required this.totalReviews,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            category,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Progress bar
        Expanded(
          flex: 2,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    average.toStringAsFixed(1),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '5.0',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textLight,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: average / 5.0,
                backgroundColor: AppColors.borderGray,
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getRatingColor(average),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Stars
        RatingStarsWidget(
          rating: average,
          size: 16,
          isInteractive: false,
        ),
      ],
    );
  }

  Color _getRatingColor(double rating) {
    if (rating <= 2) return AppColors.error;
    if (rating <= 3) return AppColors.warning;
    return AppColors.success;
  }
}

// Predefined categories for different service types
class ReviewCategories {
  static const Map<String, List<String>> serviceCategories = {
    'salon': [
      'Service Quality',
      'Cleanliness',
      'Staff Friendliness',
      'Value for Money',
      'Timeliness',
    ],
    'spa': [
      'Relaxation Experience',
      'Treatment Quality',
      'Ambiance',
      'Staff Professionalism',
      'Cleanliness',
      'Value for Money',
    ],
    'barbershop': [
      'Haircut Quality',
      'Staff Skill',
      'Cleanliness',
      'Waiting Time',
      'Value for Money',
    ],
    'nails': [
      'Nail Art Quality',
      'Hygiene Standards',
      'Staff Expertise',
      'Product Quality',
      'Value for Money',
    ],
    'beauty': [
      'Service Quality',
      'Product Quality',
      'Staff Expertise',
      'Cleanliness',
      'Value for Money',
    ],
  };

  static List<String> getCategoriesForService(String serviceType) {
    return serviceCategories[serviceType.toLowerCase()] ?? 
           serviceCategories['salon']!;
  }

  static Map<String, double> getEmptyCategoriesMap(String serviceType) {
    final categories = getCategoriesForService(serviceType);
    return Map.fromEntries(
      categories.map((category) => MapEntry(category, 0.0)),
    );
  }
}
