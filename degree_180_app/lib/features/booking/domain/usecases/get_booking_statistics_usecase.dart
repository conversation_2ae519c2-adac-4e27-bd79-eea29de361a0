import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/models/booking_statistics.dart';
import '../repositories/booking_repository.dart';

class GetBookingStatisticsUseCase implements UseCase<BookingStatistics, GetBookingStatisticsParams> {
  final BookingRepository repository;

  GetBookingStatisticsUseCase(this.repository);

  @override
  Future<Either<Failure, BookingStatistics>> call(GetBookingStatisticsParams params) async {
    return await repository.getBookingStatistics(
      params.providerId,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

class GetBookingStatisticsParams {
  final String providerId;
  final DateTime? startDate;
  final DateTime? endDate;

  const GetBookingStatisticsParams({
    required this.providerId,
    this.startDate,
    this.endDate,
  });
}
