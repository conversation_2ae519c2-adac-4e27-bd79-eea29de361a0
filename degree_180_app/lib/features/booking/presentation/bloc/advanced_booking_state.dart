import 'package:equatable/equatable.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/models/booking_statistics.dart';
import '../../../../core/errors/failures.dart';

/// حالات الحجز المتقدم
abstract class AdvancedBookingState extends Equatable {
  const AdvancedBookingState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class AdvancedBookingInitial extends AdvancedBookingState {
  const AdvancedBookingInitial();
}

/// حالة التحميل
class AdvancedBookingLoading extends AdvancedBookingState {
  const AdvancedBookingLoading();
}

/// حالة تحميل الفترات الزمنية
class TimeSlotsLoading extends AdvancedBookingState {
  const TimeSlotsLoading();
}

/// حالة تحميل الفترات الزمنية بنجاح
class TimeSlotsLoaded extends AdvancedBookingState {
  final List<TimeSlotModel> timeSlots;
  final DateTime date;
  final String providerId;
  final String serviceId;

  const TimeSlotsLoaded({
    required this.timeSlots,
    required this.date,
    required this.providerId,
    required this.serviceId,
  });

  @override
  List<Object?> get props => [timeSlots, date, providerId, serviceId];
}

/// حالة إنشاء الحجز
class BookingCreating extends AdvancedBookingState {
  const BookingCreating();
}

/// حالة إنشاء الحجز بنجاح
class BookingCreated extends AdvancedBookingState {
  final BookingModel booking;

  const BookingCreated({
    required this.booking,
  });

  @override
  List<Object?> get props => [booking];
}

/// حالة إعادة جدولة الحجز
class BookingRescheduling extends AdvancedBookingState {
  const BookingRescheduling();
}

/// حالة إعادة جدولة الحجز بنجاح
class BookingRescheduled extends AdvancedBookingState {
  final BookingModel booking;

  const BookingRescheduled({
    required this.booking,
  });

  @override
  List<Object?> get props => [booking];
}

/// حالة إلغاء الحجز
class BookingCancelling extends AdvancedBookingState {
  const BookingCancelling();
}

/// حالة إلغاء الحجز بنجاح
class BookingCancelled extends AdvancedBookingState {
  final String bookingId;
  final String reason;

  const BookingCancelled({
    required this.bookingId,
    required this.reason,
  });

  @override
  List<Object?> get props => [bookingId, reason];
}

/// حالة تأكيد الحجز
class BookingConfirming extends AdvancedBookingState {
  const BookingConfirming();
}

/// حالة تأكيد الحجز بنجاح
class BookingConfirmed extends AdvancedBookingState {
  final BookingModel booking;

  const BookingConfirmed({
    required this.booking,
  });

  @override
  List<Object?> get props => [booking];
}

/// حالة بدء الخدمة
class ServiceStarting extends AdvancedBookingState {
  const ServiceStarting();
}

/// حالة بدء الخدمة بنجاح
class ServiceStarted extends AdvancedBookingState {
  final BookingModel booking;

  const ServiceStarted({
    required this.booking,
  });

  @override
  List<Object?> get props => [booking];
}

/// حالة إكمال الخدمة
class ServiceCompleting extends AdvancedBookingState {
  const ServiceCompleting();
}

/// حالة إكمال الخدمة بنجاح
class ServiceCompleted extends AdvancedBookingState {
  final BookingModel booking;

  const ServiceCompleted({
    required this.booking,
  });

  @override
  List<Object?> get props => [booking];
}

/// حالة تحميل الإحصائيات
class BookingStatisticsLoading extends AdvancedBookingState {
  const BookingStatisticsLoading();
}

/// حالة تحميل الإحصائيات بنجاح
class BookingStatisticsLoaded extends AdvancedBookingState {
  final BookingStatistics statistics;

  const BookingStatisticsLoaded({
    required this.statistics,
  });

  @override
  List<Object?> get props => [statistics];
}

/// حالة خطأ في تحميل الإحصائيات
class BookingStatisticsError extends AdvancedBookingState {
  final String message;

  const BookingStatisticsError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

/// حالة تحميل تاريخ الحجوزات
class BookingHistoryLoading extends AdvancedBookingState {
  const BookingHistoryLoading();
}

/// حالة تحميل تاريخ الحجوزات بنجاح
class BookingHistoryLoaded extends AdvancedBookingState {
  final List<BookingModel> bookings;
  final int totalCount;
  final int currentPage;
  final bool hasMore;

  const BookingHistoryLoaded({
    required this.bookings,
    required this.totalCount,
    required this.currentPage,
    required this.hasMore,
  });

  @override
  List<Object?> get props => [bookings, totalCount, currentPage, hasMore];
}

/// حالة البحث في الحجوزات
class BookingSearchLoading extends AdvancedBookingState {
  const BookingSearchLoading();
}

/// حالة البحث في الحجوزات بنجاح
class BookingSearchLoaded extends AdvancedBookingState {
  final List<BookingModel> bookings;
  final String query;

  const BookingSearchLoaded({
    required this.bookings,
    required this.query,
  });

  @override
  List<Object?> get props => [bookings, query];
}

/// حالة تحديث حالة الحجز
class BookingStatusUpdating extends AdvancedBookingState {
  const BookingStatusUpdating();
}

/// حالة تحديث حالة الحجز بنجاح
class BookingStatusUpdated extends AdvancedBookingState {
  final BookingModel booking;

  const BookingStatusUpdated({
    required this.booking,
  });

  @override
  List<Object?> get props => [booking];
}

/// حالة إرسال التذكير
class ReminderSending extends AdvancedBookingState {
  const ReminderSending();
}

/// حالة إرسال التذكير بنجاح
class ReminderSent extends AdvancedBookingState {
  final String bookingId;
  final String reminderType;

  const ReminderSent({
    required this.bookingId,
    required this.reminderType,
  });

  @override
  List<Object?> get props => [bookingId, reminderType];
}

/// حالة تقييم الخدمة
class ServiceRating extends AdvancedBookingState {
  const ServiceRating();
}

/// حالة تقييم الخدمة بنجاح
class ServiceRated extends AdvancedBookingState {
  final String bookingId;
  final double rating;

  const ServiceRated({
    required this.bookingId,
    required this.rating,
  });

  @override
  List<Object?> get props => [bookingId, rating];
}

/// حالة تحديث تفاصيل الحجز
class BookingDetailsUpdating extends AdvancedBookingState {
  const BookingDetailsUpdating();
}

/// حالة تحديث تفاصيل الحجز بنجاح
class BookingDetailsUpdated extends AdvancedBookingState {
  final BookingModel booking;

  const BookingDetailsUpdated({
    required this.booking,
  });

  @override
  List<Object?> get props => [booking];
}

/// حالة تحميل الحجوزات القادمة
class UpcomingBookingsLoading extends AdvancedBookingState {
  const UpcomingBookingsLoading();
}

/// حالة تحميل الحجوزات القادمة بنجاح
class UpcomingBookingsLoaded extends AdvancedBookingState {
  final List<BookingModel> bookings;
  final int days;

  const UpcomingBookingsLoaded({
    required this.bookings,
    required this.days,
  });

  @override
  List<Object?> get props => [bookings, days];
}

/// حالة الخطأ
class AdvancedBookingError extends AdvancedBookingState {
  final Failure failure;
  final String message;

  const AdvancedBookingError({
    required this.failure,
    required this.message,
  });

  @override
  List<Object?> get props => [failure, message];
}


