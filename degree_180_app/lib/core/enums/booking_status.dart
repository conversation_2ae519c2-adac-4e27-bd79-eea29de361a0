enum BookingStatus {
  pending,
  confirmed,
  inProgress,
  completed,
  cancelled,
  noShow,
}

extension BookingStatusExtension on BookingStatus {
  String get displayName {
    switch (this) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.inProgress:
        return 'In Progress';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.noShow:
        return 'No Show';
    }
  }

  String get displayNameAr {
    switch (this) {
      case BookingStatus.pending:
        return 'في الانتظار';
      case BookingStatus.confirmed:
        return 'مؤكد';
      case BookingStatus.inProgress:
        return 'قيد التنفيذ';
      case BookingStatus.completed:
        return 'مكتمل';
      case BookingStatus.cancelled:
        return 'ملغي';
      case BookingStatus.noShow:
        return 'لم يحضر';
    }
  }

  bool get canBeCancelled {
    return this == BookingStatus.pending || this == BookingStatus.confirmed;
  }

  bool get canBeModified {
    return this == BookingStatus.pending;
  }

  bool get isActive {
    return this == BookingStatus.pending || 
           this == BookingStatus.confirmed || 
           this == BookingStatus.inProgress;
  }

  bool get isCompleted {
    return this == BookingStatus.completed;
  }

  bool get isCancelled {
    return this == BookingStatus.cancelled || this == BookingStatus.noShow;
  }
}
