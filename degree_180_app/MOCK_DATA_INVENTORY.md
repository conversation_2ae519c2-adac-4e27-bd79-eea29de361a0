# 📋 جرد البيانات الوهمية والتجريبية - تطبيق "180 درجة"

## 🎯 الهدف
توثيق شامل لجميع البيانات الوهمية والتجريبية الموجودة في التطبيق لاستبدالها بنظام بيانات حقيقي.

## 🔍 البيانات الوهمية المحددة

### 1. 🏪 بيانات مقدمي الخدمات

#### الموقع: `lib/features/maps/presentation/widgets/provider_info_card.dart`
**الخطوط: 194-231**

```dart
final providers = {
  '1': {
    'name': '<PERSON> Barbershop',
    'type': 'barbershop',
    'rating': 4.8,
    'reviewCount': 127,
    'distance': '0.5 km',
    'isOpen': true,
  },
  '2': {
    'name': 'Fatima Beauty Salon',
    'type': 'salon',
    'rating': 4.9,
    'reviewCount': 89,
    'distance': '1.2 km',
    'isOpen': true,
  },
  '3': {
    'name': 'Luxury Spa Center',
    'type': 'spa',
    'rating': 4.7,
    'reviewCount': 156,
    'distance': '2.1 km',
    'isOpen': false,
  },
};
```

#### الموقع: `lib/features/home/<USER>/pages/customer_home_page.dart`
**الخطوط: 252-294**

```dart
final allProviders = [
  {
    'name': 'Ahmed Hassan',
    'serviceType': 'barbershop',
    'rating': 4.8,
    'reviewCount': 127,
    'imageUrl': null,
    'isAvailable': true,
  },
  {
    'name': 'Fatima Al-Zahra',
    'serviceType': 'salon',
    'rating': 4.9,
    'reviewCount': 89,
    'imageUrl': null,
    'isAvailable': true,
  },
  // ... المزيد من البيانات الوهمية
];
```

#### الموقع: `lib/features/maps/presentation/pages/map_page.dart`
**الخطوط: 152-185**

```dart
final providers = [
  {
    'id': '1',
    'name': 'Ahmed Hassan Barbershop',
    'type': 'barbershop',
    'rating': 4.8,
    'lat': 29.3759,
    'lng': 47.9774,
    'distance': '0.5 km',
    'isOpen': true,
  },
  // ... المزيد من البيانات
];
```

### 2. ⭐ بيانات المراجعات والتقييمات

#### الموقع: `lib/features/reviews/presentation/pages/reviews_page.dart`
**الخطوط: 247-262**

```dart
return [
  {
    'id': '1',
    'customerName': 'Sara Ahmed',
    'customerAvatar': null,
    'rating': 5.0,
    'date': DateTime.now().subtract(const Duration(days: 2)),
    'comment': 'Excellent service! Ahmed was very professional...',
    'images': ['image1.jpg', 'image2.jpg'],
    'isAnonymous': false,
    'helpfulCount': 12,
    'notHelpfulCount': 1,
    'providerResponse': 'Thank you Sara! It was a pleasure serving you.',
    'providerResponseDate': DateTime.now().subtract(const Duration(days: 1)),
  },
  // ... المزيد من المراجعات الوهمية
];
```

### 3. 🎁 بيانات العروض والخصومات

#### الموقع: `lib/features/promotions/presentation/pages/promotions_page.dart`
**الخطوط: 199-214**

```dart
return [
  {
    'id': '1',
    'title': 'New Customer Special',
    'description': 'Get 30% off your first booking with any service provider',
    'type': 'percentage',
    'value': 30.0,
    'status': 'active',
    'imageUrl': null,
    'couponCode': 'WELCOME30',
    'validUntil': DateTime.now().add(const Duration(days: 30)),
    'minOrderAmount': 20.0,
    'maxDiscountAmount': 15.0,
    'target': 'new_customers',
  },
  // ... المزيد من العروض الوهمية
];
```

### 4. 📅 بيانات الحجز والأسعار

#### الموقع: `lib/features/booking/presentation/widgets/booking_summary_card.dart`
**الخطوط: 232-258**

```dart
Map<String, String> _getTimeDetails(String? timeSlotId) {
  if (timeSlotId == null) {
    return {'time': 'Time not selected'};
  }
  
  // Extract time from slot ID (mock implementation)
  final parts = timeSlotId.split('_');
  if (parts.length >= 3) {
    final hour = parts[1].padLeft(2, '0');
    final minute = parts[2].padLeft(2, '0');
    return {'time': '$hour:$minute'};
  }
  
  return {'time': 'Invalid time'};
}

String _calculateTotal(String? servicePrice) {
  if (servicePrice == null) return '1.5 KD';
  
  final price = double.tryParse(servicePrice.replaceAll(' KD', '')) ?? 0;
  final tax = 1.5;
  final total = price + tax;
  
  return '${total.toStringAsFixed(1)} KD';
}
```

### 5. ⚙️ بيانات التكوين الوهمية

#### الموقع: `lib/app_config.dart`
**الخطوط: 1-87**

```dart
class AppConfig {
  // Environment
  static const bool isProduction = false;
  
  // API Configuration
  static const String baseUrl = 'https://api.degree180.com'; // وهمي
  
  // Firebase Configuration
  static const String firebaseProjectId = 'demo-project'; // وهمي
  
  // Location
  static const double defaultLatitude = 29.3759; // Kuwait City
  static const double defaultLongitude = 47.9774; // Kuwait City
  
  // Development
  static const bool enableTestMode = !isProduction;
}
```

## 📊 إحصائيات البيانات الوهمية

### حسب النوع:
- **مقدمو الخدمات**: 15+ عنصر وهمي
- **المراجعات**: 10+ مراجعة وهمية
- **العروض**: 5+ عرض وهمي
- **بيانات الحجز**: أسعار وأوقات ثابتة
- **إعدادات التكوين**: قيم تطوير وهمية

### حسب الملف:
- **provider_info_card.dart**: 3 مقدمي خدمات
- **customer_home_page.dart**: 5 مقدمي خدمات
- **map_page.dart**: 3 مقدمي خدمات
- **reviews_page.dart**: 5+ مراجعات
- **promotions_page.dart**: 3+ عروض
- **booking_summary_card.dart**: حسابات وهمية
- **app_config.dart**: إعدادات تطوير

## 🔄 خطة الاستبدال

### المرحلة الأولى: إنشاء نماذج البيانات الحقيقية

#### 1. نموذج مقدم الخدمة
```dart
class ServiceProviderModel {
  final String id;
  final String name;
  final String email;
  final String phoneNumber;
  final ServiceType serviceType;
  final String description;
  final String profileImageUrl;
  final List<String> galleryImages;
  final LocationModel location;
  final double rating;
  final int reviewCount;
  final bool isActive;
  final bool isVerified;
  final WorkingHoursModel workingHours;
  final List<ServiceModel> services;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

#### 2. نموذج الخدمة
```dart
class ServiceModel {
  final String id;
  final String name;
  final String description;
  final double price;
  final int durationMinutes;
  final String category;
  final List<String> images;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

#### 3. نموذج المراجعة
```dart
class ReviewModel {
  final String id;
  final String customerId;
  final String customerName;
  final String customerAvatar;
  final String providerId;
  final String bookingId;
  final double rating;
  final String comment;
  final List<String> images;
  final bool isAnonymous;
  final int helpfulCount;
  final int notHelpfulCount;
  final String? providerResponse;
  final DateTime? providerResponseDate;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

#### 4. نموذج العرض
```dart
class PromotionModel {
  final String id;
  final String title;
  final String description;
  final PromotionType type;
  final double value;
  final String? couponCode;
  final double? minOrderAmount;
  final double? maxDiscountAmount;
  final DateTime validFrom;
  final DateTime validUntil;
  final PromotionTarget target;
  final List<String> applicableServices;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

### المرحلة الثانية: إنشاء Firebase Collections

#### 1. هيكل قاعدة البيانات
```
firestore/
├── users/
│   └── {userId}/
│       ├── profile/
│       ├── bookings/
│       └── favorites/
├── service_providers/
│   └── {providerId}/
│       ├── profile/
│       ├── services/
│       ├── working_hours/
│       ├── availability/
│       └── gallery/
├── services/
│   └── {serviceId}/
├── bookings/
│   └── {bookingId}/
├── reviews/
│   └── {reviewId}/
├── promotions/
│   └── {promotionId}/
└── categories/
    └── {categoryId}/
```

#### 2. Firebase Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Service providers can manage their own data
    match /service_providers/{providerId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == providerId;
    }
    
    // Reviews are readable by all, writable by authenticated users
    match /reviews/{reviewId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.customerId;
    }
  }
}
```

### المرحلة الثالثة: تطوير Repository Layer

#### 1. Service Provider Repository
```dart
abstract class ServiceProviderRepository {
  Future<Either<Failure, List<ServiceProviderModel>>> getServiceProviders({
    ServiceType? serviceType,
    LocationModel? location,
    double? radius,
    int? limit,
  });
  
  Future<Either<Failure, ServiceProviderModel>> getServiceProviderById(String id);
  
  Future<Either<Failure, List<ServiceModel>>> getProviderServices(String providerId);
  
  Future<Either<Failure, List<ReviewModel>>> getProviderReviews(String providerId);
}
```

#### 2. Booking Repository
```dart
abstract class BookingRepository {
  Future<Either<Failure, BookingModel>> createBooking(CreateBookingParams params);
  
  Future<Either<Failure, List<BookingModel>>> getUserBookings(String userId);
  
  Future<Either<Failure, List<TimeSlotModel>>> getAvailableTimeSlots({
    required String providerId,
    required DateTime date,
  });
  
  Future<Either<Failure, BookingModel>> updateBookingStatus({
    required String bookingId,
    required BookingStatus status,
  });
}
```

## 🎯 الأولويات

### أولوية قصوى:
1. **إنشاء نماذج البيانات الأساسية**
2. **إعداد Firebase Collections**
3. **تطوير Repository Layer للمصادقة**
4. **استبدال بيانات مقدمي الخدمات الوهمية**

### أولوية عالية:
1. **تطوير نظام الحجز الحقيقي**
2. **إنشاء نظام المراجعات الفعلي**
3. **تطوير نظام العروض والخصومات**
4. **إعداد Firebase Security Rules**

### أولوية متوسطة:
1. **تحسين نظام البحث والفلترة**
2. **إضافة نظام التخزين المؤقت**
3. **تطوير نظام الإشعارات**
4. **إضافة نظام التحليلات**

## 📈 مؤشرات التقدم

### مؤشرات الإنجاز:
- [ ] إنشاء جميع نماذج البيانات (0/8)
- [ ] إعداد Firebase Collections (0/7)
- [ ] تطوير Repository Layer (0/5)
- [ ] استبدال البيانات الوهمية (0/6 ملفات)
- [ ] إضافة اختبارات للبيانات الجديدة (0/10)

### مؤشرات الجودة:
- **Code Coverage**: هدف 85%+
- **Performance**: تحسين سرعة التحميل بنسبة 40%+
- **User Experience**: تقليل أخطاء البيانات بنسبة 90%+
- **Maintainability**: تحسين قابلية الصيانة بنسبة 60%+

## 🏁 الخلاصة

تم تحديد 6 ملفات رئيسية تحتوي على بيانات وهمية يجب استبدالها بنظام بيانات حقيقي. الأولوية القصوى هي إنشاء نماذج البيانات وإعداد Firebase، تليها تطوير Repository Layer واستبدال البيانات الوهمية تدريجياً.

التنفيذ السليم لهذه الخطة سيحول التطبيق من نموذج أولي إلى تطبيق إنتاج جاهز للاستخدام الفعلي.
