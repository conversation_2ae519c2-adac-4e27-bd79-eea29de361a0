import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/models/booking_model.dart';
import '../repositories/booking_repository.dart';

class RescheduleBookingUseCase implements UseCase<BookingModel, RescheduleBookingParams> {
  final BookingRepository repository;

  RescheduleBookingUseCase(this.repository);

  @override
  Future<Either<Failure, BookingModel>> call(RescheduleBookingParams params) async {
    return await repository.rescheduleBooking(
      params.bookingId,
      params.newDate,
      params.newTimeSlot,
      params.reason,
    );
  }
}

class RescheduleBookingParams {
  final String bookingId;
  final DateTime newDate;
  final String newTimeSlot;
  final String? reason;

  const RescheduleBookingParams({
    required this.bookingId,
    required this.newDate,
    required this.newTimeSlot,
    this.reason,
  });
}
