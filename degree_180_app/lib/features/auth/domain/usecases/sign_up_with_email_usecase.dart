import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

class SignUpWithEmailUseCase implements UseCase<UserEntity, SignUpWithEmailParams> {
  final AuthRepository repository;

  SignUpWithEmailUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(SignUpWithEmailParams params) async {
    return await repository.signUpWithEmailAndPassword(
      email: params.email,
      password: params.password,
      name: params.name,
      userType: params.userType,
      serviceType: params.serviceType,
    );
  }
}

class SignUpWithEmailParams extends Equatable {
  final String email;
  final String password;
  final String name;
  final UserType userType;
  final ServiceType? serviceType;

  const SignUpWithEmailParams({
    required this.email,
    required this.password,
    required this.name,
    required this.userType,
    this.serviceType,
  });

  @override
  List<Object?> get props => [email, password, name, userType, serviceType];
}
