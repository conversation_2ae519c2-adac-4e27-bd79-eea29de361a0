import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/models/service_model.dart';
import '../../../../core/models/review_model.dart';
import '../../../../core/models/location_model.dart';
import '../../../../core/enums/service_type.dart';

abstract class ServiceProviderRemoteDataSource {
  Future<List<ServiceProviderModel>> getServiceProviders({
    ServiceType? serviceType,
    LocationModel? userLocation,
    double? radiusKm,
    int? limit,
    String? lastDocumentId,
    bool? isVerified,
    bool? isOnline,
    double? minRating,
  });

  Future<ServiceProviderModel> getServiceProviderById(String providerId);
  Future<List<ServiceModel>> getProviderServices(String providerId, {bool? isActive, String? category});
  Future<List<ReviewModel>> getProviderReviews(String providerId, {int? limit, String? lastDocumentId, double? minRating});
  Future<List<ServiceProviderModel>> searchServiceProviders(String query, {ServiceType? serviceType, LocationModel? userLocation, double? radiusKm, int? limit});
  Future<List<ServiceProviderModel>> getNearbyProviders(LocationModel userLocation, {double radiusKm = 10.0, ServiceType? serviceType, int? limit});
  Future<List<ServiceProviderModel>> getFeaturedProviders({ServiceType? serviceType, int? limit});
  Future<List<String>> getProviderAvailability(String providerId, DateTime date);
  Future<void> updateProviderOnlineStatus(String providerId, bool isOnline);
  Future<ServiceModel> addProviderService(String providerId, ServiceModel service);
  Future<ServiceModel> updateProviderService(String providerId, ServiceModel service);
  Future<void> deleteProviderService(String providerId, String serviceId);
  Future<Map<String, dynamic>> getProviderStatistics(String providerId);
  Future<ServiceProviderModel> updateProviderProfile(ServiceProviderModel provider);
  Future<List<String>> uploadProviderGallery(String providerId, List<String> imagePaths);
  Future<void> deleteProviderGalleryImage(String providerId, String imageUrl);
}

class ServiceProviderRemoteDataSourceImpl implements ServiceProviderRemoteDataSource {
  final FirebaseFirestore firestore;
  final FirebaseStorage storage;

  ServiceProviderRemoteDataSourceImpl({
    required this.firestore,
    required this.storage,
  });

  @override
  Future<List<ServiceProviderModel>> getServiceProviders({
    ServiceType? serviceType,
    LocationModel? userLocation,
    double? radiusKm,
    int? limit,
    String? lastDocumentId,
    bool? isVerified,
    bool? isOnline,
    double? minRating,
  }) async {
    try {
      Query query = firestore.collection('service_providers');

      // Apply filters
      if (serviceType != null) {
        query = query.where('serviceType', isEqualTo: serviceType.toString().split('.').last);
      }
      if (isVerified != null) {
        query = query.where('isVerified', isEqualTo: isVerified);
      }
      if (isOnline != null) {
        query = query.where('isOnline', isEqualTo: isOnline);
      }
      if (minRating != null) {
        query = query.where('rating', isGreaterThanOrEqualTo: minRating);
      }

      // Apply pagination
      if (limit != null) {
        query = query.limit(limit);
      }
      if (lastDocumentId != null) {
        final lastDoc = await firestore.collection('service_providers').doc(lastDocumentId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      // Order by rating (descending)
      query = query.orderBy('rating', descending: true);

      final querySnapshot = await query.get();
      final providers = querySnapshot.docs
          .map((doc) => ServiceProviderModel.fromFirestore(doc))
          .toList();

      // Apply location-based filtering if needed
      if (userLocation != null && radiusKm != null) {
        return providers.where((provider) {
          if (provider.location == null) return false;
          final distance = userLocation.distanceTo(provider.location!);
          return distance <= radiusKm;
        }).toList();
      }

      return providers;
    } catch (e) {
      throw ServerException(message: 'Failed to fetch service providers');
    }
  }

  @override
  Future<ServiceProviderModel> getServiceProviderById(String providerId) async {
    try {
      final doc = await firestore.collection('service_providers').doc(providerId).get();
      if (!doc.exists) {
        throw ServerException(message: "Server error");
      }
      return ServiceProviderModel.fromFirestore(doc);
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<ServiceModel>> getProviderServices(
    String providerId, {
    bool? isActive,
    String? category,
  }) async {
    try {
      Query query = firestore
          .collection('service_providers')
          .doc(providerId)
          .collection('services');

      if (isActive != null) {
        query = query.where('isActive', isEqualTo: isActive);
      }
      if (category != null) {
        query = query.where('category', isEqualTo: category);
      }

      query = query.orderBy('name');

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => ServiceModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<ReviewModel>> getProviderReviews(
    String providerId, {
    int? limit,
    String? lastDocumentId,
    double? minRating,
  }) async {
    try {
      Query query = firestore
          .collection('reviews')
          .where('providerId', isEqualTo: providerId);

      if (minRating != null) {
        query = query.where('overallRating', isGreaterThanOrEqualTo: minRating);
      }

      query = query.orderBy('createdAt', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }
      if (lastDocumentId != null) {
        final lastDoc = await firestore.collection('reviews').doc(lastDocumentId).get();
        if (lastDoc.exists) {
          query = query.startAfterDocument(lastDoc);
        }
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => ReviewModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<ServiceProviderModel>> searchServiceProviders(
    String query, {
    ServiceType? serviceType,
    LocationModel? userLocation,
    double? radiusKm,
    int? limit,
  }) async {
    try {
      Query firestoreQuery = firestore.collection('service_providers');

      if (serviceType != null) {
        firestoreQuery = firestoreQuery.where('serviceType', isEqualTo: serviceType.toString().split('.').last);
      }

      // For text search, we'll use array-contains for searchable keywords
      // In a real implementation, you might want to use Algolia or similar
      firestoreQuery = firestoreQuery.where('searchKeywords', arrayContains: query.toLowerCase());

      if (limit != null) {
        firestoreQuery = firestoreQuery.limit(limit);
      }

      final querySnapshot = await firestoreQuery.get();
      final providers = querySnapshot.docs
          .map((doc) => ServiceProviderModel.fromFirestore(doc))
          .toList();

      // Apply location-based filtering if needed
      if (userLocation != null && radiusKm != null) {
        return providers.where((provider) {
          if (provider.location == null) return false;
          final distance = userLocation.distanceTo(provider.location!);
          return distance <= radiusKm;
        }).toList();
      }

      return providers;
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<ServiceProviderModel>> getNearbyProviders(
    LocationModel userLocation, {
    double radiusKm = 10.0,
    ServiceType? serviceType,
    int? limit,
  }) async {
    try {
      Query query = firestore.collection('service_providers');

      if (serviceType != null) {
        query = query.where('serviceType', isEqualTo: serviceType.toString().split('.').last);
      }

      query = query.where('isActive', isEqualTo: true);

      if (limit != null) {
        query = query.limit(limit * 2); // Get more to filter by distance
      }

      final querySnapshot = await query.get();
      final allProviders = querySnapshot.docs
          .map((doc) => ServiceProviderModel.fromFirestore(doc))
          .toList();

      // Filter by distance
      final nearbyProviders = allProviders.where((provider) {
        if (provider.location == null) return false;
        final distance = userLocation.distanceTo(provider.location!);
        return distance <= radiusKm;
      }).toList();

      // Sort by distance
      nearbyProviders.sort((a, b) {
        final distanceA = userLocation.distanceTo(a.location!);
        final distanceB = userLocation.distanceTo(b.location!);
        return distanceA.compareTo(distanceB);
      });

      return limit != null ? nearbyProviders.take(limit).toList() : nearbyProviders;
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<ServiceProviderModel>> getFeaturedProviders({
    ServiceType? serviceType,
    int? limit,
  }) async {
    try {
      Query query = firestore.collection('service_providers');

      if (serviceType != null) {
        query = query.where('serviceType', isEqualTo: serviceType.toString().split('.').last);
      }

      query = query
          .where('isVerified', isEqualTo: true)
          .where('isActive', isEqualTo: true)
          .orderBy('rating', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => ServiceProviderModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<String>> getProviderAvailability(String providerId, DateTime date) async {
    try {
      final dateString = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      
      final availabilityDoc = await firestore
          .collection('service_providers')
          .doc(providerId)
          .collection('availability')
          .doc(dateString)
          .get();

      if (availabilityDoc.exists) {
        final data = availabilityDoc.data()!;
        return List<String>.from(data['availableSlots'] ?? []);
      }

      // Return default time slots if no specific availability is set
      return _generateDefaultTimeSlots();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  List<String> _generateDefaultTimeSlots() {
    final slots = <String>[];
    for (int hour = 9; hour < 18; hour++) {
      slots.add('${hour.toString().padLeft(2, '0')}:00 - ${(hour + 1).toString().padLeft(2, '0')}:00');
    }
    return slots;
  }

  @override
  Future<void> updateProviderOnlineStatus(String providerId, bool isOnline) async {
    try {
      await firestore.collection('service_providers').doc(providerId).update({
        'isOnline': isOnline,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<ServiceModel> addProviderService(String providerId, ServiceModel service) async {
    try {
      final docRef = await firestore
          .collection('service_providers')
          .doc(providerId)
          .collection('services')
          .add(service.toFirestore());

      final newService = service.copyWith(id: docRef.id);
      await docRef.update({'id': docRef.id});

      return newService;
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<ServiceModel> updateProviderService(String providerId, ServiceModel service) async {
    try {
      await firestore
          .collection('service_providers')
          .doc(providerId)
          .collection('services')
          .doc(service.id)
          .update(service.toFirestore());

      return service;
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<void> deleteProviderService(String providerId, String serviceId) async {
    try {
      await firestore
          .collection('service_providers')
          .doc(providerId)
          .collection('services')
          .doc(serviceId)
          .delete();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<Map<String, dynamic>> getProviderStatistics(String providerId) async {
    try {
      // Get bookings statistics
      final bookingsQuery = await firestore
          .collection('bookings')
          .where('providerId', isEqualTo: providerId)
          .get();

      final totalBookings = bookingsQuery.docs.length;
      final completedBookings = bookingsQuery.docs
          .where((doc) => doc.data()['status'] == 'completed')
          .length;

      // Get reviews statistics
      final reviewsQuery = await firestore
          .collection('reviews')
          .where('providerId', isEqualTo: providerId)
          .get();

      final totalReviews = reviewsQuery.docs.length;
      final averageRating = totalReviews > 0
          ? reviewsQuery.docs
                  .map((doc) => (doc.data()['overallRating'] as num).toDouble())
                  .reduce((a, b) => a + b) /
              totalReviews
          : 0.0;

      return {
        'totalBookings': totalBookings,
        'completedBookings': completedBookings,
        'totalReviews': totalReviews,
        'averageRating': averageRating,
        'completionRate': totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0.0,
      };
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<ServiceProviderModel> updateProviderProfile(ServiceProviderModel provider) async {
    try {
      await firestore
          .collection('service_providers')
          .doc(provider.id)
          .update(provider.toFirestore());

      return provider;
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<List<String>> uploadProviderGallery(String providerId, List<String> imagePaths) async {
    try {
      final imageUrls = <String>[];

      for (final imagePath in imagePaths) {
        final file = File(imagePath);
        final fileName = '${DateTime.now().millisecondsSinceEpoch}_${imagePath.split('/').last}';
        final ref = storage.ref().child('providers/$providerId/gallery/$fileName');

        final uploadTask = await ref.putFile(file);
        final downloadUrl = await uploadTask.ref.getDownloadURL();
        imageUrls.add(downloadUrl);
      }

      // Update provider's gallery images
      await firestore.collection('service_providers').doc(providerId).update({
        'galleryImages': FieldValue.arrayUnion(imageUrls),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return imageUrls;
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }

  @override
  Future<void> deleteProviderGalleryImage(String providerId, String imageUrl) async {
    try {
      // Remove from Firestore
      await firestore.collection('service_providers').doc(providerId).update({
        'galleryImages': FieldValue.arrayRemove([imageUrl]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Delete from Storage
      final ref = storage.refFromURL(imageUrl);
      await ref.delete();
    } catch (e) {
      throw ServerException(message: "Server error");
    }
  }
}
