name: degree_180_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  firebase_messaging: ^15.1.3
  firebase_analytics: ^11.3.3
  firebase_crashlytics: ^4.1.3
  flutter_local_notifications: ^17.2.3
  permission_handler: ^11.3.1
  timezone: ^0.9.4
  crypto: ^3.0.3
  encrypt: ^5.0.3
  url_launcher: ^6.3.1

  # Charts and Analytics
  fl_chart: ^0.68.0

  # Network and Connectivity
  connectivity_plus: ^6.0.5

  flutter_localizations:
    sdk: flutter
  google_fonts: ^6.2.1

  # Authentication
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.2

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5

  # Navigation
  go_router: ^14.6.1

  # Network & API
  dio: ^5.7.0

  # Local Storage
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Image & Media
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1

  # Utils
  intl: ^0.19.0
  uuid: ^4.5.1
  dartz: ^0.10.1
  get_it: ^7.7.0

  # Location & Maps
  geolocator: ^13.0.1
  geocoding: ^3.0.0
  google_maps_flutter: ^2.6.1

  # Payment
  flutter_stripe: ^10.1.1
  pay: ^2.0.0

  # UI Components
  flutter_svg: ^2.0.10+1
  shimmer: ^3.0.0
  lottie: ^3.1.2
  table_calendar: ^3.0.9

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting & Code Quality
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  hive_generator: ^2.0.1
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0

  # Testing
  bloc_test: ^9.1.7
  mocktail: ^1.0.4
  mockito: ^5.4.4
  fake_async: ^1.3.1
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
