class AppStrings {
  // App Info
  static const String appName = '180 درجة';
  static const String appNameEn = '180 Degree';

  // Welcome & Intro
  static const String intro = 'مقدمة';
  static const String welcomeTo = 'مرحباً بك في';
  static const String appSlogan = 'التطبيق الوحيد\nلصالونات الحلاقة\nوالتجميل\nومراكز السبا';
  static const String enter = 'دخول';

  // Authentication
  static const String createAccount = 'إنشاء حساب جديد';
  static const String signUp = 'تسجيل';
  static const String signIn = 'تسجيل الدخول';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String confirmPassword = 'تأكيد كلمة المرور';
  static const String emailUsernameOrNumber = 'البريد الإلكتروني / اسم المستخدم أو رقم الهاتف';
  static const String forgetPassword = 'نسيت كلمة المرور؟';
  static const String alreadyHaveAccount = 'لديك حساب بالفعل؟';
  static const String or = 'أو';
  static const String signInWithGoogle = 'تسجيل الدخول بجوجل';
  static const String signInWithApple = 'تسجيل الدخول بآبل';
  static const String signInWithAppGallery = 'تسجيل الدخول بـ AppGallery';
  static const String dontHaveAccount = 'ليس لديك حساب؟';
  static const String resetPassword = 'إعادة تعيين كلمة المرور';
  static const String sendResetLink = 'إرسال رابط الإعادة';
  static const String backToLogin = 'العودة لتسجيل الدخول';
  static const String firstName = 'الاسم الأول';
  static const String lastName = 'الاسم الأخير';
  static const String fullName = 'الاسم الكامل';
  static const String phoneNumber = 'رقم الهاتف';
  static const String gender = 'الجنس';
  static const String male = 'ذكر';
  static const String female = 'أنثى';
  static const String agreeToTerms = 'أوافق على الشروط والأحكام';
  static const String termsAndConditions = 'الشروط والأحكام';
  static const String privacyPolicy = 'سياسة الخصوصية';
  
  // User Types
  static const String salon = 'Salon';
  static const String salonAr = 'صالون';
  static const String barbershop = 'Barbershop';
  static const String barbershopAr = 'حلاق';
  static const String spa = 'SPA';
  static const String spaAr = 'معهد';
  static const String worker = 'Worker';
  static const String workerAr = 'موظف';
  static const String freelancer = 'Freelancer';
  static const String freelancerAr = 'مستقل';
  static const String customer = 'Customer';
  static const String customerAr = 'عميل';
  
  // Profile
  static const String profile = 'الملف الشخصي';
  static const String editProfile = 'تعديل الملف الشخصي';
  static const String save = 'حفظ';
  static const String name = 'الاسم';
  static const String paymentMethod = 'طريقة الدفع';
  static const String notification = 'الإشعارات';
  static const String general = 'عام';
  static const String quickLogin = 'تسجيل دخول سريع';
  static const String rateApp = 'تقييم التطبيق';
  static const String changePassword = 'تغيير كلمة المرور';
  static const String changeLocation = 'تغيير الموقع';
  static const String defaultProfile = 'الملف الافتراضي';
  
  // Worker Dashboard
  static const String personalInfo = 'Personal info';
  static const String shift = 'Shift';
  static const String from = 'From';
  static const String to = 'To';
  static const String jobDuration = 'Job Duration';
  static const String jobDetails = 'Job Details';
  static const String finished = 'Finished';
  static const String canceled = 'Canceled';
  static const String requested = 'Requested';
  static const String overTime = 'Over time';
  static const String extraTime = 'Extra Time';
  static const String total = 'Total';
  static const String chats = 'Chats';
  static const String inbox = 'inbox';
  static const String tools = 'Tools';
  static const String product = 'Product';
  static const String homeService = 'Home\nService';
  static const String jobs = 'Jobs';
  static const String location = 'Location';
  static const String exp = 'EXP';
  static const String native = 'Native';
  static const String skill = 'Skill';
  static const String offer = 'Offer';
  
  // Customer
  static const String gift = 'Gift';
  static const String friends = 'Friends';
  
  // Common
  static const String back = 'Back';
  static const String next = 'Next';
  static const String cancel = 'Cancel';
  static const String confirm = 'Confirm';
  static const String ok = 'OK';
  static const String yes = 'Yes';
  static const String no = 'No';
  static const String loading = 'Loading...';
  static const String error = 'Error';
  static const String success = 'Success';
  static const String warning = 'Warning';
  static const String info = 'Info';
  
  // Time
  static const String am = 'AM';
  static const String pm = 'PM';
  static const String today = 'Today';
  static const String yesterday = 'Yesterday';
  static const String tomorrow = 'Tomorrow';
  
  // Ratings
  static const String rated = 'Rated';
  static const String rating = 'Rating';
  static const String reviews = 'Reviews';
  
  // Status
  static const String busy = 'Busy';
  static const String available = 'Available';
  static const String offline = 'Offline';
  static const String online = 'Online';
  
  // Messages
  static const String thankYou = 'Thanks';
  static const String mightNeedToCutHair = 'I might need to cut my hair tonight';
  
  // Validation Messages
  static const String fieldRequired = 'هذا الحقل مطلوب';
  static const String invalidEmail = 'يرجى إدخال بريد إلكتروني صحيح';
  static const String passwordTooShort = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
  static const String passwordsDoNotMatch = 'كلمات المرور غير متطابقة';
  static const String invalidPhoneNumber = 'يرجى إدخال رقم هاتف صحيح';
  static const String nameRequired = 'الاسم مطلوب';
  static const String emailRequired = 'البريد الإلكتروني مطلوب';
  static const String passwordRequired = 'كلمة المرور مطلوبة';
  static const String phoneRequired = 'رقم الهاتف مطلوب';
  static const String genderRequired = 'يرجى اختيار الجنس';
  static const String termsRequired = 'يجب الموافقة على الشروط والأحكام';

  // Error Messages
  static const String networkError = 'خطأ في الشبكة. يرجى التحقق من الاتصال.';
  static const String serverError = 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
  static const String unknownError = 'حدث خطأ غير معروف.';
  static const String loginFailed = 'فشل تسجيل الدخول. يرجى التحقق من البيانات.';
  static const String registrationFailed = 'فشل التسجيل. يرجى المحاولة مرة أخرى.';
  static const String emailAlreadyExists = 'البريد الإلكتروني مستخدم بالفعل';
  static const String weakPassword = 'كلمة المرور ضعيفة جداً';
  static const String userNotFound = 'المستخدم غير موجود';
  static const String wrongPassword = 'كلمة المرور غير صحيحة';

  // Success Messages
  static const String loginSuccess = 'تم تسجيل الدخول بنجاح';
  static const String registrationSuccess = 'تم التسجيل بنجاح';
  static const String passwordResetSent = 'تم إرسال رابط إعادة تعيين كلمة المرور';
  static const String profileUpdated = 'تم تحديث الملف الشخصي بنجاح';
}
