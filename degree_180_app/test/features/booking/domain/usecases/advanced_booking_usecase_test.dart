import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';
import 'package:degree_180_app/core/errors/failures.dart';
import 'package:degree_180_app/core/models/booking_model.dart';
import 'package:degree_180_app/core/models/service_model.dart';
import 'package:degree_180_app/core/models/time_slot_model.dart';
import 'package:degree_180_app/core/enums/booking_status.dart';
import 'package:degree_180_app/features/booking/domain/repositories/booking_repository.dart';
import 'package:degree_180_app/features/booking/domain/usecases/advanced_booking_usecase.dart';

import 'advanced_booking_usecase_test.mocks.dart';

@GenerateMocks([BookingRepository])
void main() {
  group('AdvancedBookingUseCase', () {
    late MockBookingRepository mockRepository;
    late GetAvailableTimeSlotsUseCase getAvailableTimeSlotsUseCase;
    late CreateAdvancedBookingUseCase createAdvancedBookingUseCase;
    late RescheduleBookingUseCase rescheduleBookingUseCase;
    late GetBookingStatisticsUseCase getBookingStatisticsUseCase;

    setUp(() {
      mockRepository = MockBookingRepository();
      getAvailableTimeSlotsUseCase = GetAvailableTimeSlotsUseCase(mockRepository);
      createAdvancedBookingUseCase = CreateAdvancedBookingUseCase(mockRepository);
      rescheduleBookingUseCase = RescheduleBookingUseCase(mockRepository);
      getBookingStatisticsUseCase = GetBookingStatisticsUseCase(mockRepository);
    });

    group('GetAvailableTimeSlotsUseCase', () {
      test('should return list of available time slots when repository call is successful', () async {
        // Arrange
        const providerId = 'provider-1';
        final date = DateTime(2024, 12, 25);
        const serviceDurationMinutes = 60;
        
        final timeSlots = [
          TimeSlotModel(
            id: 'slot-1',
            startTime: DateTime(2024, 12, 25, 9, 0),
            endTime: DateTime(2024, 12, 25, 10, 0),
            isAvailable: true,
            durationMinutes: 60,
          ),
          TimeSlotModel(
            id: 'slot-2',
            startTime: DateTime(2024, 12, 25, 10, 0),
            endTime: DateTime(2024, 12, 25, 11, 0),
            isAvailable: true,
            durationMinutes: 60,
          ),
        ];

        when(mockRepository.getAvailableTimeSlots(
          providerId,
          date,
          serviceDurationMinutes,
        )).thenAnswer((_) async => Right(timeSlots));

        // Act
        final result = await getAvailableTimeSlotsUseCase(
          GetAvailableTimeSlotsParams(
            providerId: providerId,
            date: date,
            serviceDurationMinutes: serviceDurationMinutes,
          ),
        );

        // Assert
        expect(result, isA<Right<Failure, List<TimeSlotModel>>>());
        result.fold(
          (failure) => fail('Expected Right but got Left'),
          (slots) {
            expect(slots, hasLength(2));
            expect(slots[0].id, 'slot-1');
            expect(slots[0].isAvailable, true);
            expect(slots[1].id, 'slot-2');
            expect(slots[1].isAvailable, true);
          },
        );

        verify(mockRepository.getAvailableTimeSlots(
          providerId,
          date,
          serviceDurationMinutes,
        )).called(1);
      });

      test('should return failure when repository call fails', () async {
        // Arrange
        const providerId = 'provider-1';
        final date = DateTime(2024, 12, 25);
        const serviceDurationMinutes = 60;
        const failure = ServerFailure(message: 'Server error');

        when(mockRepository.getAvailableTimeSlots(
          providerId,
          date,
          serviceDurationMinutes,
        )).thenAnswer((_) async => const Left(failure));

        // Act
        final result = await getAvailableTimeSlotsUseCase(
          GetAvailableTimeSlotsParams(
            providerId: providerId,
            date: date,
            serviceDurationMinutes: serviceDurationMinutes,
          ),
        );

        // Assert
        expect(result, isA<Left<Failure, List<TimeSlotModel>>>());
        result.fold(
          (f) => expect(f, failure),
          (slots) => fail('Expected Left but got Right'),
        );
      });
    });

    group('CreateAdvancedBookingUseCase', () {
      late CreateAdvancedBookingParams validParams;
      late BookingModel testBooking;

      setUp(() {
        final futureDate = DateTime.now().add(const Duration(days: 1));
        
        validParams = CreateAdvancedBookingParams(
          customerId: 'customer-1',
          customerName: 'John Doe',
          customerPhone: '+**********',
          customerEmail: '<EMAIL>',
          providerId: 'provider-1',
          providerName: 'Test Provider',
          serviceId: 'service-1',
          timeSlotId: 'slot-1',
          bookingDate: futureDate,
          timeSlot: '10:00 - 11:00',
          totalAmount: 25.0,
          serviceDurationMinutes: 60,
        );

        testBooking = BookingModel(
          id: 'booking-1',
          customerId: validParams.customerId,
          customerName: validParams.customerName,
          customerPhone: validParams.customerPhone,
          customerEmail: validParams.customerEmail,
          providerId: validParams.providerId,
          providerName: validParams.providerName,
          serviceId: validParams.serviceId,
          serviceName: 'Test Service',
          service: ServiceModel(
            id: 'service-1',
            name: 'Test Service',
            description: 'Test description',
            price: 25.0,
            durationMinutes: 60,
            category: 'Test',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          bookingDate: validParams.bookingDate,
          appointmentDateTime: validParams.bookingDate.add(const Duration(hours: 10)),
          timeSlot: validParams.timeSlot,
          status: BookingStatus.pending,
          totalAmount: validParams.totalAmount,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      });

      test('should create booking successfully when all validations pass', () async {
        // Arrange
        final availableSlots = [
          TimeSlotModel(
            id: 'slot-1',
            startTime: validParams.bookingDate.add(const Duration(hours: 10)),
            endTime: validParams.bookingDate.add(const Duration(hours: 11)),
            isAvailable: true,
            durationMinutes: 60,
          ),
        ];

        when(mockRepository.getAvailableTimeSlots(
          validParams.providerId,
          validParams.bookingDate,
          validParams.serviceDurationMinutes,
        )).thenAnswer((_) async => Right(availableSlots));

        when(mockRepository.createBooking(any))
            .thenAnswer((_) async => Right(testBooking));

        // Act
        final result = await createAdvancedBookingUseCase(validParams);

        // Assert
        expect(result, isA<Right<Failure, BookingModel>>());
        result.fold(
          (failure) => fail('Expected Right but got Left'),
          (booking) {
            expect(booking.id, 'booking-1');
            expect(booking.customerId, validParams.customerId);
            expect(booking.providerId, validParams.providerId);
            expect(booking.totalAmount, validParams.totalAmount);
          },
        );

        verify(mockRepository.getAvailableTimeSlots(
          validParams.providerId,
          validParams.bookingDate,
          validParams.serviceDurationMinutes,
        )).called(1);
        verify(mockRepository.createBooking(any)).called(1);
      });

      test('should return validation failure when booking date is in the past', () async {
        // Arrange
        final pastDate = DateTime.now().subtract(const Duration(days: 1));
        final invalidParams = validParams.copyWith(bookingDate: pastDate);

        // Act
        final result = await createAdvancedBookingUseCase(invalidParams);

        // Assert
        expect(result, isA<Left<Failure, BookingModel>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, 'Booking date must be in the future');
          },
          (booking) => fail('Expected Left but got Right'),
        );

        verifyNever(mockRepository.getAvailableTimeSlots(any, any, any));
        verifyNever(mockRepository.createBooking(any));
      });

      test('should return validation failure when booking is too far in advance', () async {
        // Arrange
        final tooFarDate = DateTime.now().add(const Duration(days: 100));
        final invalidParams = validParams.copyWith(bookingDate: tooFarDate);

        // Act
        final result = await createAdvancedBookingUseCase(invalidParams);

        // Assert
        expect(result, isA<Left<Failure, BookingModel>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, 'Booking date cannot be more than 3 months in advance');
          },
          (booking) => fail('Expected Left but got Right'),
        );
      });

      test('should return validation failure when booking is too soon', () async {
        // Arrange
        final tooSoonDate = DateTime.now().add(const Duration(hours: 1));
        final invalidParams = validParams.copyWith(bookingDate: tooSoonDate);

        // Act
        final result = await createAdvancedBookingUseCase(invalidParams);

        // Assert
        expect(result, isA<Left<Failure, BookingModel>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, 'Booking must be made at least 2 hours in advance');
          },
          (booking) => fail('Expected Left but got Right'),
        );
      });

      test('should return validation failure when total amount is zero or negative', () async {
        // Arrange
        final invalidParams = validParams.copyWith(totalAmount: 0.0);

        // Act
        final result = await createAdvancedBookingUseCase(invalidParams);

        // Assert
        expect(result, isA<Left<Failure, BookingModel>>());
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
            expect(failure.message, 'Total amount must be greater than zero');
          },
          (booking) => fail('Expected Left but got Right'),
        );
      });

      test('should return booking failure when time slot is not available', () async {
        // Arrange
        final availableSlots = [
          TimeSlotModel(
            id: 'slot-2',
            timeSlot: '11:00 - 12:00',
            startTime: validParams.bookingDate.add(const Duration(hours: 11)),
            endTime: validParams.bookingDate.add(const Duration(hours: 12)),
            isAvailable: true,
          ),
        ];

        when(mockRepository.getAvailableTimeSlots(
          validParams.providerId,
          validParams.bookingDate,
          validParams.serviceDurationMinutes,
        )).thenAnswer((_) async => Right(availableSlots));

        // Act
        final result = await createAdvancedBookingUseCase(validParams);

        // Assert
        expect(result, isA<Left<Failure, BookingModel>>());
        result.fold(
          (failure) {
            expect(failure, isA<BookingFailure>());
            expect(failure.message, 'Selected time slot is no longer available');
          },
          (booking) => fail('Expected Left but got Right'),
        );

        verify(mockRepository.getAvailableTimeSlots(
          validParams.providerId,
          validParams.bookingDate,
          validParams.serviceDurationMinutes,
        )).called(1);
        verifyNever(mockRepository.createBooking(any));
      });
    });

    group('RescheduleBookingUseCase', () {
      test('should reschedule booking successfully when conditions are met', () async {
        // Arrange
        const bookingId = 'booking-1';
        final newDate = DateTime.now().add(const Duration(days: 2));
        const newTimeSlot = '14:00 - 15:00';

        final currentBooking = BookingModel(
          id: bookingId,
          customerId: 'customer-1',
          customerName: 'John Doe',
          providerId: 'provider-1',
          providerName: 'Test Provider',
          serviceId: 'service-1',
          serviceName: 'Test Service',
          service: ServiceModel(
            id: 'service-1',
            name: 'Test Service',
            description: 'Test description',
            price: 25.0,
            durationMinutes: 60,
            category: 'Test',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          bookingDate: DateTime.now().add(const Duration(days: 1)),
          appointmentDateTime: DateTime.now().add(const Duration(days: 1, hours: 10)),
          timeSlot: '10:00 - 11:00',
          status: BookingStatus.pending,
          totalAmount: 25.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final rescheduledBooking = currentBooking.copyWith(
          bookingDate: newDate,
          timeSlot: newTimeSlot,
        );

        final availableSlots = [
          TimeSlotModel(
            id: 'slot-1',
            startTime: newDate.add(const Duration(hours: 14)),
            endTime: newDate.add(const Duration(hours: 15)),
            isAvailable: true,
            durationMinutes: 60,
          ),
        ];

        when(mockRepository.getBookingById(bookingId))
            .thenAnswer((_) async => Right(currentBooking));

        when(mockRepository.getAvailableTimeSlots(
          currentBooking.providerId,
          newDate,
          currentBooking.service.durationMinutes,
        )).thenAnswer((_) async => Right(availableSlots));

        when(mockRepository.rescheduleBooking(bookingId, newDate, newTimeSlot, any))
            .thenAnswer((_) async => Right(rescheduledBooking));

        // Act
        final result = await rescheduleBookingUseCase(
          RescheduleBookingParams(
            bookingId: bookingId,
            newDate: newDate,
            newTimeSlot: newTimeSlot,
          ),
        );

        // Assert
        expect(result, isA<Right<Failure, BookingModel>>());
        result.fold(
          (failure) => fail('Expected Right but got Left'),
          (booking) {
            expect(booking.id, bookingId);
            expect(booking.bookingDate, newDate);
            expect(booking.timeSlot, newTimeSlot);
          },
        );

        verify(mockRepository.getBookingById(bookingId)).called(1);
        verify(mockRepository.getAvailableTimeSlots(
          currentBooking.providerId,
          newDate,
          currentBooking.service.durationMinutes,
        )).called(1);
        verify(mockRepository.rescheduleBooking(bookingId, newDate, newTimeSlot, any)).called(1);
      });

      test('should return failure when booking cannot be modified', () async {
        // Arrange
        const bookingId = 'booking-1';
        final newDate = DateTime.now().add(const Duration(days: 2));
        const newTimeSlot = '14:00 - 15:00';

        final completedBooking = BookingModel(
          id: bookingId,
          customerId: 'customer-1',
          customerName: 'John Doe',
          providerId: 'provider-1',
          providerName: 'Test Provider',
          serviceId: 'service-1',
          service: ServiceModel(
            id: 'service-1',
            name: 'Test Service',
            description: 'Test description',
            price: 25.0,
            durationMinutes: 60,
            category: 'Test',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          bookingDate: DateTime.now().add(const Duration(days: 1)),
          timeSlot: '10:00 - 11:00',
          status: BookingStatus.completed, // Cannot be modified
          totalAmount: 25.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockRepository.getBookingById(bookingId))
            .thenAnswer((_) async => Right(completedBooking));

        // Act
        final result = await rescheduleBookingUseCase(
          RescheduleBookingParams(
            bookingId: bookingId,
            newDate: newDate,
            newTimeSlot: newTimeSlot,
          ),
        );

        // Assert
        expect(result, isA<Left<Failure, BookingModel>>());
        result.fold(
          (failure) {
            expect(failure, isA<BookingFailure>());
            expect(failure.message, 'This booking cannot be rescheduled');
          },
          (booking) => fail('Expected Left but got Right'),
        );

        verify(mockRepository.getBookingById(bookingId)).called(1);
        verifyNever(mockRepository.getAvailableTimeSlots(any, any, any));
        verifyNever(mockRepository.rescheduleBooking(any, any, any, any));
      });
    });

    group('GetBookingStatisticsUseCase', () {
      test('should return booking statistics when repository call is successful', () async {
        // Arrange
        const providerId = 'provider-1';
        final startDate = DateTime(2024, 1, 1);
        final endDate = DateTime(2024, 12, 31);

        final statistics = {
          'totalBookings': 100,
          'completedBookings': 85,
          'cancelledBookings': 10,
          'pendingBookings': 5,
          'totalRevenue': 2500.0,
          'averageRating': 4.5,
        };

        when(mockRepository.getBookingStatistics(
          providerId,
          startDate: startDate,
          endDate: endDate,
        )).thenAnswer((_) async => Right(statistics));

        // Act
        final result = await getBookingStatisticsUseCase(
          GetBookingStatisticsParams(
            providerId: providerId,
            startDate: startDate,
            endDate: endDate,
          ),
        );

        // Assert
        expect(result, isA<Right<Failure, Map<String, dynamic>>>());
        result.fold(
          (failure) => fail('Expected Right but got Left'),
          (stats) {
            expect(stats['totalBookings'], 100);
            expect(stats['completedBookings'], 85);
            expect(stats['cancelledBookings'], 10);
            expect(stats['pendingBookings'], 5);
            expect(stats['totalRevenue'], 2500.0);
            expect(stats['averageRating'], 4.5);
          },
        );

        verify(mockRepository.getBookingStatistics(
          providerId,
          startDate: startDate,
          endDate: endDate,
        )).called(1);
      });
    });
  });
}

extension CreateAdvancedBookingParamsExtension on CreateAdvancedBookingParams {
  CreateAdvancedBookingParams copyWith({
    DateTime? bookingDate,
    double? totalAmount,
  }) {
    return CreateAdvancedBookingParams(
      customerId: customerId,
      customerName: customerName,
      customerPhone: customerPhone,
      customerEmail: customerEmail,
      providerId: providerId,
      providerName: providerName,
      serviceId: serviceId,
      timeSlotId: timeSlotId,
      bookingDate: bookingDate ?? this.bookingDate,
      timeSlot: timeSlot,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency,
      discountAmount: discountAmount,
      couponCode: couponCode,
      notes: notes,
      paymentInfo: paymentInfo,
      serviceDurationMinutes: serviceDurationMinutes,
    );
  }
}
