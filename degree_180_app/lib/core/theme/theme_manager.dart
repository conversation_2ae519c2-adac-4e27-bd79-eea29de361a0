import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../constants/app_colors.dart';
import '../enums/gender.dart';
import 'female_colors.dart';

class ThemeManager {
  static Gender _currentGender = Gender.male;
  
  static Gender get currentGender => _currentGender;
  
  static void setGender(Gender gender) {
    _currentGender = gender;
  }
  
  // Get current theme based on gender
  static ThemeData get currentTheme {
    return _currentGender == Gender.male ? maleTheme : femaleTheme;
  }
  
  // Get current colors based on gender
  static ThemeColors get currentColors {
    return _currentGender == Gender.male ? maleColors : femaleColors;
  }

  // Male Theme (Purple)
  static ThemeData get maleTheme {
    return ThemeData(
      useMaterial3: true,
      textTheme: GoogleFonts.cairoTextTheme(),
      primarySwatch: _createMaterialColor(AppColors.primaryPurple),
      primaryColor: AppColors.primaryPurple,
      scaffoldBackgroundColor: AppColors.backgroundWhite,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primaryPurple,
        brightness: Brightness.light,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.textWhite,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryPurple,
          foregroundColor: AppColors.textWhite,
          textStyle: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.borderGray),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primaryPurple, width: 2),
        ),
        labelStyle: GoogleFonts.cairo(color: AppColors.textGray),
        hintStyle: GoogleFonts.cairo(color: AppColors.textGray),
      ),
    );
  }

  // Female Theme (Pink)
  static ThemeData get femaleTheme {
    return ThemeData(
      useMaterial3: true,
      textTheme: GoogleFonts.cairoTextTheme(),
      primarySwatch: _createMaterialColor(FemaleColors.primaryPink),
      primaryColor: FemaleColors.primaryPink,
      scaffoldBackgroundColor: AppColors.backgroundWhite,
      colorScheme: ColorScheme.fromSeed(
        seedColor: FemaleColors.primaryPink,
        brightness: Brightness.light,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: FemaleColors.primaryPink,
        foregroundColor: AppColors.textWhite,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.textWhite,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: FemaleColors.primaryPink,
          foregroundColor: AppColors.textWhite,
          textStyle: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.borderGray),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: FemaleColors.primaryPink, width: 2),
        ),
        labelStyle: GoogleFonts.cairo(color: AppColors.textGray),
        hintStyle: GoogleFonts.cairo(color: AppColors.textGray),
      ),
    );
  }

  // Male Colors
  static ThemeColors get maleColors {
    return ThemeColors(
      primary: AppColors.primaryPurple,
      primaryLight: AppColors.primaryPurple.withValues(alpha: 0.7),
      primaryDark: AppColors.primaryPurple.withValues(alpha: 0.9),
      accent: AppColors.primaryPurpleLight,
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primaryPurple,
          AppColors.primaryPurpleLight,
        ],
      ),
      cardGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primaryPurple.withValues(alpha: 0.1),
          AppColors.primaryPurpleLight.withValues(alpha: 0.1),
        ],
      ),
    );
  }

  // Female Colors
  static ThemeColors get femaleColors {
    return ThemeColors(
      primary: FemaleColors.primaryPink,
      primaryLight: FemaleColors.primaryPinkLight,
      primaryDark: FemaleColors.primaryPinkDark,
      accent: FemaleColors.accentRose,
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          FemaleColors.primaryPink,
          FemaleColors.roseGold,
        ],
      ),
      cardGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          FemaleColors.primaryPink.withValues(alpha: 0.1),
          FemaleColors.roseGold.withValues(alpha: 0.1),
        ],
      ),
    );
  }

  static MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = (color.r * 255).round();
    final int g = (color.g * 255).round();
    final int b = (color.b * 255).round();

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.value, swatch);
  }
}

// Theme Colors Class
class ThemeColors {
  final Color primary;
  final Color primaryLight;
  final Color primaryDark;
  final Color accent;
  final LinearGradient gradient;
  final LinearGradient cardGradient;

  const ThemeColors({
    required this.primary,
    required this.primaryLight,
    required this.primaryDark,
    required this.accent,
    required this.gradient,
    required this.cardGradient,
  });
}

// Theme-aware widgets
class ThemedContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? borderRadius;
  final bool useGradient;

  const ThemedContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.useGradient = false,
  });

  @override
  Widget build(BuildContext context) {
    final colors = ThemeManager.currentColors;
    
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        gradient: useGradient ? colors.cardGradient : null,
        color: useGradient ? null : Colors.white,
        borderRadius: BorderRadius.circular(borderRadius ?? 12),
        boxShadow: [
          BoxShadow(
            color: colors.primary.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: child,
    );
  }
}

class ThemedButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final IconData? icon;

  const ThemedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final colors = ThemeManager.currentColors;
    
    if (isOutlined) {
      return OutlinedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading 
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(colors.primary),
                ),
              )
            : Icon(icon),
        label: Text(text),
        style: OutlinedButton.styleFrom(
          foregroundColor: colors.primary,
          side: BorderSide(color: colors.primary),
          textStyle: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
    
    return ElevatedButton.icon(
      onPressed: isLoading ? null : onPressed,
      icon: isLoading 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Icon(icon),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: colors.primary,
        foregroundColor: Colors.white,
        textStyle: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
