import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/models/booking_model.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/models/review_model.dart';
import '../../../../core/enums/service_type.dart';
import '../widgets/rating_stars_widget.dart';
import '../widgets/review_categories_widget.dart';
import '../widgets/photo_upload_widget.dart';

class WriteReviewPage extends StatefulWidget {
  final String? bookingId;
  final ServiceProviderModel? provider;

  const WriteReviewPage({
    super.key,
    this.bookingId,
    this.provider,
  });

  @override
  State<WriteReviewPage> createState() => _WriteReviewPageState();
}

class _WriteReviewPageState extends State<WriteReviewPage> {
  final _reviewController = TextEditingController();
  final _scrollController = ScrollController();
  
  double _overallRating = 0.0;
  Map<String, double> _categoryRatings = {
    'Service Quality': 0.0,
    'Cleanliness': 0.0,
    'Staff Friendliness': 0.0,
    'Value for Money': 0.0,
    'Timeliness': 0.0,
  };
  
  List<String> _uploadedPhotos = [];
  bool _isAnonymous = false;
  bool _isSubmitting = false;
  
  // Mock data - would come from booking
  final _mockProvider = ServiceProviderModel(
    id: 'provider_1',
    name: 'Glamour Salon',
    email: '<EMAIL>',
    description: 'Premium beauty salon',
    profileImageUrl: null,
    serviceType: ServiceType.salon,
    rating: 4.5,
    reviewCount: 120,
    isVerified: true,
    isActive: true,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  @override
  void dispose() {
    _reviewController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = widget.provider ?? _mockProvider;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Write Review'),
        backgroundColor: AppColors.primaryPurple,
        foregroundColor: AppColors.textWhite,
        actions: [
          TextButton(
            onPressed: _isSubmitting ? null : _submitReview,
            child: _isSubmitting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.textWhite),
                    ),
                  )
                : const Text(
                    'Submit',
                    style: TextStyle(
                      color: AppColors.textWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Provider Info
            _buildProviderInfo(provider),
            
            const SizedBox(height: 24),
            
            // Overall Rating
            _buildOverallRating(),
            
            const SizedBox(height: 24),
            
            // Category Ratings
            _buildCategoryRatings(),
            
            const SizedBox(height: 24),
            
            // Written Review
            _buildWrittenReview(),
            
            const SizedBox(height: 24),
            
            // Photo Upload
            _buildPhotoUpload(),
            
            const SizedBox(height: 24),
            
            // Review Options
            _buildReviewOptions(),
            
            const SizedBox(height: 32),
            
            // Submit Button
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildProviderInfo(ServiceProviderModel provider) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Provider Image
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: 60,
                height: 60,
                color: AppColors.backgroundGray,
                child: provider.profileImageUrl != null
                    ? CachedNetworkImage(
                        imageUrl: provider.profileImageUrl!,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => const Center(
                          child: CircularProgressIndicator(),
                        ),
                        errorWidget: (context, url, error) => const Icon(
                          Icons.business,
                          color: AppColors.textLight,
                        ),
                      )
                    : const Icon(
                        Icons.business,
                        color: AppColors.textLight,
                        size: 30,
                      ),
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Provider Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          provider.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (provider.isVerified)
                        Icon(
                          Icons.verified,
                          color: AppColors.primaryPurple,
                          size: 20,
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: AppColors.starYellow,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        provider.rating.toStringAsFixed(1),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '(${provider.reviewCount} reviews)',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textLight,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Booking ID: ${widget.bookingId ?? 'BOOK_123456'}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textLight,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallRating() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Overall Rating',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'How would you rate your overall experience?',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
            ),
            const SizedBox(height: 20),
            Center(
              child: Column(
                children: [
                  RatingStarsWidget(
                    rating: _overallRating,
                    size: 40,
                    onRatingChanged: (rating) {
                      setState(() {
                        _overallRating = rating;
                      });
                    },
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _getRatingText(_overallRating),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: _getRatingColor(_overallRating),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryRatings() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rate by Category',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Help others by rating specific aspects',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
            ),
            const SizedBox(height: 20),
            ReviewCategoriesWidget(
              categories: _categoryRatings,
              onRatingChanged: (category, rating) {
                setState(() {
                  _categoryRatings[category] = rating;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWrittenReview() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Write Your Review',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Share your experience to help others',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _reviewController,
              maxLines: 6,
              maxLength: 500,
              decoration: InputDecoration(
                hintText: 'Tell us about your experience...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.borderGray),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.primaryPurple, width: 2),
                ),
                contentPadding: const EdgeInsets.all(16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoUpload() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add Photos',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Show others what to expect (optional)',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textLight,
              ),
            ),
            const SizedBox(height: 16),
            PhotoUploadWidget(
              photos: _uploadedPhotos,
              onPhotosChanged: (photos) {
                setState(() {
                  _uploadedPhotos = photos;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewOptions() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Review Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Post anonymously'),
              subtitle: const Text('Your name won\'t be shown with this review'),
              value: _isAnonymous,
              onChanged: (value) {
                setState(() {
                  _isAnonymous = value;
                });
              },
              activeColor: AppColors.primaryPurple,
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    final canSubmit = _overallRating > 0 && _reviewController.text.trim().isNotEmpty;
    
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: canSubmit && !_isSubmitting ? _submitReview : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryPurple,
          foregroundColor: AppColors.textWhite,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          disabledBackgroundColor: AppColors.borderGray,
        ),
        child: _isSubmitting
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.textWhite),
                ),
              )
            : const Text(
                'Submit Review',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  String _getRatingText(double rating) {
    if (rating == 0) return 'Tap to rate';
    if (rating <= 1) return 'Poor';
    if (rating <= 2) return 'Fair';
    if (rating <= 3) return 'Good';
    if (rating <= 4) return 'Very Good';
    return 'Excellent';
  }

  Color _getRatingColor(double rating) {
    if (rating == 0) return AppColors.textLight;
    if (rating <= 2) return AppColors.error;
    if (rating <= 3) return AppColors.warning;
    return AppColors.success;
  }

  Future<void> _submitReview() async {
    if (_overallRating == 0) {
      _showErrorSnackBar('Please provide an overall rating');
      return;
    }

    if (_reviewController.text.trim().isEmpty) {
      _showErrorSnackBar('Please write a review');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Create review model
      final review = ReviewModel(
        id: '',
        bookingId: widget.bookingId ?? '',
        providerId: widget.provider?.id ?? _mockProvider.id,
        customerId: 'current_user_id', // Would come from auth
        customerName: _isAnonymous ? 'Anonymous' : 'Current User',
        customerAvatar: _isAnonymous ? null : 'user_image_url',
        overallRating: _overallRating,
        comment: _reviewController.text.trim(),
        detailedRatings: _categoryRatings,
        images: _uploadedPhotos,
        isAnonymous: _isAnonymous,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Submit review (would call Firebase service)
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      // Show success and navigate back
      _showSuccessDialog();
    } catch (e) {
      _showErrorSnackBar('Failed to submit review. Please try again.');
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: 28,
            ),
            const SizedBox(width: 12),
            const Text('Review Submitted'),
          ],
        ),
        content: const Text(
          'Thank you for your review! It will help other customers make informed decisions.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Go back to previous page
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }
}
