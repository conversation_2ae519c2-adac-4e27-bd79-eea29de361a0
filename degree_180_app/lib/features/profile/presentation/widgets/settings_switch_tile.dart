import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';

class SettingsSwitchTile extends StatefulWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool value;
  final ValueChanged<bool> onChanged;
  final bool enabled;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? textColor;
  final Color? iconColor;
  final EdgeInsets? contentPadding;

  const SettingsSwitchTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    this.activeColor,
    this.inactiveColor,
    this.textColor,
    this.iconColor,
    this.contentPadding,
  });

  @override
  State<SettingsSwitchTile> createState() => _SettingsSwitchTileState();
}

class _SettingsSwitchTileState extends State<SettingsSwitchTile>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    if (!widget.enabled) return;
    
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    
    widget.onChanged(!widget.value);
  }

  @override
  Widget build(BuildContext context) {
    final effectiveTextColor = widget.enabled 
        ? (widget.textColor ?? AppColors.textDark)
        : AppColors.textLight;
    
    final effectiveIconColor = widget.enabled
        ? (widget.iconColor ?? AppColors.textLight)
        : AppColors.textLight.withValues(alpha: 0.5);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: InkWell(
            onTap: _handleTap,
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: widget.contentPadding ?? 
                       const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
              child: Row(
                children: [
                  // Leading Icon
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: effectiveIconColor,
                      size: 24,
                    ),
                    const SizedBox(width: 16),
                  ],
                  
                  // Title and Subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.title,
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: effectiveTextColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (widget.subtitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            widget.subtitle!,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.textLight,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // Switch
                  const SizedBox(width: 16),
                  Switch(
                    value: widget.value,
                    onChanged: widget.enabled ? widget.onChanged : null,
                    activeColor: widget.activeColor ?? AppColors.primaryPurple,
                    inactiveThumbColor: widget.inactiveColor ?? AppColors.borderGray,
                    inactiveTrackColor: AppColors.borderGray.withValues(alpha: 0.3),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Animated switch tile with custom animations
class AnimatedSettingsSwitchTile extends StatefulWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool value;
  final ValueChanged<bool> onChanged;
  final bool enabled;
  final Duration animationDuration;
  final Color? activeColor;
  final Color? inactiveColor;

  const AnimatedSettingsSwitchTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.activeColor,
    this.inactiveColor,
  });

  @override
  State<AnimatedSettingsSwitchTile> createState() => _AnimatedSettingsSwitchTileState();
}

class _AnimatedSettingsSwitchTileState extends State<AnimatedSettingsSwitchTile>
    with TickerProviderStateMixin {
  late AnimationController _colorController;
  late AnimationController _iconController;
  late Animation<Color?> _colorAnimation;
  late Animation<double> _iconRotationAnimation;

  @override
  void initState() {
    super.initState();
    
    _colorController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _iconController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _colorAnimation = ColorTween(
      begin: AppColors.textLight,
      end: widget.activeColor ?? AppColors.primaryPurple,
    ).animate(_colorController);
    
    _iconRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.easeInOut,
    ));
    
    if (widget.value) {
      _colorController.value = 1.0;
      _iconController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(AnimatedSettingsSwitchTile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      if (widget.value) {
        _colorController.forward();
        _iconController.forward();
      } else {
        _colorController.reverse();
        _iconController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _colorController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_colorController, _iconController]),
      builder: (context, child) {
        return SettingsSwitchTile(
          title: widget.title,
          subtitle: widget.subtitle,
          icon: widget.icon,
          value: widget.value,
          onChanged: widget.onChanged,
          enabled: widget.enabled,
          activeColor: widget.activeColor,
          inactiveColor: widget.inactiveColor,
          iconColor: _colorAnimation.value,
        );
      },
    );
  }
}

// Switch tile with confirmation dialog
class ConfirmationSwitchTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool value;
  final ValueChanged<bool> onChanged;
  final String confirmationTitle;
  final String confirmationMessage;
  final String confirmButtonText;
  final String cancelButtonText;
  final bool enabled;
  final bool requireConfirmationOnlyForTrue;

  const ConfirmationSwitchTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
    required this.onChanged,
    required this.confirmationTitle,
    required this.confirmationMessage,
    this.confirmButtonText = 'Confirm',
    this.cancelButtonText = 'Cancel',
    this.enabled = true,
    this.requireConfirmationOnlyForTrue = true,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsSwitchTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      value: value,
      enabled: enabled,
      onChanged: (newValue) {
        final needsConfirmation = requireConfirmationOnlyForTrue 
            ? newValue 
            : true;
        
        if (needsConfirmation) {
          _showConfirmationDialog(context, newValue);
        } else {
          onChanged(newValue);
        }
      },
    );
  }

  void _showConfirmationDialog(BuildContext context, bool newValue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(confirmationTitle),
        content: Text(confirmationMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(cancelButtonText),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onChanged(newValue);
            },
            child: Text(confirmButtonText),
          ),
        ],
      ),
    );
  }
}

// Switch tile with custom switch design
class CustomSwitchTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool value;
  final ValueChanged<bool> onChanged;
  final bool enabled;
  final Widget customSwitch;

  const CustomSwitchTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    required this.customSwitch,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveTextColor = enabled 
        ? AppColors.textDark
        : AppColors.textLight;
    
    final effectiveIconColor = enabled
        ? AppColors.textLight
        : AppColors.textLight.withValues(alpha: 0.5);

    return InkWell(
      onTap: enabled ? () => onChanged(!value) : null,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            // Leading Icon
            if (icon != null) ...[
              Icon(
                icon,
                color: effectiveIconColor,
                size: 24,
              ),
              const SizedBox(width: 16),
            ],
            
            // Title and Subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: effectiveTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      subtitle!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textLight,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            
            // Custom Switch
            const SizedBox(width: 16),
            customSwitch,
          ],
        ),
      ),
    );
  }
}

// Switch tile with loading state
class LoadingSwitchTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool value;
  final ValueChanged<bool>? onChanged;
  final bool isLoading;
  final bool enabled;

  const LoadingSwitchTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
    this.onChanged,
    required this.isLoading,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsSwitchTile(
      title: title,
      subtitle: subtitle,
      icon: icon,
      value: value,
      enabled: enabled && !isLoading,
      onChanged: isLoading ? (_) {} : (onChanged ?? (_) {}),
    );
  }
}
