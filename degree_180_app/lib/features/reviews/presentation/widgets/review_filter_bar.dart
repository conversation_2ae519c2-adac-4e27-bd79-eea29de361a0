import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class ReviewFilterBar extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const ReviewFilterBar({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final filters = [
      {'id': 'all', 'label': 'All'},
      {'id': '5', 'label': '5⭐'},
      {'id': '4', 'label': '4⭐'},
      {'id': '3', 'label': '3⭐'},
      {'id': '2', 'label': '2⭐'},
      {'id': '1', 'label': '1⭐'},
      {'id': 'with_photos', 'label': 'With Photos'},
      {'id': 'with_comments', 'label': 'With Comments'},
    ];

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: filters.map((filter) {
            final isSelected = selectedFilter == filter['id'];
            return Padding(
              padding: const EdgeInsets.only(right: AppConstants.smallPadding),
              child: _buildFilterChip(
                label: filter['label']!,
                isSelected: isSelected,
                onTap: () => onFilterChanged(filter['id']!),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primaryPurple : AppColors.backgroundGray,
            borderRadius: BorderRadius.circular(AppConstants.circularBorderRadius),
            border: Border.all(
              color: isSelected ? AppColors.primaryPurple : AppColors.borderGray,
            ),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: isSelected ? AppColors.textWhite : AppColors.textDark,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              fontSize: AppConstants.fontSizeSmall,
            ),
          ),
        ),
      ),
    );
  }
}
