import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';

import 'package:degree_180_app/features/auth/domain/entities/user_entity.dart';
import 'package:degree_180_app/features/auth/domain/usecases/sign_in_with_email_usecase.dart';
import 'package:degree_180_app/features/auth/domain/usecases/sign_up_with_email_usecase.dart';
import 'package:degree_180_app/features/auth/domain/usecases/sign_in_with_google_usecase.dart';
import 'package:degree_180_app/features/auth/domain/usecases/sign_out_usecase.dart';
import 'package:degree_180_app/features/auth/domain/usecases/get_current_user_usecase.dart';
import 'package:degree_180_app/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:degree_180_app/core/errors/failures.dart';
import 'package:degree_180_app/core/usecases/usecase.dart';

class MockSignInWithEmailUseCase extends Mock implements SignInWithEmailUseCase {}
class MockSignUpWithEmailUseCase extends Mock implements SignUpWithEmailUseCase {}
class MockSignInWithGoogleUseCase extends Mock implements SignInWithGoogleUseCase {}
class MockSignOutUseCase extends Mock implements SignOutUseCase {}
class MockGetCurrentUserUseCase extends Mock implements GetCurrentUserUseCase {}

void main() {
  late AuthBloc authBloc;
  late MockSignInWithEmailUseCase mockSignInWithEmailUseCase;
  late MockSignUpWithEmailUseCase mockSignUpWithEmailUseCase;
  late MockSignInWithGoogleUseCase mockSignInWithGoogleUseCase;
  late MockSignOutUseCase mockSignOutUseCase;
  late MockGetCurrentUserUseCase mockGetCurrentUserUseCase;

  setUp(() {
    mockSignInWithEmailUseCase = MockSignInWithEmailUseCase();
    mockSignUpWithEmailUseCase = MockSignUpWithEmailUseCase();
    mockSignInWithGoogleUseCase = MockSignInWithGoogleUseCase();
    mockSignOutUseCase = MockSignOutUseCase();
    mockGetCurrentUserUseCase = MockGetCurrentUserUseCase();
    
    authBloc = AuthBloc(
      signInWithEmailUseCase: mockSignInWithEmailUseCase,
      signUpWithEmailUseCase: mockSignUpWithEmailUseCase,
      signInWithGoogleUseCase: mockSignInWithGoogleUseCase,
      signOutUseCase: mockSignOutUseCase,
      getCurrentUserUseCase: mockGetCurrentUserUseCase,
    );
  });

  tearDown(() {
    authBloc.close();
  });

  const testEmail = '<EMAIL>';
  const testPassword = 'password123';
  const testName = 'Test User';
  
  final testUser = UserEntity(
    id: '1',
    uid: '1',
    email: testEmail,
    name: testName,
    userType: UserType.customer,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  test('initial state should be AuthInitial', () {
    expect(authBloc.state, AuthInitial());
  });

  group('SignInWithEmailEvent', () {
    blocTest<AuthBloc, AuthState>(
      'should emit [AuthLoading, AuthAuthenticated] when sign in is successful',
      build: () {
        when(() => mockSignInWithEmailUseCase(any()))
            .thenAnswer((_) async => Right(testUser));
        return authBloc;
      },
      act: (bloc) => bloc.add(const SignInWithEmailEvent(
        email: testEmail,
        password: testPassword,
      )),
      expect: () => [
        AuthLoading(),
        AuthAuthenticated(user: testUser),
      ],
    );

    blocTest<AuthBloc, AuthState>(
      'should emit [AuthLoading, AuthError] when sign in fails',
      build: () {
        when(() => mockSignInWithEmailUseCase(any()))
            .thenAnswer((_) async => const Left(InvalidCredentialsFailure()));
        return authBloc;
      },
      act: (bloc) => bloc.add(const SignInWithEmailEvent(
        email: testEmail,
        password: testPassword,
      )),
      expect: () => [
        AuthLoading(),
        const AuthError(message: 'Invalid email or password'),
      ],
    );
  });

  group('SignOutEvent', () {
    blocTest<AuthBloc, AuthState>(
      'should emit [AuthLoading, AuthUnauthenticated] when sign out is successful',
      build: () {
        when(() => mockSignOutUseCase(any()))
            .thenAnswer((_) async => const Right(null));
        return authBloc;
      },
      act: (bloc) => bloc.add(const SignOutEvent()),
      expect: () => [
        AuthLoading(),
        AuthUnauthenticated(),
      ],
    );
  });

  group('CheckAuthStatusEvent', () {
    blocTest<AuthBloc, AuthState>(
      'should emit [AuthLoading, AuthAuthenticated] when user is signed in',
      build: () {
        when(() => mockGetCurrentUserUseCase(any()))
            .thenAnswer((_) async => Right(testUser));
        return authBloc;
      },
      act: (bloc) => bloc.add(const CheckAuthStatusEvent()),
      expect: () => [
        AuthLoading(),
        AuthAuthenticated(user: testUser),
      ],
    );

    blocTest<AuthBloc, AuthState>(
      'should emit [AuthLoading, AuthUnauthenticated] when no user is signed in',
      build: () {
        when(() => mockGetCurrentUserUseCase(any()))
            .thenAnswer((_) async => const Left(AuthFailure(message: 'No user')));
        return authBloc;
      },
      act: (bloc) => bloc.add(const CheckAuthStatusEvent()),
      expect: () => [
        AuthLoading(),
        AuthUnauthenticated(),
      ],
    );
  });
}
