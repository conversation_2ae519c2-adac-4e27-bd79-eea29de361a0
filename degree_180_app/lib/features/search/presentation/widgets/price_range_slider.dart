import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class PriceRangeSlider extends StatelessWidget {
  final RangeValues values;
  final Function(RangeValues) onChanged;

  const PriceRangeSlider({
    super.key,
    required this.values,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RangeSlider(
          values: values,
          min: 0,
          max: 100,
          divisions: 20,
          activeColor: AppColors.primaryPurple,
          inactiveColor: AppColors.primaryPurple.withValues(alpha: 0.3),
          onChanged: onChanged,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${values.start.toInt()} KD',
              style: const TextStyle(
                color: AppColors.textDark,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${values.end.toInt()} KD',
              style: const TextStyle(
                color: AppColors.textDark,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
