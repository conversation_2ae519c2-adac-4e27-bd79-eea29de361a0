import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class RecentSearchesWidget extends StatefulWidget {
  const RecentSearchesWidget({super.key});

  @override
  State<RecentSearchesWidget> createState() => _RecentSearchesWidgetState();
}

class _RecentSearchesWidgetState extends State<RecentSearchesWidget> {
  List<String> _recentSearches = [];

  @override
  void initState() {
    super.initState();
    _loadRecentSearches();
  }

  Future<void> _loadRecentSearches() async {
    final prefs = await SharedPreferences.getInstance();
    final searches = prefs.getStringList('recent_searches') ?? [];
    setState(() {
      _recentSearches = searches.take(8).toList(); // Show max 8 recent searches
    });
  }

  Future<void> _addRecentSearch(String query) async {
    final prefs = await SharedPreferences.getInstance();
    final searches = prefs.getStringList('recent_searches') ?? [];
    
    // Remove if already exists
    searches.remove(query);
    // Add to beginning
    searches.insert(0, query);
    // Keep only last 20 searches
    if (searches.length > 20) {
      searches.removeRange(20, searches.length);
    }
    
    await prefs.setStringList('recent_searches', searches);
    _loadRecentSearches();
  }

  Future<void> _clearRecentSearches() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('recent_searches');
    setState(() {
      _recentSearches = [];
    });
  }

  Future<void> _removeRecentSearch(String query) async {
    final prefs = await SharedPreferences.getInstance();
    final searches = prefs.getStringList('recent_searches') ?? [];
    searches.remove(query);
    await prefs.setStringList('recent_searches', searches);
    _loadRecentSearches();
  }

  @override
  Widget build(BuildContext context) {
    if (_recentSearches.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Searches',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: _showClearConfirmation,
              child: Text(
                'Clear All',
                style: TextStyle(
                  color: AppColors.textLight,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Recent Searches List
        Column(
          children: _recentSearches.map((search) {
            return _RecentSearchItem(
              query: search,
              onTap: () => _performSearch(search),
              onRemove: () => _removeRecentSearch(search),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _performSearch(String query) {
    _addRecentSearch(query);
    Navigator.pushNamed(
      context,
      '/search',
      arguments: {'query': query},
    );
  }

  void _showClearConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Recent Searches'),
        content: const Text('Are you sure you want to clear all recent searches?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearRecentSearches();
            },
            child: Text(
              'Clear',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}

class _RecentSearchItem extends StatelessWidget {
  final String query;
  final VoidCallback onTap;
  final VoidCallback onRemove;

  const _RecentSearchItem({
    required this.query,
    required this.onTap,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          decoration: BoxDecoration(
            color: AppColors.backgroundGray.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.history,
                size: 20,
                color: AppColors.textLight,
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: Text(
                  query,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textDark,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Transfer to search button
              GestureDetector(
                onTap: () => _transferToSearch(context, query),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  child: Icon(
                    Icons.north_west,
                    size: 16,
                    color: AppColors.textLight,
                  ),
                ),
              ),
              
              const SizedBox(width: 4),
              
              // Remove button
              GestureDetector(
                onTap: onRemove,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  child: Icon(
                    Icons.close,
                    size: 16,
                    color: AppColors.textLight,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _transferToSearch(BuildContext context, String query) {
    // Navigate back to search page and fill the search field
    Navigator.pop(context);
    // This would require passing the query back to the search page
    // Implementation depends on how the search page is structured
  }
}

// Utility class for managing recent searches
class RecentSearchManager {
  static const String _key = 'recent_searches';
  static const int _maxSearches = 20;

  static Future<List<String>> getRecentSearches() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_key) ?? [];
  }

  static Future<void> addRecentSearch(String query) async {
    if (query.trim().isEmpty) return;
    
    final prefs = await SharedPreferences.getInstance();
    final searches = await getRecentSearches();
    
    // Remove if already exists
    searches.remove(query);
    // Add to beginning
    searches.insert(0, query);
    // Keep only last searches
    if (searches.length > _maxSearches) {
      searches.removeRange(_maxSearches, searches.length);
    }
    
    await prefs.setStringList(_key, searches);
  }

  static Future<void> removeRecentSearch(String query) async {
    final prefs = await SharedPreferences.getInstance();
    final searches = await getRecentSearches();
    searches.remove(query);
    await prefs.setStringList(_key, searches);
  }

  static Future<void> clearRecentSearches() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_key);
  }

  static Future<List<String>> getSearchSuggestions(String query) async {
    final recentSearches = await getRecentSearches();
    return recentSearches
        .where((search) => search.toLowerCase().contains(query.toLowerCase()))
        .take(5)
        .toList();
  }
}
