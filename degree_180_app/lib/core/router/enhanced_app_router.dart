import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
// import '../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../features/home/<USER>/pages/enhanced_home_page.dart';
import '../../features/services/presentation/pages/service_providers_page.dart';
import '../../features/services/presentation/pages/service_provider_details_page.dart';
import '../../features/booking/presentation/pages/booking_page.dart';
import '../../features/booking/presentation/pages/booking_confirmation_page.dart';
import '../../features/booking/presentation/pages/my_bookings_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
// import '../../features/profile/presentation/pages/edit_profile_page.dart';
import '../../features/search/presentation/pages/search_page.dart';
import '../../features/notifications/presentation/pages/notifications_page.dart';
import '../../features/maps/presentation/pages/map_page.dart';
import '../../features/reviews/presentation/pages/reviews_page.dart';
import '../../features/reviews/presentation/pages/write_review_page.dart';
import '../../features/payment/presentation/pages/payment_page.dart';
import '../../features/chat/presentation/pages/chat_page.dart';
import '../../features/chat/presentation/pages/chat_list_page.dart';

class EnhancedAppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final authState = context.read<AuthBloc>().state;
      final isLoggedIn = authState is AuthAuthenticated;
      final isLoggingIn = state.matchedLocation == '/login' ||
                         state.matchedLocation == '/register' ||
                         state.matchedLocation == '/forgot-password';

      // If not logged in and not on auth pages, redirect to login
      if (!isLoggedIn && !isLoggingIn) {
        return '/login';
      }

      // If logged in and on auth pages, redirect to home
      if (isLoggedIn && isLoggingIn) {
        return '/';
      }

      return null;
    },
    routes: [
      // Auth Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      // GoRoute(
      //   path: '/forgot-password',
      //   name: 'forgot-password',
      //   builder: (context, state) => const ForgotPasswordPage(),
      // ),

      // Main App Routes
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const EnhancedHomePage(),
      ),

      // Service Provider Routes
      GoRoute(
        path: '/providers',
        name: 'providers',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          return ServiceProvidersPage(
            serviceType: extra?['serviceType'],
            userLocation: extra?['userLocation'],
            featured: extra?['featured'] ?? false,
            nearby: extra?['nearby'] ?? false,
          );
        },
      ),
      GoRoute(
        path: '/provider-details/:providerId',
        name: 'provider-details',
        builder: (context, state) {
          final providerId = state.pathParameters['providerId']!;
          return ServiceProviderDetailsPage(providerId: providerId);
        },
      ),

      // Booking Routes
      GoRoute(
        path: '/booking',
        name: 'booking',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          return BookingPage(
            providerId: extra?['providerId'],
            serviceId: extra?['serviceId'],
          );
        },
      ),
      GoRoute(
        path: '/booking-confirmation/:bookingId',
        name: 'booking-confirmation',
        builder: (context, state) {
          final bookingId = state.pathParameters['bookingId']!;
          return BookingConfirmationPage(bookingId: bookingId);
        },
      ),
      GoRoute(
        path: '/my-bookings',
        name: 'my-bookings',
        builder: (context, state) => const MyBookingsPage(),
      ),

      // Profile Routes
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
      ),
      // GoRoute(
      //   path: '/edit-profile',
      //   name: 'edit-profile',
      //   builder: (context, state) => const EditProfilePage(),
      // ),

      // Search Routes
      GoRoute(
        path: '/search',
        name: 'search',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          return SearchPage(
            initialQuery: extra?['query'],
            serviceType: extra?['serviceType'],
          );
        },
      ),

      // Notification Routes
      GoRoute(
        path: '/notifications',
        name: 'notifications',
        builder: (context, state) => const NotificationsPage(),
      ),

      // Map Routes
      GoRoute(
        path: '/map',
        name: 'map',
        builder: (context, state) {
          return const MapPage();
        },
      ),

      // Review Routes
      GoRoute(
        path: '/reviews/:providerId',
        name: 'reviews',
        builder: (context, state) {
          final providerId = state.pathParameters['providerId']!;
          return ReviewsPage(providerId: providerId);
        },
      ),
      GoRoute(
        path: '/write-review/:bookingId',
        name: 'write-review',
        builder: (context, state) {
          final bookingId = state.pathParameters['bookingId']!;
          return WriteReviewPage(bookingId: bookingId);
        },
      ),

      // Payment Routes
      GoRoute(
        path: '/payment/:bookingId',
        name: 'payment',
        builder: (context, state) {
          final bookingId = state.pathParameters['bookingId']!;
          final extra = state.extra as Map<String, dynamic>?;
          return PaymentPage(
            bookingId: bookingId,
            amount: extra?['amount'] ?? 0.0,
            providerId: extra?['providerId'] ?? '',
          );
        },
      ),

      // Chat Routes
      GoRoute(
        path: '/chat-list',
        name: 'chat-list',
        builder: (context, state) => const ChatListPage(),
      ),
      GoRoute(
        path: '/chat/:chatId',
        name: 'chat',
        builder: (context, state) {
          final chatId = state.pathParameters['chatId']!;
          final extra = state.extra as Map<String, dynamic>?;
          return ChatPage(
            chatId: chatId,
            recipientName: extra?['recipientName'],
            recipientImage: extra?['recipientImage'],
          );
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}
