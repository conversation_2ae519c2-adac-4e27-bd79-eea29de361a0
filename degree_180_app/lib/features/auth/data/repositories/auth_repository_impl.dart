import 'package:dartz/dartz.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_data_source.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/network/network_info.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, UserEntity>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signInWithEmailAndPassword(
          email: email,
          password: password,
        );
        return Right(user);
      } on ServerException catch (e) {
        return Left(_mapServerExceptionToFailure(e));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    required UserType userType,
    ServiceType? serviceType,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signUpWithEmailAndPassword(
          email: email,
          password: password,
          name: name,
          userType: userType,
          serviceType: serviceType,
        );
        return Right(user);
      } on ServerException catch (e) {
        return Left(_mapServerExceptionToFailure(e));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithGoogle() async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.signInWithGoogle();
        return Right(user);
      } on ServerException catch (e) {
        return Left(_mapServerExceptionToFailure(e));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> signInWithApple() async {
    // TODO: Implement Apple Sign In
    return const Left(ServerFailure(message: 'Apple Sign In not implemented yet'));
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await remoteDataSource.signOut();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  Future<Either<Failure, void>> sendPasswordResetEmail({
    required String email,
  }) async {
    // TODO: Implement password reset
    return const Left(ServerFailure(message: 'Password reset not implemented yet'));
  }

  @override
  Future<Either<Failure, void>> sendEmailVerification() async {
    // TODO: Implement email verification
    return const Left(ServerFailure(message: 'Email verification not implemented yet'));
  }

  @override
  Future<Either<Failure, UserEntity>> getCurrentUser() async {
    try {
      final user = await remoteDataSource.getCurrentUser();
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  Future<Either<Failure, bool>> isUserSignedIn() async {
    try {
      final isSignedIn = await remoteDataSource.isUserSignedIn();
      return Right(isSignedIn);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateUserProfile({
    String? name,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final user = await remoteDataSource.updateUserProfile(
          name: name,
          phoneNumber: phoneNumber,
          profileImageUrl: profileImageUrl,
        );
        return Right(user);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    // TODO: Implement password update
    return const Left(ServerFailure(message: 'Password update not implemented yet'));
  }

  @override
  Future<Either<Failure, void>> deleteAccount() async {
    // TODO: Implement account deletion
    return const Left(ServerFailure(message: 'Account deletion not implemented yet'));
  }

  @override
  Future<Either<Failure, UserEntity>> getUserById(String userId) async {
    // TODO: Implement get user by ID
    return const Left(ServerFailure(message: 'Get user by ID not implemented yet'));
  }

  @override
  Future<Either<Failure, List<UserEntity>>> getServiceProviders({
    ServiceType? serviceType,
    int? limit,
  }) async {
    // TODO: Implement get service providers
    return const Left(ServerFailure(message: 'Get service providers not implemented yet'));
  }

  @override
  Future<Either<Failure, void>> updateUserStatus({
    required bool isActive,
  }) async {
    // TODO: Implement update user status
    return const Left(ServerFailure(message: 'Update user status not implemented yet'));
  }

  @override
  Stream<UserEntity?> get authStateChanges {
    // TODO: Implement auth state changes stream
    throw UnimplementedError();
  }

  @override
  Stream<UserEntity?> get userChanges {
    // TODO: Implement user changes stream
    throw UnimplementedError();
  }

  Failure _mapServerExceptionToFailure(ServerException exception) {
    switch (exception.code) {
      case 'user-not-found':
        return const UserNotFoundFailure();
      case 'wrong-password':
      case 'invalid-credential':
        return const InvalidCredentialsFailure();
      case 'email-already-in-use':
        return const EmailAlreadyInUseFailure();
      case 'weak-password':
        return const WeakPasswordFailure();
      case 'invalid-email':
        return const InvalidEmailFailure();
      case 'user-disabled':
        return const UserDisabledFailure();
      case 'too-many-requests':
        return const TooManyRequestsFailure();
      case 'operation-not-allowed':
        return const OperationNotAllowedFailure();
      default:
        return ServerFailure(message: exception.message);
    }
  }
}
