import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/enhanced_card.dart';
import '../../../../core/models/service_provider_model.dart';

class ProviderInfoWidget extends StatelessWidget {
  final ServiceProviderModel provider;

  const ProviderInfoWidget({
    super.key,
    required this.provider,
  });

  // Mock data - in real app, this would come from provider model
  final List<String> specialties = const ['Hair Styling', 'Color Treatment', 'Keratin'];
  final String mockWebsite = 'https://example.com';

  @override
  Widget build(BuildContext context) {

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // About Section
          _buildAboutSection(context),
          
          const SizedBox(height: 24),
          
          // Contact Information
          _buildContactSection(context),
          
          const SizedBox(height: 24),
          
          // Working Hours
          _buildWorkingHoursSection(context),
          
          const SizedBox(height: 24),
          
          // Location & Directions
          _buildLocationSection(context),
          
          const SizedBox(height: 24),
          
          // Statistics
          _buildStatisticsSection(context),
          
          const SizedBox(height: 24),
          
          // Action Buttons
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.primaryPurple,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'About',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (provider.description != null && provider.description!.isNotEmpty)
              Text(
                provider.description!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.5,
                ),
              )
            else
              Text(
                'No description available for this provider.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textLight,
                  fontStyle: FontStyle.italic,
                ),
              ),
            
            const SizedBox(height: 16),
            
            // Tags/Specialties
            if (specialties.isNotEmpty) ...[
              Text(
                'Specialties',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: specialties.map((specialty) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.primaryPurple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppColors.primaryPurple.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      specialty,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.primaryPurple,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.contact_phone,
                  color: AppColors.primaryPurple,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Contact Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Phone
            if (provider.phoneNumber != null)
              _buildContactItem(
                context,
                Icons.phone,
                'Phone',
                provider.phoneNumber!,
                () => _makePhoneCall(provider.phoneNumber!),
              ),
            
            // Email
            _buildContactItem(
              context,
              Icons.email,
              'Email',
              provider.email,
              () => _sendEmail(provider.email),
            ),
            
            // Website - Mock data
            _buildContactItem(
              context,
              Icons.language,
              'Website',
              mockWebsite,
              () => _openWebsite(mockWebsite),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem(
    BuildContext context,
    IconData icon,
    String label,
    String value,
    VoidCallback onTap,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryPurple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primaryPurple,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textLight,
                      ),
                    ),
                    Text(
                      value,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryPurple,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textLight,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWorkingHoursSection(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: AppColors.primaryPurple,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Working Hours',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Working hours would be displayed here
            // For now, showing default hours
            _buildWorkingHourItem(context, 'Monday', '9:00 AM - 8:00 PM', true),
            _buildWorkingHourItem(context, 'Tuesday', '9:00 AM - 8:00 PM', false),
            _buildWorkingHourItem(context, 'Wednesday', '9:00 AM - 8:00 PM', false),
            _buildWorkingHourItem(context, 'Thursday', '9:00 AM - 8:00 PM', false),
            _buildWorkingHourItem(context, 'Friday', '2:00 PM - 8:00 PM', false),
            _buildWorkingHourItem(context, 'Saturday', '9:00 AM - 8:00 PM', false),
            _buildWorkingHourItem(context, 'Sunday', 'Closed', false),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkingHourItem(BuildContext context, String day, String hours, bool isToday) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            day,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
              color: isToday ? AppColors.primaryPurple : AppColors.textDark,
            ),
          ),
          Text(
            hours,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
              color: isToday ? AppColors.primaryPurple : AppColors.textLight,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: AppColors.primaryPurple,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Location',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (provider.location != null) ...[
              Text(
                provider.location!.address ?? 'Address not available',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _openMaps(),
                  icon: const Icon(Icons.directions),
                  label: const Text('Get Directions'),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppColors.primaryPurple),
                    foregroundColor: AppColors.primaryPurple,
                  ),
                ),
              ),
            ] else
              Text(
                'Location information not available',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textLight,
                  fontStyle: FontStyle.italic,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppColors.primaryPurple,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Statistics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    Icons.star,
                    provider.rating.toStringAsFixed(1),
                    'Rating',
                    AppColors.starYellow,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    Icons.reviews,
                    provider.reviewCount.toString(),
                    'Reviews',
                    AppColors.primaryPurple,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    Icons.calendar_today,
                    '${provider.services.length}',
                    'Services',
                    AppColors.success,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    IconData icon,
    String value,
    String label,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textLight,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => _bookNow(context),
            icon: const Icon(Icons.calendar_today),
            label: const Text('Book Appointment'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryPurple,
              foregroundColor: AppColors.textWhite,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _startChat(context),
                icon: const Icon(Icons.chat),
                label: const Text('Chat'),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: AppColors.primaryPurple),
                  foregroundColor: AppColors.primaryPurple,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _makePhoneCall(provider.phoneNumber ?? ''),
                icon: const Icon(Icons.phone),
                label: const Text('Call'),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: AppColors.success),
                  foregroundColor: AppColors.success,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    final uri = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _sendEmail(String email) async {
    final uri = Uri.parse('mailto:$email');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _openWebsite(String website) async {
    final uri = Uri.parse(website);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _openMaps() async {
    if (provider.location != null) {
      final uri = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=${provider.location!.latitude},${provider.location!.longitude}',
      );
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    }
  }

  void _bookNow(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/booking',
      arguments: {'providerId': provider.id},
    );
  }

  void _startChat(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: {
        'recipientId': provider.id,
        'recipientName': provider.name,
        'recipientImage': provider.profileImageUrl,
      },
    );
  }
}
