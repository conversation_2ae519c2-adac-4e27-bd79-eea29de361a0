import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';

class SocialLoginButtons extends StatelessWidget {
  final VoidCallback? onGooglePressed;
  final VoidCallback? onApplePressed;
  final VoidCallback? onAppGalleryPressed;
  final bool showAppGallery;

  const SocialLoginButtons({
    super.key,
    this.onGooglePressed,
    this.onApplePressed,
    this.onAppGalleryPressed,
    this.showAppGallery = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Divider with "أو"
        Row(
          children: [
            const Expanded(
              child: Divider(
                color: AppColors.borderGray,
                thickness: 1,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              child: Text(
                AppStrings.or,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.textGray,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Expanded(
              child: Divider(
                color: AppColors.borderGray,
                thickness: 1,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        // Google Sign In Button
        _SocialButton(
          onPressed: onGooglePressed,
          icon: Icons.g_mobiledata,
          text: AppStrings.signInWithGoogle,
          backgroundColor: Colors.white,
          textColor: AppColors.textDark,
          borderColor: AppColors.borderGray,
        ),
        
        const SizedBox(height: AppConstants.smallPadding),
        
        // Apple Sign In Button
        _SocialButton(
          onPressed: onApplePressed,
          icon: Icons.apple,
          text: AppStrings.signInWithApple,
          backgroundColor: Colors.black,
          textColor: Colors.white,
        ),
        
        if (showAppGallery) ...[
          const SizedBox(height: AppConstants.smallPadding),
          
          // AppGallery Sign In Button
          _SocialButton(
            onPressed: onAppGalleryPressed,
            icon: Icons.apps,
            text: AppStrings.signInWithAppGallery,
            backgroundColor: const Color(0xFF1976D2),
            textColor: Colors.white,
          ),
        ],
      ],
    );
  }
}

class _SocialButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String text;
  final Color backgroundColor;
  final Color textColor;
  final Color? borderColor;

  const _SocialButton({
    required this.onPressed,
    required this.icon,
    required this.text,
    required this.backgroundColor,
    required this.textColor,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          elevation: 1,
          shadowColor: Colors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            side: borderColor != null 
                ? BorderSide(color: borderColor!, width: 1)
                : BorderSide.none,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 20,
              color: textColor,
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Text(
              text,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Alternative compact social buttons (horizontal layout)
class CompactSocialButtons extends StatelessWidget {
  final VoidCallback? onGooglePressed;
  final VoidCallback? onApplePressed;
  final VoidCallback? onAppGalleryPressed;
  final bool showAppGallery;

  const CompactSocialButtons({
    super.key,
    this.onGooglePressed,
    this.onApplePressed,
    this.onAppGalleryPressed,
    this.showAppGallery = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Divider with "أو"
        Row(
          children: [
            const Expanded(
              child: Divider(
                color: AppColors.borderGray,
                thickness: 1,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              child: Text(
                AppStrings.or,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.textGray,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Expanded(
              child: Divider(
                color: AppColors.borderGray,
                thickness: 1,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppConstants.defaultPadding),
        
        // Social buttons in a row
        Row(
          children: [
            // Google
            Expanded(
              child: _CompactSocialButton(
                onPressed: onGooglePressed,
                icon: Icons.g_mobiledata,
                backgroundColor: Colors.white,
                iconColor: Colors.red,
                borderColor: AppColors.borderGray,
              ),
            ),
            
            const SizedBox(width: AppConstants.smallPadding),
            
            // Apple
            Expanded(
              child: _CompactSocialButton(
                onPressed: onApplePressed,
                icon: Icons.apple,
                backgroundColor: Colors.black,
                iconColor: Colors.white,
              ),
            ),
            
            if (showAppGallery) ...[
              const SizedBox(width: AppConstants.smallPadding),
              
              // AppGallery
              Expanded(
                child: _CompactSocialButton(
                  onPressed: onAppGalleryPressed,
                  icon: Icons.apps,
                  backgroundColor: const Color(0xFF1976D2),
                  iconColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }
}

class _CompactSocialButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final Color? borderColor;

  const _CompactSocialButton({
    required this.onPressed,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          elevation: 1,
          shadowColor: Colors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
            side: borderColor != null 
                ? BorderSide(color: borderColor!, width: 1)
                : BorderSide.none,
          ),
        ),
        child: Icon(
          icon,
          size: 24,
          color: iconColor,
        ),
      ),
    );
  }
}
