import 'package:equatable/equatable.dart';

/// نموذج فترة زمنية للحجز
class TimeSlotModel extends Equatable {
  final String id;
  final DateTime startTime;
  final DateTime endTime;
  final bool isAvailable;
  final bool isBooked;
  final String? bookingId;
  final double? price;
  final int durationMinutes;
  final String? providerId;
  final String? serviceId;
  final Map<String, dynamic>? metadata;

  const TimeSlotModel({
    required this.id,
    required this.startTime,
    required this.endTime,
    this.isAvailable = true,
    this.isBooked = false,
    this.bookingId,
    this.price,
    required this.durationMinutes,
    this.providerId,
    this.serviceId,
    this.metadata,
  });

  /// إنشاء TimeSlotModel من JSON
  factory TimeSlotModel.fromJson(Map<String, dynamic> json) {
    return TimeSlotModel(
      id: json['id'] ?? '',
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      isAvailable: json['isAvailable'] ?? true,
      isBooked: json['isBooked'] ?? false,
      bookingId: json['bookingId'],
      price: json['price']?.toDouble(),
      durationMinutes: json['durationMinutes'] ?? 60,
      providerId: json['providerId'],
      serviceId: json['serviceId'],
      metadata: json['metadata'],
    );
  }

  /// تحويل TimeSlotModel إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'isAvailable': isAvailable,
      'isBooked': isBooked,
      'bookingId': bookingId,
      'price': price,
      'durationMinutes': durationMinutes,
      'providerId': providerId,
      'serviceId': serviceId,
      'metadata': metadata,
    };
  }

  /// نسخ مع تعديل بعض الخصائص
  TimeSlotModel copyWith({
    String? id,
    DateTime? startTime,
    DateTime? endTime,
    bool? isAvailable,
    bool? isBooked,
    String? bookingId,
    double? price,
    int? durationMinutes,
    String? providerId,
    String? serviceId,
    Map<String, dynamic>? metadata,
  }) {
    return TimeSlotModel(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isAvailable: isAvailable ?? this.isAvailable,
      isBooked: isBooked ?? this.isBooked,
      bookingId: bookingId ?? this.bookingId,
      price: price ?? this.price,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      providerId: providerId ?? this.providerId,
      serviceId: serviceId ?? this.serviceId,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        startTime,
        endTime,
        isAvailable,
        isBooked,
        bookingId,
        price,
        durationMinutes,
        providerId,
        serviceId,
        metadata,
      ];

  /// الحصول على النص المنسق للوقت
  String get formattedTime {
    final start = _formatTime(startTime);
    final end = _formatTime(endTime);
    return '$start - $end';
  }

  /// الحصول على النص المنسق للتاريخ
  String get formattedDate {
    return '${startTime.day}/${startTime.month}/${startTime.year}';
  }

  /// فحص ما إذا كانت الفترة في الماضي
  bool get isPast {
    return endTime.isBefore(DateTime.now());
  }

  /// فحص ما إذا كانت الفترة في المستقبل
  bool get isFuture {
    return startTime.isAfter(DateTime.now());
  }

  /// فحص ما إذا كانت الفترة حالياً
  bool get isCurrent {
    final now = DateTime.now();
    return startTime.isBefore(now) && endTime.isAfter(now);
  }

  /// فحص ما إذا كانت الفترة قابلة للحجز
  bool get canBook {
    return isAvailable && !isBooked && isFuture;
  }

  /// تنسيق الوقت
  String _formatTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }

  /// إنشاء فترات زمنية لليوم الواحد
  static List<TimeSlotModel> generateDaySlots({
    required DateTime date,
    required int durationMinutes,
    required DateTime startTime,
    required DateTime endTime,
    String? providerId,
    String? serviceId,
    double? price,
  }) {
    final slots = <TimeSlotModel>[];
    var currentTime = DateTime(
      date.year,
      date.month,
      date.day,
      startTime.hour,
      startTime.minute,
    );
    
    final dayEndTime = DateTime(
      date.year,
      date.month,
      date.day,
      endTime.hour,
      endTime.minute,
    );

    while (currentTime.add(Duration(minutes: durationMinutes)).isBefore(dayEndTime) ||
           currentTime.add(Duration(minutes: durationMinutes)).isAtSameMomentAs(dayEndTime)) {
      final slotEndTime = currentTime.add(Duration(minutes: durationMinutes));
      
      slots.add(TimeSlotModel(
        id: '${currentTime.millisecondsSinceEpoch}',
        startTime: currentTime,
        endTime: slotEndTime,
        durationMinutes: durationMinutes,
        providerId: providerId,
        serviceId: serviceId,
        price: price,
      ));

      currentTime = slotEndTime;
    }

    return slots;
  }

  /// إنشاء فترات زمنية لعدة أيام
  static List<TimeSlotModel> generateWeekSlots({
    required DateTime startDate,
    required int days,
    required int durationMinutes,
    required DateTime dailyStartTime,
    required DateTime dailyEndTime,
    String? providerId,
    String? serviceId,
    double? price,
    List<int>? excludeWeekdays, // 1=Monday, 7=Sunday
  }) {
    final slots = <TimeSlotModel>[];
    
    for (int i = 0; i < days; i++) {
      final date = startDate.add(Duration(days: i));
      
      // تخطي الأيام المستثناة
      if (excludeWeekdays?.contains(date.weekday) == true) {
        continue;
      }
      
      final daySlots = generateDaySlots(
        date: date,
        durationMinutes: durationMinutes,
        startTime: dailyStartTime,
        endTime: dailyEndTime,
        providerId: providerId,
        serviceId: serviceId,
        price: price,
      );
      
      slots.addAll(daySlots);
    }

    return slots;
  }

  /// تجميع الفترات حسب التاريخ
  static Map<String, List<TimeSlotModel>> groupByDate(List<TimeSlotModel> slots) {
    final grouped = <String, List<TimeSlotModel>>{};
    
    for (final slot in slots) {
      final dateKey = slot.formattedDate;
      grouped.putIfAbsent(dateKey, () => []).add(slot);
    }
    
    // ترتيب الفترات داخل كل يوم
    for (final daySlots in grouped.values) {
      daySlots.sort((a, b) => a.startTime.compareTo(b.startTime));
    }
    
    return grouped;
  }

  /// تصفية الفترات المتاحة
  static List<TimeSlotModel> filterAvailable(List<TimeSlotModel> slots) {
    return slots.where((slot) => slot.canBook).toList();
  }

  /// تصفية الفترات المحجوزة
  static List<TimeSlotModel> filterBooked(List<TimeSlotModel> slots) {
    return slots.where((slot) => slot.isBooked).toList();
  }

  /// البحث عن فترة بالمعرف
  static TimeSlotModel? findById(List<TimeSlotModel> slots, String id) {
    try {
      return slots.firstWhere((slot) => slot.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'TimeSlotModel(id: $id, time: $formattedTime, available: $isAvailable, booked: $isBooked)';
  }
}
