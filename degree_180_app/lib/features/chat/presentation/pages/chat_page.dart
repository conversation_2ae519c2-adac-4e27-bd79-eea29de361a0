import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/chat_model.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../widgets/message_bubble.dart';
import '../widgets/chat_input_widget.dart';
import '../../../../core/widgets/typing_indicator.dart';

class ChatPage extends StatefulWidget {
  final String chatId;
  final String? recipientName;
  final String? recipientImage;

  const ChatPage({
    super.key,
    required this.chatId,
    this.recipientName,
    this.recipientImage,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  final FocusNode _messageFocusNode = FocusNode();
  
  List<MessageModel> _messages = [];
  bool _isTyping = false;
  bool _isOnline = true;

  @override
  void initState() {
    super.initState();
    _loadMessages();
    _simulateTyping();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    _messageFocusNode.dispose();
    super.dispose();
  }

  void _loadMessages() {
    // Mock messages data
    _messages = [
      MessageModel(
        id: '1',
        chatId: widget.chatId,
        senderId: 'provider_1',
        senderName: widget.recipientName ?? 'Provider',
        message: 'Hello! Thank you for your interest in our services. How can I help you today?',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: true,
        messageType: MessageType.text,
      ),
      MessageModel(
        id: '2',
        chatId: widget.chatId,
        senderId: 'user_1',
        senderName: 'You',
        message: 'Hi! I\'d like to book an appointment for a haircut. What times do you have available tomorrow?',
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 45)),
        isRead: true,
        messageType: MessageType.text,
      ),
      MessageModel(
        id: '3',
        chatId: widget.chatId,
        senderId: 'provider_1',
        senderName: widget.recipientName ?? 'Provider',
        message: 'Great! We have availability tomorrow at 10:00 AM, 2:00 PM, and 4:00 PM. Which time works best for you?',
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
        isRead: true,
        messageType: MessageType.text,
      ),
      MessageModel(
        id: '4',
        chatId: widget.chatId,
        senderId: 'user_1',
        senderName: 'You',
        message: '2:00 PM would be perfect! How much will it cost?',
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        isRead: true,
        messageType: MessageType.text,
      ),
      MessageModel(
        id: '5',
        chatId: widget.chatId,
        senderId: 'provider_1',
        senderName: widget.recipientName ?? 'Provider',
        message: 'Perfect! A standard haircut is 15 KWD. Would you like to book the 2:00 PM slot?',
        timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
        isRead: false,
        messageType: MessageType.text,
      ),
    ];
  }

  void _simulateTyping() {
    // Simulate typing indicator
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isTyping = true;
        });
        
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              _isTyping = false;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // Messages List
          Expanded(
            child: _buildMessagesList(),
          ),
          
          // Typing Indicator
          if (_isTyping)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 12,
                    backgroundImage: widget.recipientImage != null
                        ? CachedNetworkImageProvider(widget.recipientImage!)
                        : null,
                    backgroundColor: AppColors.primaryPurple.withValues(alpha: 0.1),
                    child: widget.recipientImage == null
                        ? Icon(
                            Icons.person,
                            size: 14,
                            color: AppColors.primaryPurple,
                          )
                        : null,
                  ),
                  const SizedBox(width: 8),
                  const SimpleTypingIndicator(),
                ],
              ),
            ),
          
          // Message Input
          ChatInputWidget(
            controller: _messageController,
            focusNode: _messageFocusNode,
            onSendMessage: _sendMessage,
            onSendImage: _sendImage,
            onSendVoice: _sendVoice,
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryPurple,
      foregroundColor: AppColors.textWhite,
      title: Row(
        children: [
          // Recipient Avatar
          CircleAvatar(
            radius: 18,
            backgroundImage: widget.recipientImage != null
                ? CachedNetworkImageProvider(widget.recipientImage!)
                : null,
            backgroundColor: AppColors.textWhite.withValues(alpha: 0.2),
            child: widget.recipientImage == null
                ? Icon(
                    Icons.person,
                    color: AppColors.textWhite,
                    size: 20,
                  )
                : null,
          ),
          
          const SizedBox(width: 12),
          
          // Recipient Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.recipientName ?? 'Chat',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _isOnline ? 'Online' : 'Last seen recently',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textWhite.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: _makeCall,
          icon: const Icon(Icons.phone),
        ),
        IconButton(
          onPressed: _makeVideoCall,
          icon: const Icon(Icons.videocam),
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view_profile',
              child: Row(
                children: [
                  Icon(Icons.person),
                  SizedBox(width: 8),
                  Text('View Profile'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'book_appointment',
              child: Row(
                children: [
                  Icon(Icons.calendar_today),
                  SizedBox(width: 8),
                  Text('Book Appointment'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'block',
              child: Row(
                children: [
                  Icon(Icons.block, color: AppColors.error),
                  SizedBox(width: 8),
                  Text('Block', style: TextStyle(color: AppColors.error)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMessagesList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        final isMe = message.senderId == 'user_1'; // Current user ID
        final showAvatar = index == _messages.length - 1 || 
                          _messages[index + 1].senderId != message.senderId;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: MessageBubble(
            message: message,
            isMe: isMe,
            showAvatar: showAvatar,
            recipientImage: widget.recipientImage,
          ),
        );
      },
    );
  }

  void _sendMessage(String text) {
    if (text.trim().isEmpty) return;

    final newMessage = MessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      chatId: widget.chatId,
      senderId: 'user_1',
      senderName: 'You',
      message: text.trim(),
      timestamp: DateTime.now(),
      isRead: false,
      messageType: MessageType.text,
    );

    setState(() {
      _messages.add(newMessage);
    });

    _messageController.clear();
    _scrollToBottom();

    // Simulate response
    _simulateResponse();
  }

  void _sendImage() {
    // Implement image sending
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Image sending coming soon!'),
      ),
    );
  }

  void _sendVoice() {
    // Implement voice message sending
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voice messages coming soon!'),
      ),
    );
  }

  void _simulateResponse() {
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isTyping = true;
        });
        
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            final responses = [
              'Great! I\'ll book that slot for you. Can you please confirm your phone number?',
              'Perfect! Your appointment is confirmed for tomorrow at 2:00 PM.',
              'Thank you! Is there anything specific you\'d like me to know about your hair?',
              'Excellent! We look forward to seeing you tomorrow.',
            ];
            
            final response = MessageModel(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              chatId: widget.chatId,
              senderId: 'provider_1',
              senderName: widget.recipientName ?? 'Provider',
              message: responses[DateTime.now().millisecond % responses.length],
              timestamp: DateTime.now(),
              isRead: false,
              messageType: MessageType.text,
            );

            setState(() {
              _isTyping = false;
              _messages.add(response);
            });
            
            _scrollToBottom();
          }
        });
      }
    });
  }

  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _makeCall() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling ${widget.recipientName ?? 'provider'}...'),
      ),
    );
  }

  void _makeVideoCall() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting video call with ${widget.recipientName ?? 'provider'}...'),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'view_profile':
        Navigator.pushNamed(context, '/provider-details');
        break;
      case 'book_appointment':
        Navigator.pushNamed(context, '/booking');
        break;
      case 'block':
        _showBlockConfirmation();
        break;
    }
  }

  void _showBlockConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: Text('Are you sure you want to block ${widget.recipientName ?? 'this user'}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${widget.recipientName ?? 'User'} has been blocked'),
                  backgroundColor: AppColors.error,
                ),
              );
            },
            child: Text(
              'Block',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
