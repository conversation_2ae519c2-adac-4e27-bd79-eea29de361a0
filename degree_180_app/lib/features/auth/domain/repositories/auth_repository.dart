import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';

abstract class AuthRepository {
  // Authentication Methods
  Future<Either<Failure, UserEntity>> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  Future<Either<Failure, UserEntity>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    required UserType userType,
    ServiceType? serviceType,
  });

  Future<Either<Failure, UserEntity>> signInWithGoogle();

  Future<Either<Failure, UserEntity>> signInWithApple();

  Future<Either<Failure, void>> signOut();

  Future<Either<Failure, void>> sendPasswordResetEmail({
    required String email,
  });

  Future<Either<Failure, void>> sendEmailVerification();

  Future<Either<Failure, UserEntity>> getCurrentUser();

  Future<Either<Failure, bool>> isUserSignedIn();

  // User Profile Methods
  Future<Either<Failure, UserEntity>> updateUserProfile({
    String? name,
    String? phoneNumber,
    String? profileImageUrl,
  });

  Future<Either<Failure, void>> updatePassword({
    required String currentPassword,
    required String newPassword,
  });

  Future<Either<Failure, void>> deleteAccount();

  // User Data Methods
  Future<Either<Failure, UserEntity>> getUserById(String userId);

  Future<Either<Failure, List<UserEntity>>> getServiceProviders({
    ServiceType? serviceType,
    int? limit,
  });

  Future<Either<Failure, void>> updateUserStatus({
    required bool isActive,
  });

  // Stream Methods
  Stream<UserEntity?> get authStateChanges;
  
  Stream<UserEntity?> get userChanges;
}
