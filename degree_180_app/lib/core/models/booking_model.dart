import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import '../enums/booking_status.dart';
import 'service_model.dart';
import 'location_model.dart';

class BookingModel extends Equatable {
  final String id;
  final String customerId;
  final String customerName;
  final String? customerPhone;
  final String? customerEmail;
  final String providerId;
  final String providerName;
  final String serviceId;
  final String serviceName; // اسم الخدمة
  final ServiceModel service;
  final DateTime bookingDate;
  final DateTime appointmentDateTime; // وقت الموعد الكامل
  final String timeSlot;
  final BookingStatus status;
  final double totalAmount;
  final String currency;
  final double? discountAmount;
  final String? couponCode;
  final String? notes;
  final LocationModel? location;
  final Map<String, dynamic>? paymentInfo;
  final String? cancellationReason;
  final DateTime? cancellationDate;
  final String? cancellationBy;
  final bool isReminderSent;
  final bool isReviewSubmitted;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BookingModel({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.customerPhone,
    this.customerEmail,
    required this.providerId,
    required this.providerName,
    required this.serviceId,
    required this.serviceName,
    required this.service,
    required this.bookingDate,
    required this.appointmentDateTime,
    required this.timeSlot,
    required this.status,
    required this.totalAmount,
    this.currency = 'KD',
    this.discountAmount,
    this.couponCode,
    this.notes,
    this.location,
    this.paymentInfo,
    this.cancellationReason,
    this.cancellationDate,
    this.cancellationBy,
    this.isReminderSent = false,
    this.isReviewSubmitted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id'] ?? '',
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerPhone: json['customerPhone'],
      customerEmail: json['customerEmail'],
      providerId: json['providerId'] ?? '',
      providerName: json['providerName'] ?? '',
      serviceId: json['serviceId'] ?? '',
      serviceName: json['serviceName'] ?? '',
      service: ServiceModel.fromJson(json['service'] ?? {}),
      bookingDate: _parseDateTime(json['bookingDate']),
      appointmentDateTime: _parseDateTime(json['appointmentDateTime'] ?? json['bookingDate']),
      timeSlot: json['timeSlot'] ?? '',
      status: BookingStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => BookingStatus.pending,
      ),
      totalAmount: (json['totalAmount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'KD',
      discountAmount: json['discountAmount']?.toDouble(),
      couponCode: json['couponCode'],
      notes: json['notes'],
      location: json['location'] != null 
          ? LocationModel.fromJson(json['location']) 
          : null,
      paymentInfo: json['paymentInfo'],
      cancellationReason: json['cancellationReason'],
      cancellationDate: json['cancellationDate'] != null 
          ? _parseDateTime(json['cancellationDate']) 
          : null,
      cancellationBy: json['cancellationBy'],
      isReminderSent: json['isReminderSent'] ?? false,
      isReviewSubmitted: json['isReviewSubmitted'] ?? false,
      createdAt: _parseDateTime(json['createdAt']),
      updatedAt: _parseDateTime(json['updatedAt']),
    );
  }

  factory BookingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    data['id'] = doc.id;
    return BookingModel.fromJson(data);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerEmail': customerEmail,
      'providerId': providerId,
      'providerName': providerName,
      'serviceId': serviceId,
      'serviceName': serviceName,
      'service': service.toJson(),
      'bookingDate': Timestamp.fromDate(bookingDate),
      'appointmentDateTime': Timestamp.fromDate(appointmentDateTime),
      'timeSlot': timeSlot,
      'status': status.toString().split('.').last,
      'totalAmount': totalAmount,
      'currency': currency,
      'discountAmount': discountAmount,
      'couponCode': couponCode,
      'notes': notes,
      'location': location?.toJson(),
      'paymentInfo': paymentInfo,
      'cancellationReason': cancellationReason,
      'cancellationDate': cancellationDate != null 
          ? Timestamp.fromDate(cancellationDate!) 
          : null,
      'cancellationBy': cancellationBy,
      'isReminderSent': isReminderSent,
      'isReviewSubmitted': isReviewSubmitted,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() {
    final json = toJson();
    json.remove('id');
    return json;
  }

  BookingModel copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? customerEmail,
    String? providerId,
    String? providerName,
    String? serviceId,
    String? serviceName,
    ServiceModel? service,
    DateTime? bookingDate,
    DateTime? appointmentDateTime,
    String? timeSlot,
    BookingStatus? status,
    double? totalAmount,
    String? currency,
    double? discountAmount,
    String? couponCode,
    String? notes,
    LocationModel? location,
    Map<String, dynamic>? paymentInfo,
    String? cancellationReason,
    DateTime? cancellationDate,
    String? cancellationBy,
    bool? isReminderSent,
    bool? isReviewSubmitted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookingModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerEmail: customerEmail ?? this.customerEmail,
      providerId: providerId ?? this.providerId,
      providerName: providerName ?? this.providerName,
      serviceId: serviceId ?? this.serviceId,
      serviceName: serviceName ?? this.serviceName,
      service: service ?? this.service,
      bookingDate: bookingDate ?? this.bookingDate,
      appointmentDateTime: appointmentDateTime ?? this.appointmentDateTime,
      timeSlot: timeSlot ?? this.timeSlot,
      status: status ?? this.status,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      discountAmount: discountAmount ?? this.discountAmount,
      couponCode: couponCode ?? this.couponCode,
      notes: notes ?? this.notes,
      location: location ?? this.location,
      paymentInfo: paymentInfo ?? this.paymentInfo,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancellationDate: cancellationDate ?? this.cancellationDate,
      cancellationBy: cancellationBy ?? this.cancellationBy,
      isReminderSent: isReminderSent ?? this.isReminderSent,
      isReviewSubmitted: isReviewSubmitted ?? this.isReviewSubmitted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get formattedDate {
    return '${bookingDate.day}/${bookingDate.month}/${bookingDate.year}';
  }

  String get formattedTime {
    return timeSlot;
  }

  String get formattedAmount {
    return '${totalAmount.toStringAsFixed(1)} $currency';
  }

  String get statusDisplayName {
    switch (status) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.inProgress:
        return 'In Progress';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.noShow:
        return 'No Show';
    }
  }

  bool get canBeCancelled {
    return status == BookingStatus.pending || status == BookingStatus.confirmed;
  }

  bool get canBeModified {
    return status == BookingStatus.pending;
  }

  bool get isUpcoming {
    final now = DateTime.now();
    return bookingDate.isAfter(now) && 
           (status == BookingStatus.pending || status == BookingStatus.confirmed);
  }

  bool get isPast {
    final now = DateTime.now();
    return bookingDate.isBefore(now);
  }

  Duration get timeUntilBooking {
    final now = DateTime.now();
    return bookingDate.difference(now);
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is String) return DateTime.parse(value);
    if (value is DateTime) return value;
    return DateTime.now();
  }

  @override
  List<Object?> get props => [
        id,
        customerId,
        customerName,
        customerPhone,
        customerEmail,
        providerId,
        providerName,
        serviceId,
        serviceName,
        service,
        bookingDate,
        appointmentDateTime,
        timeSlot,
        status,
        totalAmount,
        currency,
        discountAmount,
        couponCode,
        notes,
        location,
        paymentInfo,
        cancellationReason,
        cancellationDate,
        cancellationBy,
        isReminderSent,
        isReviewSubmitted,
        createdAt,
        updatedAt,
      ];

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerEmail': customerEmail,
      'providerId': providerId,
      'providerName': providerName,
      'serviceId': serviceId,
      'service': {
        'id': service.id,
        'name': service.name,
        'category': service.category,
        'price': service.price,
      },
      'bookingDate': Timestamp.fromDate(bookingDate),
      'timeSlot': timeSlot,
      'status': status.name,
      'totalAmount': totalAmount,
      'currency': currency,
      'discountAmount': discountAmount,
      'couponCode': couponCode,
      'notes': notes,
      'location': location?.toMap(),
      'paymentInfo': paymentInfo,
      'cancellationReason': cancellationReason,
      'cancellationDate': cancellationDate != null ? Timestamp.fromDate(cancellationDate!) : null,
      'cancellationBy': cancellationBy,
      'isReminderSent': isReminderSent,
      'isReviewSubmitted': isReviewSubmitted,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  String get serviceType => service.category;

  @override
  String toString() {
    return 'BookingModel(id: $id, service: ${service.name}, date: $formattedDate, status: $statusDisplayName)';
  }
}

/// معاملات إنشاء الحجز
class CreateBookingParams {
  final String customerId;
  final String customerName;
  final String? customerPhone;
  final String? customerEmail;
  final String providerId;
  final String providerName;
  final String serviceId;
  final String serviceName;
  final ServiceModel service;
  final DateTime bookingDate;
  final DateTime appointmentDateTime;
  final String timeSlot;
  final double totalAmount;
  final String currency;
  final String? notes;
  final LocationModel? location;

  const CreateBookingParams({
    required this.customerId,
    required this.customerName,
    this.customerPhone,
    this.customerEmail,
    required this.providerId,
    required this.providerName,
    required this.serviceId,
    required this.serviceName,
    required this.service,
    required this.bookingDate,
    required this.appointmentDateTime,
    required this.timeSlot,
    required this.totalAmount,
    required this.currency,
    this.notes,
    this.location,
  });
}
