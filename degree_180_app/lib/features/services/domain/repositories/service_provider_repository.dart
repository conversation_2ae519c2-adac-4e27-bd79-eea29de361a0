import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/models/service_provider_model.dart';
import '../../../../core/models/service_model.dart';
import '../../../../core/models/review_model.dart';
import '../../../../core/models/location_model.dart';
import '../../../../core/enums/service_type.dart';

abstract class ServiceProviderRepository {
  /// Get list of service providers with optional filters
  Future<Either<Failure, List<ServiceProviderModel>>> getServiceProviders({
    ServiceType? serviceType,
    LocationModel? userLocation,
    double? radiusKm,
    int? limit,
    String? lastDocumentId,
    bool? isVerified,
    bool? isOnline,
    double? minRating,
  });

  /// Get a specific service provider by ID
  Future<Either<Failure, ServiceProviderModel>> getServiceProviderById(
    String providerId,
  );

  /// Get services offered by a specific provider
  Future<Either<Failure, List<ServiceModel>>> getProviderServices(
    String providerId, {
    bool? isActive,
    String? category,
  });

  /// Get reviews for a specific provider
  Future<Either<Failure, List<ReviewModel>>> getProviderReviews(
    String providerId, {
    int? limit,
    String? lastDocumentId,
    double? minRating,
  });

  /// Search service providers by name or description
  Future<Either<Failure, List<ServiceProviderModel>>> searchServiceProviders(
    String query, {
    ServiceType? serviceType,
    LocationModel? userLocation,
    double? radiusKm,
    int? limit,
  });

  /// Get nearby service providers
  Future<Either<Failure, List<ServiceProviderModel>>> getNearbyProviders(
    LocationModel userLocation, {
    double radiusKm = 10.0,
    ServiceType? serviceType,
    int? limit,
  });

  /// Get popular/featured service providers
  Future<Either<Failure, List<ServiceProviderModel>>> getFeaturedProviders({
    ServiceType? serviceType,
    int? limit,
  });

  /// Get provider availability for a specific date
  Future<Either<Failure, List<String>>> getProviderAvailability(
    String providerId,
    DateTime date,
  );

  /// Update provider online status
  Future<Either<Failure, void>> updateProviderOnlineStatus(
    String providerId,
    bool isOnline,
  );

  /// Add a new service to provider
  Future<Either<Failure, ServiceModel>> addProviderService(
    String providerId,
    ServiceModel service,
  );

  /// Update an existing service
  Future<Either<Failure, ServiceModel>> updateProviderService(
    String providerId,
    ServiceModel service,
  );

  /// Delete a service
  Future<Either<Failure, void>> deleteProviderService(
    String providerId,
    String serviceId,
  );

  /// Get provider statistics
  Future<Either<Failure, Map<String, dynamic>>> getProviderStatistics(
    String providerId,
  );

  /// Update provider profile
  Future<Either<Failure, ServiceProviderModel>> updateProviderProfile(
    ServiceProviderModel provider,
  );

  /// Upload provider gallery images
  Future<Either<Failure, List<String>>> uploadProviderGallery(
    String providerId,
    List<String> imagePaths,
  );

  /// Delete provider gallery image
  Future<Either<Failure, void>> deleteProviderGalleryImage(
    String providerId,
    String imageUrl,
  );
}
