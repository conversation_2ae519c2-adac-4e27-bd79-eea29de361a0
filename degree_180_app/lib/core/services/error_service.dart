import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'analytics_service.dart';
import 'connectivity_service.dart';

enum ErrorType {
  network,
  authentication,
  authorization,
  validation,
  business,
  system,
  unknown,
}

enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

class AppError {
  final String id;
  final ErrorType type;
  final ErrorSeverity severity;
  final String message;
  final String? userMessage;
  final String? code;
  final Map<String, dynamic>? details;
  final StackTrace? stackTrace;
  final DateTime timestamp;
  final String? userId;
  final String? sessionId;
  final Map<String, dynamic>? context;

  const AppError({
    required this.id,
    required this.type,
    required this.severity,
    required this.message,
    this.userMessage,
    this.code,
    this.details,
    this.stackTrace,
    required this.timestamp,
    this.userId,
    this.sessionId,
    this.context,
  });

  factory AppError.fromException(
    dynamic exception, {
    StackTrace? stackTrace,
    String? userId,
    String? sessionId,
    Map<String, dynamic>? context,
  }) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final timestamp = DateTime.now();

    if (exception is DioException) {
      return AppError._fromDioException(
        exception,
        id: id,
        stackTrace: stackTrace,
        timestamp: timestamp,
        userId: userId,
        sessionId: sessionId,
        context: context,
      );
    }

    if (exception is FormatException) {
      return AppError(
        id: id,
        type: ErrorType.validation,
        severity: ErrorSeverity.medium,
        message: 'Data format error: ${exception.message}',
        userMessage: 'Invalid data format received',
        code: 'FORMAT_ERROR',
        details: {'source': exception.source},
        stackTrace: stackTrace,
        timestamp: timestamp,
        userId: userId,
        sessionId: sessionId,
        context: context,
      );
    }

    if (exception is TimeoutException) {
      return AppError(
        id: id,
        type: ErrorType.network,
        severity: ErrorSeverity.medium,
        message: 'Operation timed out: ${exception.message}',
        userMessage: 'Request timed out. Please try again.',
        code: 'TIMEOUT_ERROR',
        details: {'duration': exception.duration?.inSeconds},
        stackTrace: stackTrace,
        timestamp: timestamp,
        userId: userId,
        sessionId: sessionId,
        context: context,
      );
    }

    // Generic error
    return AppError(
      id: id,
      type: ErrorType.unknown,
      severity: ErrorSeverity.medium,
      message: exception.toString(),
      userMessage: 'An unexpected error occurred',
      code: 'UNKNOWN_ERROR',
      stackTrace: stackTrace,
      timestamp: timestamp,
      userId: userId,
      sessionId: sessionId,
      context: context,
    );
  }

  factory AppError._fromDioException(
    DioException exception, {
    required String id,
    StackTrace? stackTrace,
    required DateTime timestamp,
    String? userId,
    String? sessionId,
    Map<String, dynamic>? context,
  }) {
    ErrorType type;
    ErrorSeverity severity;
    String message;
    String userMessage;
    String code;
    Map<String, dynamic>? details;

    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        type = ErrorType.network;
        severity = ErrorSeverity.medium;
        message = 'Network timeout: ${exception.message}';
        userMessage = 'Connection timed out. Please check your internet connection.';
        code = 'NETWORK_TIMEOUT';
        break;

      case DioExceptionType.connectionError:
        type = ErrorType.network;
        severity = ErrorSeverity.high;
        message = 'Connection error: ${exception.message}';
        userMessage = 'Unable to connect. Please check your internet connection.';
        code = 'CONNECTION_ERROR';
        break;

      case DioExceptionType.badResponse:
        final statusCode = exception.response?.statusCode;
        if (statusCode == 401) {
          type = ErrorType.authentication;
          severity = ErrorSeverity.high;
          message = 'Authentication failed';
          userMessage = 'Please log in again';
          code = 'AUTH_ERROR';
        } else if (statusCode == 403) {
          type = ErrorType.authorization;
          severity = ErrorSeverity.medium;
          message = 'Access denied';
          userMessage = 'You don\'t have permission to perform this action';
          code = 'ACCESS_DENIED';
        } else if (statusCode != null && statusCode >= 400 && statusCode < 500) {
          type = ErrorType.validation;
          severity = ErrorSeverity.medium;
          message = 'Client error: ${exception.response?.statusMessage}';
          userMessage = 'Invalid request. Please try again.';
          code = 'CLIENT_ERROR';
        } else {
          type = ErrorType.system;
          severity = ErrorSeverity.high;
          message = 'Server error: ${exception.response?.statusMessage}';
          userMessage = 'Server error. Please try again later.';
          code = 'SERVER_ERROR';
        }
        
        details = {
          'statusCode': statusCode,
          'statusMessage': exception.response?.statusMessage,
          'responseData': exception.response?.data,
        };
        break;

      case DioExceptionType.cancel:
        type = ErrorType.system;
        severity = ErrorSeverity.low;
        message = 'Request cancelled';
        userMessage = 'Request was cancelled';
        code = 'REQUEST_CANCELLED';
        break;

      case DioExceptionType.unknown:
      default:
        type = ErrorType.unknown;
        severity = ErrorSeverity.medium;
        message = 'Unknown network error: ${exception.message}';
        userMessage = 'An unexpected error occurred';
        code = 'UNKNOWN_NETWORK_ERROR';
        break;
    }

    return AppError(
      id: id,
      type: type,
      severity: severity,
      message: message,
      userMessage: userMessage,
      code: code,
      details: details,
      stackTrace: stackTrace,
      timestamp: timestamp,
      userId: userId,
      sessionId: sessionId,
      context: context,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'severity': severity.name,
      'message': message,
      'userMessage': userMessage,
      'code': code,
      'details': details,
      'stackTrace': stackTrace?.toString(),
      'timestamp': timestamp.toIso8601String(),
      'userId': userId,
      'sessionId': sessionId,
      'context': context,
    };
  }

  @override
  String toString() {
    return 'AppError(id: $id, type: $type, severity: $severity, message: $message)';
  }
}

class ErrorService {
  static final ErrorService _instance = ErrorService._internal();
  factory ErrorService() => _instance;
  ErrorService._internal();

  final AnalyticsService _analytics = AnalyticsService();
  final ConnectivityService _connectivity = ConnectivityService();
  
  final List<AppError> _errorHistory = [];
  final StreamController<AppError> _errorController = StreamController<AppError>.broadcast();
  
  String? _currentUserId;
  String? _currentSessionId;
  
  // Configuration
  static const int _maxErrorHistory = 100;
  static const Duration _errorReportingCooldown = Duration(minutes: 1);
  final Map<String, DateTime> _lastReportedErrors = {};

  Stream<AppError> get errorStream => _errorController.stream;
  List<AppError> get errorHistory => List.unmodifiable(_errorHistory);

  void initialize({String? userId, String? sessionId}) {
    _currentUserId = userId;
    _currentSessionId = sessionId;
    debugPrint('ErrorService initialized');
  }

  void setUser(String userId) {
    _currentUserId = userId;
  }

  void setSession(String sessionId) {
    _currentSessionId = sessionId;
  }

  Future<void> reportError(
    dynamic error, {
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
    bool notify = true,
  }) async {
    try {
      final appError = AppError.fromException(
        error,
        stackTrace: stackTrace,
        userId: _currentUserId,
        sessionId: _currentSessionId,
        context: {
          'connectivity': _connectivity.currentStatus.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          ...?context,
        },
      );

      await _processError(appError, notify: notify);
    } catch (e) {
      debugPrint('Error reporting error: $e');
    }
  }

  Future<void> reportCustomError({
    required ErrorType type,
    required ErrorSeverity severity,
    required String message,
    String? userMessage,
    String? code,
    Map<String, dynamic>? details,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
    bool notify = true,
  }) async {
    try {
      final appError = AppError(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: type,
        severity: severity,
        message: message,
        userMessage: userMessage,
        code: code,
        details: details,
        stackTrace: stackTrace,
        timestamp: DateTime.now(),
        userId: _currentUserId,
        sessionId: _currentSessionId,
        context: {
          'connectivity': _connectivity.currentStatus.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          ...?context,
        },
      );

      await _processError(appError, notify: notify);
    } catch (e) {
      debugPrint('Error reporting custom error: $e');
    }
  }

  Future<void> _processError(AppError error, {bool notify = true}) async {
    // Add to history
    _addToHistory(error);

    // Log to console in debug mode
    if (kDebugMode) {
      debugPrint('Error reported: ${error.toString()}');
      if (error.stackTrace != null) {
        debugPrint('Stack trace: ${error.stackTrace}');
      }
    }

    // Report to analytics (with cooldown to prevent spam)
    if (_shouldReportToAnalytics(error)) {
      await _reportToAnalytics(error);
      _lastReportedErrors[error.code ?? error.type.name] = DateTime.now();
    }

    // Notify listeners
    if (notify) {
      _errorController.add(error);
    }
  }

  void _addToHistory(AppError error) {
    _errorHistory.add(error);
    
    // Keep only the last N errors
    if (_errorHistory.length > _maxErrorHistory) {
      _errorHistory.removeAt(0);
    }
  }

  bool _shouldReportToAnalytics(AppError error) {
    final key = error.code ?? error.type.name;
    final lastReported = _lastReportedErrors[key];
    
    if (lastReported == null) return true;
    
    return DateTime.now().difference(lastReported) > _errorReportingCooldown;
  }

  Future<void> _reportToAnalytics(AppError error) async {
    try {
      await _analytics.recordError(
        error.message,
        error.stackTrace,
        fatal: error.severity == ErrorSeverity.critical,
        customKeys: {
          'error_id': error.id,
          'error_type': error.type.name,
          'error_severity': error.severity.name,
          'error_code': error.code ?? 'unknown',
          'user_id': error.userId ?? 'anonymous',
          'session_id': error.sessionId ?? 'unknown',
          if (error.details != null) ...error.details!,
          if (error.context != null) ...error.context!,
        },
      );
    } catch (e) {
      debugPrint('Failed to report error to analytics: $e');
    }
  }

  // Error recovery suggestions
  String getRecoveryMessage(AppError error) {
    switch (error.type) {
      case ErrorType.network:
        if (!_connectivity.isConnected) {
          return 'Please check your internet connection and try again.';
        }
        return 'Network error occurred. Please try again.';
        
      case ErrorType.authentication:
        return 'Please log in again to continue.';
        
      case ErrorType.authorization:
        return 'You don\'t have permission to perform this action.';
        
      case ErrorType.validation:
        return 'Please check your input and try again.';
        
      case ErrorType.business:
        return error.userMessage ?? 'Business rule violation occurred.';
        
      case ErrorType.system:
        return 'System error occurred. Please try again later.';
        
      case ErrorType.unknown:
        return error.userMessage ?? 'An unexpected error occurred. Please try again.';
    }
  }

  bool canRetry(AppError error) {
    switch (error.type) {
      case ErrorType.network:
        return true;
      case ErrorType.system:
        return error.severity != ErrorSeverity.critical;
      case ErrorType.authentication:
      case ErrorType.authorization:
      case ErrorType.validation:
      case ErrorType.business:
        return false;
      case ErrorType.unknown:
        return error.severity == ErrorSeverity.low || error.severity == ErrorSeverity.medium;
    }
  }

  // Error statistics
  Map<ErrorType, int> getErrorStatistics({Duration? period}) {
    final cutoff = period != null ? DateTime.now().subtract(period) : null;
    final relevantErrors = cutoff != null 
        ? _errorHistory.where((e) => e.timestamp.isAfter(cutoff)).toList()
        : _errorHistory;

    final stats = <ErrorType, int>{};
    for (final error in relevantErrors) {
      stats[error.type] = (stats[error.type] ?? 0) + 1;
    }
    
    return stats;
  }

  List<AppError> getErrorsByType(ErrorType type, {int? limit}) {
    final errors = _errorHistory.where((e) => e.type == type).toList();
    errors.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    if (limit != null && errors.length > limit) {
      return errors.take(limit).toList();
    }
    
    return errors;
  }

  List<AppError> getCriticalErrors({Duration? period}) {
    final cutoff = period != null ? DateTime.now().subtract(period) : null;
    return _errorHistory
        .where((e) => e.severity == ErrorSeverity.critical)
        .where((e) => cutoff == null || e.timestamp.isAfter(cutoff))
        .toList();
  }

  void clearErrorHistory() {
    _errorHistory.clear();
  }

  void dispose() {
    _errorController.close();
  }
}

// Global error handler
void setupGlobalErrorHandling() {
  final errorService = ErrorService();
  
  // Handle Flutter framework errors
  FlutterError.onError = (FlutterErrorDetails details) {
    errorService.reportError(
      details.exception,
      stackTrace: details.stack,
      context: {
        'library': details.library,
        'context': details.context?.toString(),
        'silent': details.silent,
      },
      notify: !details.silent,
    );
  };

  // Handle platform errors
  PlatformDispatcher.instance.onError = (error, stack) {
    errorService.reportError(
      error,
      stackTrace: stack,
      context: {'source': 'platform'},
    );
    return true;
  };
}
