import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';

import 'package:degree_180_app/features/auth/domain/entities/user_entity.dart';
import 'package:degree_180_app/features/auth/domain/repositories/auth_repository.dart';
import 'package:degree_180_app/features/auth/domain/usecases/sign_in_with_email_usecase.dart';
import 'package:degree_180_app/core/errors/failures.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  late SignInWithEmailUseCase usecase;
  late MockAuthRepository mockAuthRepository;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    usecase = SignInWithEmailUseCase(mockAuthRepository);
  });

  const testEmail = '<EMAIL>';
  const testPassword = 'password123';
  
  final testUser = UserEntity(
    id: '1',
    uid: '1',
    email: testEmail,
    name: 'Test User',
    userType: UserType.customer,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  test(
    'should get user from the repository when sign in is successful',
    () async {
      // arrange
      when(() => mockAuthRepository.signInWithEmailAndPassword(
            email: any(named: 'email'),
            password: any(named: 'password'),
          )).thenAnswer((_) async => Right(testUser));

      // act
      final result = await usecase(const SignInWithEmailParams(
        email: testEmail,
        password: testPassword,
      ));

      // assert
      expect(result, Right(testUser));
      verify(() => mockAuthRepository.signInWithEmailAndPassword(
            email: testEmail,
            password: testPassword,
          ));
      verifyNoMoreInteractions(mockAuthRepository);
    },
  );

  test(
    'should return failure when sign in fails',
    () async {
      // arrange
      const failure = InvalidCredentialsFailure();
      when(() => mockAuthRepository.signInWithEmailAndPassword(
            email: any(named: 'email'),
            password: any(named: 'password'),
          )).thenAnswer((_) async => const Left(failure));

      // act
      final result = await usecase(const SignInWithEmailParams(
        email: testEmail,
        password: testPassword,
      ));

      // assert
      expect(result, const Left(failure));
      verify(() => mockAuthRepository.signInWithEmailAndPassword(
            email: testEmail,
            password: testPassword,
          ));
      verifyNoMoreInteractions(mockAuthRepository);
    },
  );
}
