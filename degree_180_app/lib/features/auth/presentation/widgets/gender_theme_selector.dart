import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/enums/gender.dart';
import '../../../../core/theme/theme_manager.dart';
import '../../../../core/theme/female_colors.dart';

class GenderThemeSelector extends StatefulWidget {
  final Gender? selectedGender;
  final Function(Gender) onGenderChanged;
  final bool showTitle;
  final bool isCompact;

  const GenderThemeSelector({
    super.key,
    this.selectedGender,
    required this.onGenderChanged,
    this.showTitle = true,
    this.isCompact = false,
  });

  @override
  State<GenderThemeSelector> createState() => _GenderThemeSelectorState();
}

class _GenderThemeSelectorState extends State<GenderThemeSelector>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  Gender? _selectedGender;

  @override
  void initState() {
    super.initState();
    _selectedGender = widget.selectedGender;
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _selectGender(Gender gender) {
    setState(() {
      _selectedGender = gender;
    });
    
    // Update theme
    ThemeManager.setGender(gender);
    
    // Trigger animation
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    
    // Notify parent
    widget.onGenderChanged(gender);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isCompact) {
      return _buildCompactSelector();
    }
    
    return _buildFullSelector();
  }

  Widget _buildFullSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) ...[
          Text(
            AppStrings.gender,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'اختر الثيم المناسب لك',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppColors.textGray,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
        ],
        
        Row(
          children: [
            Expanded(
              child: _buildGenderCard(
                gender: Gender.male,
                title: AppStrings.male,
                subtitle: 'ثيم بنفسجي أنيق',
                icon: Icons.male,
                primaryColor: AppColors.primaryPurple,
                accentColor: AppColors.primaryPurpleLight,
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primaryPurple,
                    AppColors.primaryPurpleLight,
                  ],
                ),
              ),
            ),
            
            const SizedBox(width: AppConstants.defaultPadding),
            
            Expanded(
              child: _buildGenderCard(
                gender: Gender.female,
                title: AppStrings.female,
                subtitle: 'ثيم وردي جميل',
                icon: Icons.female,
                primaryColor: FemaleColors.primaryPink,
                accentColor: FemaleColors.roseGold,
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    FemaleColors.primaryPink,
                    FemaleColors.roseGold,
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompactSelector() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildCompactGenderButton(
          gender: Gender.male,
          icon: Icons.male,
          color: AppColors.primaryPurple,
        ),
        const SizedBox(width: AppConstants.smallPadding),
        _buildCompactGenderButton(
          gender: Gender.female,
          icon: Icons.female,
          color: FemaleColors.primaryPink,
        ),
      ],
    );
  }

  Widget _buildGenderCard({
    required Gender gender,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color primaryColor,
    required Color accentColor,
    required LinearGradient gradient,
  }) {
    final isSelected = _selectedGender == gender;
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isSelected && _animationController.isAnimating 
              ? _scaleAnimation.value 
              : 1.0,
          child: GestureDetector(
            onTap: () => _selectGender(gender),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                gradient: isSelected ? gradient : null,
                color: isSelected ? null : Colors.white,
                borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                border: Border.all(
                  color: isSelected ? Colors.transparent : AppColors.borderGray,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isSelected 
                        ? primaryColor.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.1),
                    blurRadius: isSelected ? 15 : 5,
                    spreadRadius: isSelected ? 2 : 0,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Icon
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? Colors.white.withValues(alpha: 0.2)
                          : primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Icon(
                      icon,
                      size: 30,
                      color: isSelected ? Colors.white : primaryColor,
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.smallPadding),
                  
                  // Title
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : AppColors.textDark,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Subtitle
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: isSelected 
                          ? Colors.white.withValues(alpha: 0.9)
                          : AppColors.textGray,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: AppConstants.smallPadding),
                  
                  // Selection indicator
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: isSelected ? 20 : 0,
                    height: 3,
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.white : Colors.transparent,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompactGenderButton({
    required Gender gender,
    required IconData icon,
    required Color color,
  }) {
    final isSelected = _selectedGender == gender;
    
    return GestureDetector(
      onTap: () => _selectGender(gender),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.white,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: color,
            width: 2,
          ),
          boxShadow: [
            if (isSelected)
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: 10,
                spreadRadius: 2,
              ),
          ],
        ),
        child: Icon(
          icon,
          size: 24,
          color: isSelected ? Colors.white : color,
        ),
      ),
    );
  }
}

// Theme Preview Widget
class ThemePreview extends StatelessWidget {
  final Gender gender;
  final bool isSelected;

  const ThemePreview({
    super.key,
    required this.gender,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final colors = gender == Gender.male 
        ? ThemeManager.maleColors 
        : ThemeManager.femaleColors;
    
    return Container(
      width: 100,
      height: 80,
      decoration: BoxDecoration(
        gradient: colors.gradient,
        borderRadius: BorderRadius.circular(8),
        border: isSelected 
            ? Border.all(color: Colors.white, width: 3)
            : null,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            gender == Gender.male ? Icons.male : Icons.female,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            gender == Gender.male ? AppStrings.male : AppStrings.female,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
