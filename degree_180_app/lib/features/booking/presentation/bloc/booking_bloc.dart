import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/models/booking_model.dart' hide CreateBookingParams;
import '../../../../core/models/booking_statistics.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/enums/booking_status.dart';
import '../../domain/usecases/get_available_time_slots_usecase.dart';
import '../../domain/usecases/create_advanced_booking_usecase.dart';
import '../../domain/usecases/reschedule_booking_usecase.dart';
import '../../domain/usecases/get_booking_statistics_usecase.dart';
import '../../domain/repositories/booking_repository.dart';

class BookingBloc extends Bloc<BookingEvent, BookingState> {
  final GetAvailableTimeSlotsUseCase getAvailableTimeSlotsUseCase;
  final CreateAdvancedBookingUseCase createAdvancedBookingUseCase;
  final RescheduleBookingUseCase rescheduleBookingUseCase;
  final GetBookingStatisticsUseCase getBookingStatisticsUseCase;

  BookingBloc({
    required this.getAvailableTimeSlotsUseCase,
    required this.createAdvancedBookingUseCase,
    required this.rescheduleBookingUseCase,
    required this.getBookingStatisticsUseCase,
  }) : super(BookingInitial()) {
    on<LoadAvailableTimeSlotsEvent>(_onLoadAvailableTimeSlots);
    on<CreateBookingEvent>(_onCreateBooking);
    on<RescheduleBookingEvent>(_onRescheduleBooking);
    on<LoadBookingStatisticsEvent>(_onLoadBookingStatistics);
  }

  Future<void> _onLoadAvailableTimeSlots(
    LoadAvailableTimeSlotsEvent event,
    Emitter<BookingState> emit,
  ) async {
    emit(BookingTimeSlotsLoading());

    final result = await getAvailableTimeSlotsUseCase(
      GetAvailableTimeSlotsParams(
        providerId: event.providerId,
        date: event.date,
        serviceDurationMinutes: event.serviceDurationMinutes,
      ),
    );

    result.fold(
      (failure) => emit(BookingError(message: failure.message)),
      (timeSlots) => emit(BookingTimeSlotsLoaded(timeSlots: timeSlots)),
    );
  }

  Future<void> _onCreateBooking(
    CreateBookingEvent event,
    Emitter<BookingState> emit,
  ) async {
    emit(BookingCreating());

    final result = await createAdvancedBookingUseCase(event.params);

    result.fold(
      (failure) => emit(BookingError(message: failure.message)),
      (booking) => emit(BookingCreated(booking: booking)),
    );
  }

  Future<void> _onRescheduleBooking(
    RescheduleBookingEvent event,
    Emitter<BookingState> emit,
  ) async {
    emit(BookingRescheduling());

    final result = await rescheduleBookingUseCase(
      RescheduleBookingParams(
        bookingId: event.bookingId,
        newDate: event.newDate,
        newTimeSlot: event.newTimeSlot,
      ),
    );

    result.fold(
      (failure) => emit(BookingError(message: failure.message)),
      (booking) => emit(BookingRescheduled(booking: booking)),
    );
  }

  Future<void> _onLoadBookingStatistics(
    LoadBookingStatisticsEvent event,
    Emitter<BookingState> emit,
  ) async {
    emit(BookingStatisticsLoading());

    final result = await getBookingStatisticsUseCase(
      GetBookingStatisticsParams(
        providerId: event.providerId,
        startDate: event.startDate,
        endDate: event.endDate,
      ),
    );

    result.fold(
      (failure) => emit(BookingError(message: failure.message)),
      (statistics) => emit(BookingStatisticsLoaded(statistics: statistics)),
    );
  }
}

// Events
abstract class BookingEvent extends Equatable {
  const BookingEvent();

  @override
  List<Object?> get props => [];
}

class LoadAvailableTimeSlotsEvent extends BookingEvent {
  final String providerId;
  final DateTime date;
  final int serviceDurationMinutes;

  const LoadAvailableTimeSlotsEvent({
    required this.providerId,
    required this.date,
    required this.serviceDurationMinutes,
  });

  @override
  List<Object> get props => [providerId, date, serviceDurationMinutes];
}

class CreateBookingEvent extends BookingEvent {
  final CreateAdvancedBookingParams params;

  const CreateBookingEvent({required this.params});

  @override
  List<Object> get props => [params];
}

class RescheduleBookingEvent extends BookingEvent {
  final String bookingId;
  final DateTime newDate;
  final String newTimeSlot;

  const RescheduleBookingEvent({
    required this.bookingId,
    required this.newDate,
    required this.newTimeSlot,
  });

  @override
  List<Object> get props => [bookingId, newDate, newTimeSlot];
}

class LoadBookingStatisticsEvent extends BookingEvent {
  final String providerId;
  final DateTime? startDate;
  final DateTime? endDate;

  const LoadBookingStatisticsEvent({
    required this.providerId,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [providerId, startDate, endDate];
}

// States
abstract class BookingState extends Equatable {
  const BookingState();

  @override
  List<Object?> get props => [];
}

class BookingInitial extends BookingState {}

class BookingTimeSlotsLoading extends BookingState {}

class BookingTimeSlotsLoaded extends BookingState {
  final List<TimeSlotModel> timeSlots;

  const BookingTimeSlotsLoaded({required this.timeSlots});

  @override
  List<Object> get props => [timeSlots];
}

class BookingCreating extends BookingState {}

class BookingCreated extends BookingState {
  final BookingModel booking;

  const BookingCreated({required this.booking});

  @override
  List<Object> get props => [booking];
}

class BookingRescheduling extends BookingState {}

class BookingRescheduled extends BookingState {
  final BookingModel booking;

  const BookingRescheduled({required this.booking});

  @override
  List<Object> get props => [booking];
}

class BookingStatisticsLoading extends BookingState {}

class BookingStatisticsLoaded extends BookingState {
  final BookingStatistics statistics;

  const BookingStatisticsLoaded({required this.statistics});

  @override
  List<Object> get props => [statistics];
}

class BookingError extends BookingState {
  final String message;

  const BookingError({required this.message});

  @override
  List<Object> get props => [message];
}
